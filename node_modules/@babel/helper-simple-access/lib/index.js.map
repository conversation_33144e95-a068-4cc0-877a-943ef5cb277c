{"version": 3, "names": ["_t", "require", "LOGICAL_OPERATORS", "assignmentExpression", "binaryExpression", "cloneNode", "identifier", "logicalExpression", "numericLiteral", "sequenceExpression", "unaryExpression", "simpleAssignmentVisitor", "AssignmentExpression", "exit", "path", "scope", "seen", "bindingNames", "node", "operator", "has", "add", "left", "get", "isIdentifier", "localName", "name", "getBinding", "slice", "includes", "replaceWith", "right", "UpdateExpression", "includeUpdateExpression", "arg", "parentPath", "isExpressionStatement", "isCompletionRecord", "prefix", "old", "generateUidIdentifierBasedOnNode", "varName", "push", "id", "binary", "simplifyAccess", "_arguments$", "traverse", "WeakSet", "arguments"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  LOGICAL_OPERATORS,\n  assignmentExpression,\n  binaryExpression,\n  cloneNode,\n  identifier,\n  logicalExpression,\n  numericLiteral,\n  sequenceExpression,\n  unaryExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { <PERSON>de<PERSON><PERSON>, <PERSON>ope, Visitor } from \"@babel/traverse\";\n\ntype State = {\n  scope: Scope;\n  bindingNames: Set<string>;\n  seen: WeakSet<t.Node>;\n};\n\nconst simpleAssignmentVisitor: Visitor<State> = {\n  AssignmentExpression: {\n    exit(path) {\n      const { scope, seen, bindingNames } = this;\n\n      if (path.node.operator === \"=\") return;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n      if (!left.isIdentifier()) return;\n\n      // Simple update-assign foo += 1;\n      // =>   exports.foo =  (foo += 1);\n      const localName = left.node.name;\n\n      if (!bindingNames.has(localName)) return;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const operator = path.node.operator.slice(0, -1);\n      if (LOGICAL_OPERATORS.includes(operator)) {\n        // &&, ||, ??\n        // (foo &&= bar) => (foo && foo = bar)\n        path.replaceWith(\n          logicalExpression(\n            // @ts-expect-error Guarded by LOGICAL_OPERATORS.includes\n            operator,\n            path.node.left,\n            assignmentExpression(\n              \"=\",\n              cloneNode(path.node.left),\n              path.node.right,\n            ),\n          ),\n        );\n      } else {\n        // (foo += bar) => (foo = foo + bar)\n        path.node.right = binaryExpression(\n          // @ts-expect-error An assignment expression operator removing \"=\" must\n          // be a valid binary operator\n          operator,\n          cloneNode(path.node.left),\n          path.node.right,\n        );\n        path.node.operator = \"=\";\n      }\n    },\n  },\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  simpleAssignmentVisitor.UpdateExpression = {\n    exit(path) {\n      // @ts-expect-error This is Babel7-only\n      if (!this.includeUpdateExpression) return;\n\n      const { scope, bindingNames } = this;\n\n      const arg = path.get(\"argument\");\n      if (!arg.isIdentifier()) return;\n      const localName = arg.node.name;\n\n      if (!bindingNames.has(localName)) return;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      if (\n        path.parentPath.isExpressionStatement() &&\n        !path.isCompletionRecord()\n      ) {\n        // ++i => (i += 1);\n        const operator = path.node.operator === \"++\" ? \"+=\" : \"-=\";\n        path.replaceWith(\n          assignmentExpression(operator, arg.node, numericLiteral(1)),\n        );\n      } else if (path.node.prefix) {\n        // ++i => (i = (+i) + 1);\n        path.replaceWith(\n          assignmentExpression(\n            \"=\",\n            identifier(localName),\n            binaryExpression(\n              path.node.operator[0] as \"+\" | \"-\",\n              unaryExpression(\"+\", arg.node),\n              numericLiteral(1),\n            ),\n          ),\n        );\n      } else {\n        const old = path.scope.generateUidIdentifierBasedOnNode(\n          arg.node,\n          \"old\",\n        );\n        const varName = old.name;\n        path.scope.push({ id: old });\n\n        const binary = binaryExpression(\n          path.node.operator[0] as \"+\" | \"-\",\n          identifier(varName),\n          // todo: support bigint\n          numericLiteral(1),\n        );\n\n        // i++ => (_old = (+i), i = _old + 1, _old)\n        path.replaceWith(\n          sequenceExpression([\n            assignmentExpression(\n              \"=\",\n              identifier(varName),\n              unaryExpression(\"+\", arg.node),\n            ),\n            assignmentExpression(\"=\", cloneNode(arg.node), binary),\n            identifier(varName),\n          ]),\n        );\n      }\n    },\n  };\n}\n\nexport default function simplifyAccess(\n  path: NodePath,\n  bindingNames: Set<string>,\n) {\n  if (process.env.BABEL_8_BREAKING) {\n    path.traverse(simpleAssignmentVisitor, {\n      scope: path.scope,\n      bindingNames,\n      seen: new WeakSet(),\n    });\n  } else {\n    path.traverse(simpleAssignmentVisitor, {\n      scope: path.scope,\n      bindingNames,\n      seen: new WeakSet(),\n      // @ts-expect-error This is Babel7-only\n      includeUpdateExpression: arguments[2] ?? true,\n    });\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAUsB;EATpBC,iBAAiB;EACjBC,oBAAoB;EACpBC,gBAAgB;EAChBC,SAAS;EACTC,UAAU;EACVC,iBAAiB;EACjBC,cAAc;EACdC,kBAAkB;EAClBC;AAAe,IAAAV,EAAA;AAWjB,MAAMW,uBAAuC,GAAG;EAC9CC,oBAAoB,EAAE;IACpBC,IAAIA,CAACC,IAAI,EAAE;MACT,MAAM;QAAEC,KAAK;QAAEC,IAAI;QAAEC;MAAa,CAAC,GAAG,IAAI;MAE1C,IAAIH,IAAI,CAACI,IAAI,CAACC,QAAQ,KAAK,GAAG,EAAE;MAEhC,IAAIH,IAAI,CAACI,GAAG,CAACN,IAAI,CAACI,IAAI,CAAC,EAAE;MACzBF,IAAI,CAACK,GAAG,CAACP,IAAI,CAACI,IAAI,CAAC;MAEnB,MAAMI,IAAI,GAAGR,IAAI,CAACS,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAI,CAACD,IAAI,CAACE,YAAY,CAAC,CAAC,EAAE;MAI1B,MAAMC,SAAS,GAAGH,IAAI,CAACJ,IAAI,CAACQ,IAAI;MAEhC,IAAI,CAACT,YAAY,CAACG,GAAG,CAACK,SAAS,CAAC,EAAE;MAGlC,IAAIV,KAAK,CAACY,UAAU,CAACF,SAAS,CAAC,KAAKX,IAAI,CAACC,KAAK,CAACY,UAAU,CAACF,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMN,QAAQ,GAAGL,IAAI,CAACI,IAAI,CAACC,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChD,IAAI1B,iBAAiB,CAAC2B,QAAQ,CAACV,QAAQ,CAAC,EAAE;QAGxCL,IAAI,CAACgB,WAAW,CACdvB,iBAAiB,CAEfY,QAAQ,EACRL,IAAI,CAACI,IAAI,CAACI,IAAI,EACdnB,oBAAoB,CAClB,GAAG,EACHE,SAAS,CAACS,IAAI,CAACI,IAAI,CAACI,IAAI,CAAC,EACzBR,IAAI,CAACI,IAAI,CAACa,KACZ,CACF,CACF,CAAC;MACH,CAAC,MAAM;QAELjB,IAAI,CAACI,IAAI,CAACa,KAAK,GAAG3B,gBAAgB,CAGhCe,QAAQ,EACRd,SAAS,CAACS,IAAI,CAACI,IAAI,CAACI,IAAI,CAAC,EACzBR,IAAI,CAACI,IAAI,CAACa,KACZ,CAAC;QACDjB,IAAI,CAACI,IAAI,CAACC,QAAQ,GAAG,GAAG;MAC1B;IACF;EACF;AACF,CAAC;AAEkC;EACjCR,uBAAuB,CAACqB,gBAAgB,GAAG;IACzCnB,IAAIA,CAACC,IAAI,EAAE;MAET,IAAI,CAAC,IAAI,CAACmB,uBAAuB,EAAE;MAEnC,MAAM;QAAElB,KAAK;QAAEE;MAAa,CAAC,GAAG,IAAI;MAEpC,MAAMiB,GAAG,GAAGpB,IAAI,CAACS,GAAG,CAAC,UAAU,CAAC;MAChC,IAAI,CAACW,GAAG,CAACV,YAAY,CAAC,CAAC,EAAE;MACzB,MAAMC,SAAS,GAAGS,GAAG,CAAChB,IAAI,CAACQ,IAAI;MAE/B,IAAI,CAACT,YAAY,CAACG,GAAG,CAACK,SAAS,CAAC,EAAE;MAGlC,IAAIV,KAAK,CAACY,UAAU,CAACF,SAAS,CAAC,KAAKX,IAAI,CAACC,KAAK,CAACY,UAAU,CAACF,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,IACEX,IAAI,CAACqB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IACvC,CAACtB,IAAI,CAACuB,kBAAkB,CAAC,CAAC,EAC1B;QAEA,MAAMlB,QAAQ,GAAGL,IAAI,CAACI,IAAI,CAACC,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;QAC1DL,IAAI,CAACgB,WAAW,CACd3B,oBAAoB,CAACgB,QAAQ,EAAEe,GAAG,CAAChB,IAAI,EAAEV,cAAc,CAAC,CAAC,CAAC,CAC5D,CAAC;MACH,CAAC,MAAM,IAAIM,IAAI,CAACI,IAAI,CAACoB,MAAM,EAAE;QAE3BxB,IAAI,CAACgB,WAAW,CACd3B,oBAAoB,CAClB,GAAG,EACHG,UAAU,CAACmB,SAAS,CAAC,EACrBrB,gBAAgB,CACdU,IAAI,CAACI,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,EACrBT,eAAe,CAAC,GAAG,EAAEwB,GAAG,CAAChB,IAAI,CAAC,EAC9BV,cAAc,CAAC,CAAC,CAClB,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACL,MAAM+B,GAAG,GAAGzB,IAAI,CAACC,KAAK,CAACyB,gCAAgC,CACrDN,GAAG,CAAChB,IAAI,EACR,KACF,CAAC;QACD,MAAMuB,OAAO,GAAGF,GAAG,CAACb,IAAI;QACxBZ,IAAI,CAACC,KAAK,CAAC2B,IAAI,CAAC;UAAEC,EAAE,EAAEJ;QAAI,CAAC,CAAC;QAE5B,MAAMK,MAAM,GAAGxC,gBAAgB,CAC7BU,IAAI,CAACI,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,EACrBb,UAAU,CAACmC,OAAO,CAAC,EAEnBjC,cAAc,CAAC,CAAC,CAClB,CAAC;QAGDM,IAAI,CAACgB,WAAW,CACdrB,kBAAkB,CAAC,CACjBN,oBAAoB,CAClB,GAAG,EACHG,UAAU,CAACmC,OAAO,CAAC,EACnB/B,eAAe,CAAC,GAAG,EAAEwB,GAAG,CAAChB,IAAI,CAC/B,CAAC,EACDf,oBAAoB,CAAC,GAAG,EAAEE,SAAS,CAAC6B,GAAG,CAAChB,IAAI,CAAC,EAAE0B,MAAM,CAAC,EACtDtC,UAAU,CAACmC,OAAO,CAAC,CACpB,CACH,CAAC;MACH;IACF;EACF,CAAC;AACH;AAEe,SAASI,cAAcA,CACpC/B,IAAc,EACdG,YAAyB,EACzB;EAOO;IAAA,IAAA6B,WAAA;IACLhC,IAAI,CAACiC,QAAQ,CAACpC,uBAAuB,EAAE;MACrCI,KAAK,EAAED,IAAI,CAACC,KAAK;MACjBE,YAAY;MACZD,IAAI,EAAE,IAAIgC,OAAO,CAAC,CAAC;MAEnBf,uBAAuB,GAAAa,WAAA,GAAEG,SAAS,CAAC,CAAC,CAAC,YAAAH,WAAA,GAAI;IAC3C,CAAC,CAAC;EACJ;AACF", "ignoreList": []}