"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _superPropertyGet;
var _get = require("./get.js");
var _getPrototypeOf = require("./getPrototypeOf.js");
function _superPropertyGet(classArg, property, receiver, flags) {
  var result = (0, _get.default)((0, _getPrototypeOf.default)(flags & 1 ? classArg.prototype : classArg), property, receiver);
  return flags & 2 && typeof result === "function" ? function (args) {
    return result.apply(receiver, args);
  } : result;
}

//# sourceMappingURL=superPropGet.js.map
