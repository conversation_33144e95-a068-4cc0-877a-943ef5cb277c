/*!
 * 
 *               jsPDF AutoTable plugin v5.0.2
 *
 *               Copyright (c) 2025 <PERSON>, https://github.com/simonben<PERSON><PERSON>/jsPDF-AutoTable
 *               Licensed under the MIT License.
 *               http://opensource.org/licenses/mit-license
 *
 */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n=e();for(var o in n)("object"==typeof exports?exports:t)[o]=n[o]}}("undefined"!=typeof globalThis?globalThis:void 0!==this?this:"undefined"!=typeof window?window:"undefined"!=typeof self?self:global,(function(){return function(){"use strict";var t={28:function(t,e){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlRowInput=void 0,e.defaultStyles=function(t){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/t,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}},e.getTheme=function(t){return{striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}}[t]};var i=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return o(e,t),e}(Array);e.HtmlRowInput=i},150:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,n,o,i){o=o||{};var r=i.internal.scaleFactor,l=i.internal.getFontSize()/r,a=i.getLineHeightFactor?i.getLineHeightFactor():1.15,s=l*a,u="",h=1;"middle"!==o.valign&&"bottom"!==o.valign&&"center"!==o.halign&&"right"!==o.halign||(h=(u="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1);n+=l*(2-1.15),"middle"===o.valign?n-=h/2*s:"bottom"===o.valign&&(n-=h*s);if("center"===o.halign||"right"===o.halign){var d=l;if("center"===o.halign&&(d*=.5),u&&h>=1){for(var c=0;c<u.length;c++)i.text(u[c],e-i.getStringUnitWidth(u[c])*d,n),n+=s;return i}e-=i.getStringUnitWidth(t)*d}"justify"===o.halign?i.text(t,e,n,{maxWidth:o.maxWidth||100,align:"justify"}):i.text(t,e,n);return i}},152:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.calculateWidths=function(t,e){!function(t,e){var n=t.scaleFactor(),i=e.settings.horizontalPageBreak,r=(0,o.getPageAvailableWidth)(t,e);e.allRows().forEach((function(l){for(var a=0,s=e.columns;a<s.length;a++){var u=s[a],h=l.cells[u.index];if(h){var d=e.hooks.didParseCell;e.callCellHooks(t,d,h,l,u,null);var c=h.padding("horizontal");h.contentWidth=(0,o.getStringWidth)(h.text,h.styles,t)+c;var f=(0,o.getStringWidth)(h.text.join(" ").split(/[^\S\u00A0]+/),h.styles,t);if(h.minReadableWidth=f+h.padding("horizontal"),"number"==typeof h.styles.cellWidth)h.minWidth=h.styles.cellWidth,h.wrappedWidth=h.styles.cellWidth;else if("wrap"===h.styles.cellWidth||!0===i)h.contentWidth>r?(h.minWidth=r,h.wrappedWidth=r):(h.minWidth=h.contentWidth,h.wrappedWidth=h.contentWidth);else{var g=10/n;h.minWidth=h.styles.minCellWidth||g,h.wrappedWidth=h.contentWidth,h.minWidth>h.wrappedWidth&&(h.wrappedWidth=h.minWidth)}}}})),e.allRows().forEach((function(t){for(var n=0,o=e.columns;n<o.length;n++){var i=o[n],r=t.cells[i.index];if(r&&1===r.colSpan)i.wrappedWidth=Math.max(i.wrappedWidth,r.wrappedWidth),i.minWidth=Math.max(i.minWidth,r.minWidth),i.minReadableWidth=Math.max(i.minReadableWidth,r.minReadableWidth);else{var l=e.styles.columnStyles[i.dataKey]||e.styles.columnStyles[i.index]||{},a=l.cellWidth||l.minCellWidth;a&&"number"==typeof a&&(i.minWidth=a,i.wrappedWidth=a)}r&&(r.colSpan>1&&!i.minWidth&&(i.minWidth=r.minWidth),r.colSpan>1&&!i.wrappedWidth&&(i.wrappedWidth=r.minWidth))}}))}(t,e);var n=[],l=0;e.columns.forEach((function(t){var o=t.getMaxCustomCellWidth(e);o?t.width=o:(t.width=t.wrappedWidth,n.push(t)),l+=t.width}));var a=e.getWidth(t.pageSize().width)-l;a&&(a=i(n,a,(function(t){return Math.max(t.minReadableWidth,t.minWidth)})));a&&(a=i(n,a,(function(t){return t.minWidth})));a=Math.abs(a),!e.settings.horizontalPageBreak&&a>.1/t.scaleFactor()&&(a=a<1?a:Math.round(a),console.warn("Of the table content, ".concat(a," units width could not fit page")));(function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var o=e[n],i=null,r=0,l=0,a=0;a<t.columns.length;a++){var s=t.columns[a];if((l-=1)>1&&t.columns[a+1])r+=s.width,delete o.cells[s.index];else if(i){var u=i;delete o.cells[s.index],i=null,u.width=s.width+r}else{if(!(u=o.cells[s.index]))continue;if(l=u.colSpan,r=0,u.colSpan>1){i=u,r+=s.width;continue}u.width=s.width+r}}})(e),function(t,e){for(var n={count:0,height:0},o=0,i=t.allRows();o<i.length;o++){for(var l=i[o],a=0,s=t.columns;a<s.length;a++){var u=s[a],h=l.cells[u.index];if(h){e.applyStyles(h.styles,!0);var d=h.width-h.padding("horizontal");if("linebreak"===h.styles.overflow)h.text=e.splitTextToSize(h.text,d+1/e.scaleFactor(),{fontSize:h.styles.fontSize});else if("ellipsize"===h.styles.overflow)h.text=r(h.text,d,h.styles,e,"...");else if("hidden"===h.styles.overflow)h.text=r(h.text,d,h.styles,e,"");else if("function"==typeof h.styles.overflow){var c=h.styles.overflow(h.text,d);h.text="string"==typeof c?[c]:c}h.contentHeight=h.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var f=h.contentHeight/h.rowSpan;h.rowSpan>1&&n.count*n.height<f*h.rowSpan?n={height:f,count:h.rowSpan}:n&&n.count>0&&n.height>f&&(f=n.height),f>l.height&&(l.height=f)}}n.count--}}(e,t),function(t){for(var e={},n=1,o=t.allRows(),i=0;i<o.length;i++)for(var r=o[i],l=0,a=t.columns;l<a.length;l++){var s=a[l],u=e[s.index];if(n>1)n--,delete r.cells[s.index];else if(u)u.cell.height+=r.height,n=u.cell.colSpan,delete r.cells[s.index],u.left--,u.left<=1&&delete e[s.index];else{var h=r.cells[s.index];if(!h)continue;if(h.height=r.height,h.rowSpan>1){var d=o.length-i,c=h.rowSpan>d?d:h.rowSpan;e[s.index]={cell:h,left:c,row:r}}}}}(e)},e.resizeColumns=i,e.ellipsize=r;var o=n(799);function i(t,e,n){for(var o=e,r=t.reduce((function(t,e){return t+e.wrappedWidth}),0),l=0;l<t.length;l++){var a=t[l],s=o*(a.wrappedWidth/r),u=a.width+s,h=n(a),d=u<h?h:u;e-=d-a.width,a.width=d}if(e=Math.round(1e10*e)/1e10){var c=t.filter((function(t){return!(e<0)||t.width>n(t)}));c.length&&(e=i(c,e,n))}return e}function r(t,e,n,i,r){return t.map((function(t){return function(t,e,n,i,r){var l=1e4*i.scaleFactor();if(e=Math.ceil(e*l)/l,e>=(0,o.getStringWidth)(t,n,i))return t;for(;e<(0,o.getStringWidth)(t+r,n,i)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+r}(t,e,n,i,r)}))}},176:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.assign=function(t,e,n,o,i){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var r=Object(t),l=1;l<arguments.length;l++){var a=arguments[l];if(null!=a)for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(r[s]=a[s])}return r}},344:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.validateInput=function(t,e,n){for(var o=0,i=[t,e,n];o<i.length;o++){var r=i[o];r&&"object"!=typeof r&&console.error("The options parameter should be of type object, is: "+typeof r),r.startY&&"number"!=typeof r.startY&&(console.error("Invalid value for startY option",r.startY),delete r.startY)}}},371:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseInput=function(t,e){var n=new i.DocHandler(t),s=n.getDocumentOptions(),u=n.getGlobalOptions();(0,l.validateInput)(u,s,e);var h,d=(0,a.assign)({},u,s,e);"undefined"!=typeof window&&(h=window);var c=function(t,e,n){for(var o={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},i=function(i){if("columnStyles"===i){var r=t[i],l=e[i],s=n[i];o.columnStyles=(0,a.assign)({},r,l,s)}else{var u=[t,e,n].map((function(t){return t[i]||{}}));o[i]=(0,a.assign)({},u[0],u[1],u[2])}},r=0,l=Object.keys(o);r<l.length;r++){i(l[r])}return o}(u,s,e),f=function(t,e,n){for(var o={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},i=0,r=[t,e,n];i<r.length;i++){var l=r[i];l.didParseCell&&o.didParseCell.push(l.didParseCell),l.willDrawCell&&o.willDrawCell.push(l.willDrawCell),l.didDrawCell&&o.didDrawCell.push(l.didDrawCell),l.willDrawPage&&o.willDrawPage.push(l.willDrawPage),l.didDrawPage&&o.didDrawPage.push(l.didDrawPage)}return o}(u,s,e),g=function(t,e){var n,i,r,l,a,s,u,h,d,c,f,g,p,y,v=(0,o.parseSpacing)(e.margin,40/t.scaleFactor()),m=null!==(n=function(t,e){var n=t.getLastAutoTable(),o=t.scaleFactor(),i=t.pageNumber(),r=!1;if(n&&n.startPageNumber){r=n.startPageNumber+n.pageNumber-1===i}if("number"==typeof e)return e;if((null==e||!1===e)&&r&&null!=(null==n?void 0:n.finalY))return n.finalY+20/o;return null}(t,e.startY))&&void 0!==n?n:v.top;p=!0===e.showFoot?"everyPage":!1===e.showFoot?"never":null!==(i=e.showFoot)&&void 0!==i?i:"everyPage";y=!0===e.showHead?"everyPage":!1===e.showHead?"never":null!==(r=e.showHead)&&void 0!==r?r:"everyPage";var b=null!==(l=e.useCss)&&void 0!==l&&l,w=e.theme||(b?"plain":"striped"),x=!!e.horizontalPageBreak,S=null!==(a=e.horizontalPageBreakRepeat)&&void 0!==a?a:null;return{includeHiddenHtml:null!==(s=e.includeHiddenHtml)&&void 0!==s&&s,useCss:b,theme:w,startY:m,margin:v,pageBreak:null!==(u=e.pageBreak)&&void 0!==u?u:"auto",rowPageBreak:null!==(h=e.rowPageBreak)&&void 0!==h?h:"auto",tableWidth:null!==(d=e.tableWidth)&&void 0!==d?d:"auto",showHead:y,showFoot:p,tableLineWidth:null!==(c=e.tableLineWidth)&&void 0!==c?c:0,tableLineColor:null!==(f=e.tableLineColor)&&void 0!==f?f:200,horizontalPageBreak:x,horizontalPageBreakRepeat:S,horizontalPageBreakBehaviour:null!==(g=e.horizontalPageBreakBehaviour)&&void 0!==g?g:"afterAllRows"}}(n,d),p=function(t,e,n){var o=e.head||[],i=e.body||[],l=e.foot||[];if(e.html){var a=e.includeHiddenHtml;if(n){var s=(0,r.parseHtml)(t,e.html,n,a,e.useCss)||{};o=s.head||o,i=s.body||o,l=s.foot||o}else console.error("Cannot parse html in non browser environment")}var u=e.columns||function(t,e,n){var o=t[0]||e[0]||n[0]||[],i=[];return Object.keys(o).filter((function(t){return"_element"!==t})).forEach((function(t){var e,n=1;"object"!=typeof(e=Array.isArray(o)?o[parseInt(t)]:o[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var r=0;r<n;r++){var l={dataKey:Array.isArray(o)?i.length:t+(r>0?"_".concat(r):"")};i.push(l)}})),i}(o,i,l);return{columns:u,head:o,body:i,foot:l}}(n,d,h);return{id:e.tableId,content:p,hooks:f,styles:c,settings:g}};var o=n(799),i=n(643),r=n(660),l=n(344),a=n(176)},376:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.createTable=function(t,e){var n=new i.DocHandler(t),o=function(t,e){var n=t.content,o=function(t){return t.map((function(t,e){var n,o;return o="object"==typeof t&&null!==(n=t.dataKey)&&void 0!==n?n:e,new r.Column(o,t,e)}))}(n.columns);if(0===n.head.length){(i=u(o,"head"))&&n.head.push(i)}if(0===n.foot.length){var i;(i=u(o,"foot"))&&n.foot.push(i)}var l=t.settings.theme,a=t.styles;return{columns:o,head:s("head",n.head,o,a,l,e),body:s("body",n.body,o,a,l,e),foot:s("foot",n.foot,o,a,l,e)}}(e,n.scaleFactor()),l=new r.Table(e,o);return(0,a.calculateWidths)(n,l),n.applyStyles(n.userStyles),l};var o=n(28),i=n(643),r=n(524),l=n(176),a=n(152);function s(t,e,n,o,i,l){var a={};return e.map((function(e,s){for(var u=0,d={},c=0,f=0,g=0,p=n;g<p.length;g++){var y=p[g];if(null==a[y.index]||0===a[y.index].left)if(0===f){var v=void 0,m={};"object"!=typeof(v=Array.isArray(e)?e[y.index-c-u]:e[y.dataKey])||Array.isArray(v)||(m=(null==v?void 0:v.styles)||{});var b=h(t,y,s,i,o,l,m),w=new r.Cell(v,b,t);d[y.dataKey]=w,d[y.index]=w,f=w.colSpan-1,a[y.index]={left:w.rowSpan-1,times:f}}else f--,c++;else a[y.index].left--,f=a[y.index].times,u++}return new r.Row(e,s,t,d)}))}function u(t,e){var n={};return t.forEach((function(t){if(null!=t.raw){var o=function(t,e){if("head"===t){if("object"==typeof e)return e.header||null;if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=o&&(n[t.dataKey]=o)}})),Object.keys(n).length>0?n:null}function h(t,e,n,i,r,a,s){var u,h=(0,o.getTheme)(i);"head"===t?u=r.headStyles:"body"===t?u=r.bodyStyles:"foot"===t&&(u=r.footStyles);var d=(0,l.assign)({},h.table,h[t],r.styles,u),c=r.columnStyles[e.dataKey]||r.columnStyles[e.index]||{},f="body"===t?c:{},g="body"===t&&n%2==0?(0,l.assign)({},h.alternateRow,r.alternateRowStyles):{},p=(0,o.defaultStyles)(a),y=(0,l.assign)({},p,d,g,f);return(0,l.assign)(y,s)}},460:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseCss=function(t,e,n,r,l){var a={},s=96/72,u=i(e,(function(t){return l.getComputedStyle(t).backgroundColor}));null!=u&&(a.fillColor=u);var h=i(e,(function(t){return l.getComputedStyle(t).color}));null!=h&&(a.textColor=h);var d=function(t,e){var n=[t.paddingTop,t.paddingRight,t.paddingBottom,t.paddingLeft],i=96/(72/e),r=(parseInt(t.lineHeight)-parseInt(t.fontSize))/e/2,l=n.map((function(t){return parseInt(t||"0")/i})),a=(0,o.parseSpacing)(l,0);r>a.top&&(a.top=r);r>a.bottom&&(a.bottom=r);return a}(r,n);d&&(a.cellPadding=d);var c="borderTopColor",f=s*n,g=r.borderTopWidth;if(r.borderBottomWidth===g&&r.borderRightWidth===g&&r.borderLeftWidth===g){var p=(parseFloat(g)||0)/f;p&&(a.lineWidth=p)}else a.lineWidth={top:(parseFloat(r.borderTopWidth)||0)/f,right:(parseFloat(r.borderRightWidth)||0)/f,bottom:(parseFloat(r.borderBottomWidth)||0)/f,left:(parseFloat(r.borderLeftWidth)||0)/f},a.lineWidth.top||(a.lineWidth.right?c="borderRightColor":a.lineWidth.bottom?c="borderBottomColor":a.lineWidth.left&&(c="borderLeftColor"));var y=i(e,(function(t){return l.getComputedStyle(t)[c]}));null!=y&&(a.lineColor=y);var v=["left","right","center","justify"];-1!==v.indexOf(r.textAlign)&&(a.halign=r.textAlign);-1!==(v=["middle","bottom","top"]).indexOf(r.verticalAlign)&&(a.valign=r.verticalAlign);var m=parseInt(r.fontSize||"");isNaN(m)||(a.fontSize=m/s);var b=function(t){var e="";("bold"===t.fontWeight||"bolder"===t.fontWeight||parseInt(t.fontWeight)>=700)&&(e="bold");"italic"!==t.fontStyle&&"oblique"!==t.fontStyle||(e+="italic");return e}(r);b&&(a.fontStyle=b);var w=(r.fontFamily||"").toLowerCase();-1!==t.indexOf(w)&&(a.font=w);return a};var o=n(799);function i(t,e){var n=r(t,e);if(!n)return null;var o=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!o||!Array.isArray(o))return null;var i=[parseInt(o[1]),parseInt(o[2]),parseInt(o[3])];return 0===parseInt(o[4])||isNaN(i[0])||isNaN(i[1])||isNaN(i[2])?null:i}function r(t,e){var n=e(t);return"rgba(0, 0, 0, 0)"===n||"transparent"===n||"initial"===n||"inherit"===n?null==t.parentElement?null:r(t.parentElement,e):n}},524:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.Column=e.Cell=e.Row=e.Table=void 0;var o=n(799),i=n(28),r=n(601),l=function(){function t(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce((function(e,n){return e+n.getMaxCellHeight(t)}),0)},t.prototype.getFootHeight=function(t){return this.foot.reduce((function(e,n){return e+n.getMaxCellHeight(t)}),0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,o,i,l){for(var a=0,s=e;a<s.length;a++){var u=!1===(0,s[a])(new r.CellHookData(t,this,n,o,i,l));if(n.text=Array.isArray(n.text)?n.text:[n.text],u)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,o=this.hooks.didDrawPage;n<o.length;n++){(0,o[n])(new r.HookData(t,this,e))}},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,o=this.hooks.willDrawPage;n<o.length;n++){(0,o[n])(new r.HookData(t,this,e))}},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce((function(t,e){return t+e.wrappedWidth}),0);var e=this.settings.margin;return t-e.left-e.right},t}();e.Table=l;var a=function(){function t(t,e,n,o,r){void 0===r&&(r=!1),this.height=0,this.raw=t,t instanceof i.HtmlRowInput&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=o,this.spansMultiplePages=r}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce((function(t,n){var o;return Math.max(t,(null===(o=e.cells[n.index])||void 0===o?void 0:o.height)||0)}),0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter((function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1})).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce((function(t,o){var i=n.cells[o.index];if(!i)return 0;var r=e.getLineHeight(i.styles.fontSize),l=i.padding("vertical")+r;return l>t?l:t}),0)},t}();e.Row=a;var s=function(){function t(t,e,n){var o;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var i=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,i=null!==(o=t.content)&&void 0!==o?o:t,t._element&&(this.raw=t._element));var r=null!=i?""+i:"";this.text=r.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){var t,e;if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var o=this.width-this.padding("horizontal");e=this.x+o/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){void 0===e&&(e=1.15);var n=(Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical");return Math.max(n,this.styles.minCellHeight)},t.prototype.padding=function(t){var e=(0,o.parseSpacing)(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}();e.Cell=s;var u=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,o=t.allRows();n<o.length;n++){var i=o[n].cells[this.index];i&&"number"==typeof i.styles.cellWidth&&(e=Math.max(e,i.styles.cellWidth))}return e},t}();e.Column=u},601:function(t,e){var n,o=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.CellHookData=e.HookData=void 0;var i=function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()};e.HookData=i;var r=function(t){function e(e,n,o,i,r,l){var a=t.call(this,e,n,l)||this;return a.cell=o,a.row=i,a.column=r,a.section=i.section,a}return o(e,t),e}(i);e.CellHookData=r},626:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.calculateAllColumnsCanFitInPage=function(t,e){for(var n=[],o=0;o<e.columns.length;o++){var r=i(t,e,{start:o});r.columns.length&&(n.push(r),o=r.lastIndex)}return n};var o=n(799);function i(t,e,n){var i;void 0===n&&(n={});var r=(0,o.getPageAvailableWidth)(t,e),l=new Map,a=[],s=[],u=[];Array.isArray(e.settings.horizontalPageBreakRepeat)?u=e.settings.horizontalPageBreakRepeat:"string"!=typeof e.settings.horizontalPageBreakRepeat&&"number"!=typeof e.settings.horizontalPageBreakRepeat||(u=[e.settings.horizontalPageBreakRepeat]),u.forEach((function(t){var n=e.columns.find((function(e){return e.dataKey===t||e.index===t}));n&&!l.has(n.index)&&(l.set(n.index,!0),a.push(n.index),s.push(e.columns[n.index]),r-=n.wrappedWidth)}));for(var h=!0,d=null!==(i=null==n?void 0:n.start)&&void 0!==i?i:0;d<e.columns.length;)if(l.has(d))d++;else{var c=e.columns[d].wrappedWidth;if(!(h||r>=c))break;h=!1,a.push(d),s.push(e.columns[d]),r-=c,d++}return{colIndexes:a,columns:s,lastIndex:d-1}}},639:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.applyPlugin=function(t){t.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],o=(0,l.parseInput)(this,n),i=(0,a.createTable)(this,o);return(0,s.drawTable)(this,i),this},t.API.lastAutoTable=!1,t.API.autoTableText=function(t,e,n,i){(0,o.default)(t,e,n,i,this)},t.API.autoTableSetDefaults=function(t){return i.DocHandler.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){i.DocHandler.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){var n;if(void 0===e&&(e=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var o=new i.DocHandler(this),l=(0,r.parseHtml)(o,t,window,e,!1),a=l.head,s=l.body;return{columns:(null===(n=a[0])||void 0===n?void 0:n.map((function(t){return t.content})))||[],rows:s,data:s}}};var o=n(150),i=n(643),r=n(660),l=n(371),a=n(376),s=n(789)},643:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.DocHandler=void 0;var n={},o=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:n=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){var o,i,r;void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var l=this.jsPDFDocument.internal.getFont(),a=l.fontStyle,s=l.fontName;if(e.font&&(s=e.font),e.fontStyle){a=e.fontStyle;var u=this.getFontList()[s];u&&-1===u.indexOf(a)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(u[0]),a=u[0])}if(this.jsPDFDocument.setFont(s,a),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var h=t.unifyColor(e.fillColor);h&&(o=this.jsPDFDocument).setFillColor.apply(o,h),(h=t.unifyColor(e.textColor))&&(i=this.jsPDFDocument).setTextColor.apply(i,h),(h=t.unifyColor(e.lineColor))&&(r=this.jsPDFDocument).setDrawColor.apply(r,h),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,o,i){return this.jsPDFDocument.rect(t,e,n,o,i)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return n||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}();e.DocHandler=o},660:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseHtml=function(t,e,n,o,i){var l,a,s;void 0===o&&(o=!1);void 0===i&&(i=!1);s="string"==typeof e?n.document.querySelector(e):e;var u=Object.keys(t.getFontList()),h=t.scaleFactor(),d=[],c=[],f=[];if(!s)return console.error("Html table could not be found with input: ",e),{head:d,body:c,foot:f};for(var g=0;g<s.rows.length;g++){var p=s.rows[g],y=null===(a=null===(l=null==p?void 0:p.parentElement)||void 0===l?void 0:l.tagName)||void 0===a?void 0:a.toLowerCase(),v=r(u,h,n,p,o,i);v&&("thead"===y?d.push(v):"tfoot"===y?f.push(v):c.push(v))}return{head:d,body:c,foot:f}};var o=n(28),i=n(460);function r(t,e,n,r,a,s){for(var u=new o.HtmlRowInput(r),h=0;h<r.cells.length;h++){var d=r.cells[h],c=n.getComputedStyle(d);if(a||"none"!==c.display){var f=void 0;s&&(f=(0,i.parseCss)(t,d,e,c,n)),u.push({rowSpan:d.rowSpan,colSpan:d.colSpan,styles:f,_element:d,content:l(d)})}}var g=n.getComputedStyle(r);if(u.length>0&&(a||"none"!==g.display))return u}function l(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map((function(t){return t.trim()})).join("\n"),e.innerText||e.textContent||""}},789:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.drawTable=function(t,e){var n=e.settings,o=n.startY,l=n.margin,c={x:l.left,y:o},p=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),y=o+l.bottom+p;if("avoid"===n.pageBreak){y+=e.body.reduce((function(t,e){return t+e.height}),0)}var b=new r.DocHandler(t);("always"===n.pageBreak||null!=n.startY&&y>b.pageSize().height)&&(m(b),c.y=l.top);e.callWillDrawPageHooks(b,c);var w=(0,a.assign)({},c);e.startPageNumber=b.pageNumber(),n.horizontalPageBreak?function(t,e,n,o){var i=(0,s.calculateAllColumnsCanFitInPage)(t,e),r=e.settings;if("afterAllRows"===r.horizontalPageBreakBehaviour)i.forEach((function(i,r){t.applyStyles(t.userStyles),r>0?v(t,e,n,o,i.columns,!0):u(t,e,o,i.columns),function(t,e,n,o,i){t.applyStyles(t.userStyles),e.body.forEach((function(r,l){var a=l===e.body.length-1;f(t,e,r,a,n,o,i)}))}(t,e,n,o,i.columns),d(t,e,o,i.columns)}));else for(var l=-1,a=i[0],c=function(){var r=l;if(a){t.applyStyles(t.userStyles);var s=a.columns;l>=0?v(t,e,n,o,s,!0):u(t,e,o,s),r=h(t,e,l+1,o,s),d(t,e,o,s)}var c=r-l;i.slice(1).forEach((function(i){t.applyStyles(t.userStyles),v(t,e,n,o,i.columns,!0),h(t,e,l+1,o,i.columns,c),d(t,e,o,i.columns)})),l=r};l<e.body.length-1;)c()}(b,e,w,c):(b.applyStyles(b.userStyles),"firstPage"!==n.showHead&&"everyPage"!==n.showHead||e.head.forEach((function(t){return g(b,e,t,c,e.columns)})),b.applyStyles(b.userStyles),e.body.forEach((function(t,n){var o=n===e.body.length-1;f(b,e,t,o,w,c,e.columns)})),b.applyStyles(b.userStyles),"lastPage"!==n.showFoot&&"everyPage"!==n.showFoot||e.foot.forEach((function(t){return g(b,e,t,c,e.columns)})));(0,i.addTableBorder)(b,e,w,c),e.callEndPageHooks(b,c),e.finalY=c.y,t.lastAutoTable=e,b.applyStyles(b.userStyles)},e.addPage=v;var o=n(150),i=n(799),r=n(643),l=n(524),a=n(176),s=n(626);function u(t,e,n,o){var i=e.settings;t.applyStyles(t.userStyles),"firstPage"!==i.showHead&&"everyPage"!==i.showHead||e.head.forEach((function(i){return g(t,e,i,n,o)}))}function h(t,e,n,o,i,r){t.applyStyles(t.userStyles),r=null!=r?r:e.body.length;var l=Math.min(n+r,e.body.length),a=-1;return e.body.slice(n,l).forEach((function(r,l){var s=n+l===e.body.length-1,u=y(t,e,s,o);r.canEntireRowFit(u,i)&&(g(t,e,r,o,i),a=n+l)})),a}function d(t,e,n,o){var i=e.settings;t.applyStyles(t.userStyles),"lastPage"!==i.showFoot&&"everyPage"!==i.showFoot||e.foot.forEach((function(i){return g(t,e,i,n,o)}))}function c(t,e,n){var o=n.getLineHeight(t.styles.fontSize),i=t.padding("vertical"),r=Math.floor((e-i)/o);return Math.max(0,r)}function f(t,e,n,o,i,r,s){var u=y(t,e,o,r);if(n.canEntireRowFit(u,s))g(t,e,n,r,s);else if(function(t,e,n,o){var i=t.pageSize().height,r=o.settings.margin,l=i-(r.top+r.bottom);"body"===e.section&&(l-=o.getHeadHeight(o.columns)+o.getFootHeight(o.columns));var a=e.getMinimumRowHeight(o.columns,t),s=a<n;if(a>l)return console.error("Will not be able to print row ".concat(e.index," correctly since it's minimum height is larger than page height")),!0;if(!s)return!1;var u=e.hasRowSpan(o.columns);return e.getMaxCellHeight(o.columns)>l?(u&&console.error("The content of row ".concat(e.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!u&&"avoid"!==o.settings.rowPageBreak}(t,n,u,e)){var h=function(t,e,n,o){var i={};t.spansMultiplePages=!0,t.height=0;for(var r=0,s=0,u=n.columns;s<u.length;s++){var h=u[s];if(b=t.cells[h.index]){Array.isArray(b.text)||(b.text=[b.text]);var d=new l.Cell(b.raw,b.styles,b.section);(d=(0,a.assign)(d,b)).text=[];var f=c(b,e,o);b.text.length>f&&(d.text=b.text.splice(f,b.text.length));var g=o.scaleFactor(),p=o.getLineHeightFactor();b.contentHeight=b.getContentHeight(g,p),b.contentHeight>=e&&(b.contentHeight=e,d.styles.minCellHeight-=e),b.contentHeight>t.height&&(t.height=b.contentHeight),d.contentHeight=d.getContentHeight(g,p),d.contentHeight>r&&(r=d.contentHeight),i[h.index]=d}}var y=new l.Row(t.raw,-1,t.section,i,!0);y.height=r;for(var v=0,m=n.columns;v<m.length;v++){var b;h=m[v],(d=y.cells[h.index])&&(d.height=y.height),(b=t.cells[h.index])&&(b.height=t.height)}return y}(n,u,e,t);g(t,e,n,r,s),v(t,e,i,r,s),f(t,e,h,o,i,r,s)}else v(t,e,i,r,s),f(t,e,n,o,i,r,s)}function g(t,e,n,i,r){i.x=e.settings.margin.left;for(var l=0,a=r;l<a.length;l++){var s=a[l],u=n.cells[s.index];if(u)if(t.applyStyles(u.styles),u.x=i.x,u.y=i.y,!1!==e.callCellHooks(t,e.hooks.willDrawCell,u,n,s,i)){p(t,u,i);var h=u.getTextPos();(0,o.default)(u.text,h.x,h.y,{halign:u.styles.halign,valign:u.styles.valign,maxWidth:Math.ceil(u.width-u.padding("left")-u.padding("right"))},t.getDocument()),e.callCellHooks(t,e.hooks.didDrawCell,u,n,s,i),i.x+=s.width}else i.x+=s.width;else i.x+=s.width}i.y+=n.height}function p(t,e,n){var o=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof o.lineWidth){var r=(0,i.getFillStyle)(o.lineWidth,o.fillColor);r&&t.rect(e.x,n.y,e.width,e.height,r)}else"object"==typeof o.lineWidth&&(o.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,o){var i,r,l,a;o.top&&(i=n.x,r=n.y,l=n.x+e.width,a=n.y,o.right&&(l+=.5*o.right),o.left&&(i-=.5*o.left),s(o.top,i,r,l,a));o.bottom&&(i=n.x,r=n.y+e.height,l=n.x+e.width,a=n.y+e.height,o.right&&(l+=.5*o.right),o.left&&(i-=.5*o.left),s(o.bottom,i,r,l,a));o.left&&(i=n.x,r=n.y,l=n.x,a=n.y+e.height,o.top&&(r-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.left,i,r,l,a));o.right&&(i=n.x+e.width,r=n.y,l=n.x+e.width,a=n.y+e.height,o.top&&(r-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.right,i,r,l,a));function s(e,n,o,i,r){t.getDocument().setLineWidth(e),t.getDocument().line(n,o,i,r,"S")}}(t,e,n,o.lineWidth))}function y(t,e,n,o){var i=e.settings.margin.bottom,r=e.settings.showFoot;return("everyPage"===r||"lastPage"===r&&n)&&(i+=e.getFootHeight(e.columns)),t.pageSize().height-o.y-i}function v(t,e,n,o,r,l){void 0===r&&(r=[]),void 0===l&&(l=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||l||e.foot.forEach((function(n){return g(t,e,n,o,r)})),e.callEndPageHooks(t,o);var a=e.settings.margin;(0,i.addTableBorder)(t,e,n,o),m(t),e.pageNumber++,o.x=a.left,o.y=a.top,n.y=a.top,e.callWillDrawPageHooks(t,o),"everyPage"===e.settings.showHead&&(e.head.forEach((function(n){return g(t,e,n,o,r)})),t.applyStyles(t.userStyles))}function m(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}},799:function(t,e){function n(t,e){var n=t>0,o=e||0===e;return n&&o?"DF":n?"S":o?"F":null}function o(t,e){var n,o,i,r;if(t=t||e,Array.isArray(t)){if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!==(n=t.left)&&void 0!==n?n:e,top:null!==(o=t.top)&&void 0!==o?o:e,right:null!==(i=t.right)&&void 0!==i?i:e,bottom:null!==(r=t.bottom)&&void 0!==r?r:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}Object.defineProperty(e,"__esModule",{value:!0}),e.getStringWidth=function(t,e,n){return n.applyStyles(e,!0),(Array.isArray(t)?t:[t]).map((function(t){return n.getTextWidth(t)})).reduce((function(t,e){return Math.max(t,e)}),0)},e.addTableBorder=function(t,e,o,i){var r=e.settings.tableLineWidth,l=e.settings.tableLineColor;t.applyStyles({lineWidth:r,lineColor:l});var a=n(r,!1);a&&t.rect(o.x,o.y,e.getWidth(t.pageSize().width),i.y-o.y,a)},e.getFillStyle=n,e.parseSpacing=o,e.getPageAvailableWidth=function(t,e){var n=o(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}}},e={};function n(o){var i=e[o];if(void 0!==i)return i.exports;var r=e[o]={exports:{}};return t[o].call(r.exports,r,r.exports,n),r.exports}var o={};return function(){var t,e=o;Object.defineProperty(e,"__esModule",{value:!0}),e.Table=e.Row=e.HookData=e.Column=e.CellHookData=e.Cell=e.applyPlugin=void 0,e.autoTable=h,e.__createTable=function(t,e){var n=(0,l.parseInput)(t,e);return(0,s.createTable)(t,n)},e.__drawTable=function(t,e){(0,u.drawTable)(t,e)};var i=n(639);Object.defineProperty(e,"applyPlugin",{enumerable:!0,get:function(){return i.applyPlugin}});var r=n(601);Object.defineProperty(e,"CellHookData",{enumerable:!0,get:function(){return r.CellHookData}}),Object.defineProperty(e,"HookData",{enumerable:!0,get:function(){return r.HookData}});var l=n(371),a=n(524);Object.defineProperty(e,"Cell",{enumerable:!0,get:function(){return a.Cell}}),Object.defineProperty(e,"Column",{enumerable:!0,get:function(){return a.Column}}),Object.defineProperty(e,"Row",{enumerable:!0,get:function(){return a.Row}}),Object.defineProperty(e,"Table",{enumerable:!0,get:function(){return a.Table}});var s=n(376),u=n(789);function h(t,e){var n=(0,l.parseInput)(t,e),o=(0,s.createTable)(t,n);(0,u.drawTable)(t,o)}try{if("undefined"!=typeof window&&window){var d=window,c=d.jsPDF||(null===(t=d.jspdf)||void 0===t?void 0:t.jsPDF);c&&(0,i.applyPlugin)(c)}}catch(t){console.error("Could not apply autoTable plugin",t)}e.default=h}(),o}()}));