// Comprehensive MLM Referral System Test
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🎯 Comprehensive MLM Referral System Test');
console.log('==========================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// MLM Configuration
const MLM_CONFIG = {
  BONUS_AMOUNT: 250.00,
  DIRECT_REFERRAL_LIMIT: 2,
  GRANDPARENT_REFERRAL_POSITION: 3
};

async function testMLMSystem() {
  console.log('🔍 Testing MLM Referral System...\n');
  
  let testsPassed = 0;
  let totalTests = 0;
  
  // Test 1: Database Connectivity
  totalTests++;
  console.log('1️⃣ Testing Database Connectivity...');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      console.log('❌ Database connection failed:', error.message);
    } else {
      console.log('✅ Database connection successful');
      testsPassed++;
    }
  } catch (error) {
    console.log('❌ Database connection error:', error.message);
  }
  
  // Test 2: Required Tables
  totalTests++;
  console.log('\n2️⃣ Testing Required Tables...');
  const requiredTables = ['users', 'referrals', 'wallet_transactions', 'admin_settings'];
  let tablesExist = 0;
  
  for (const table of requiredTables) {
    try {
      const { error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (!error) {
        console.log(`   ✅ ${table} table: accessible`);
        tablesExist++;
      } else {
        console.log(`   ❌ ${table} table: ${error.message}`);
      }
    } catch (error) {
      console.log(`   ❌ ${table} table: error`);
    }
  }
  
  if (tablesExist === requiredTables.length) {
    console.log('✅ All required tables exist');
    testsPassed++;
  } else {
    console.log(`❌ Missing ${requiredTables.length - tablesExist} tables`);
  }
  
  // Test 3: MLM Logic Calculation
  totalTests++;
  console.log('\n3️⃣ Testing MLM Logic Calculation...');
  
  const testCases = [
    { position: 1, expected: 'direct_referrer' },
    { position: 2, expected: 'direct_referrer' },
    { position: 3, expected: 'grandparent' },
    { position: 4, expected: 'direct_referrer' },
    { position: 5, expected: 'direct_referrer' }
  ];
  
  let logicTestsPassed = 0;
  
  testCases.forEach(testCase => {
    let actual;
    if (testCase.position <= MLM_CONFIG.DIRECT_REFERRAL_LIMIT) {
      actual = 'direct_referrer';
    } else if (testCase.position === MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION) {
      actual = 'grandparent';
    } else {
      actual = 'direct_referrer'; // 4th+ go back to direct referrer
    }

    const isCorrect = actual === testCase.expected;
    const status = isCorrect ? '✅' : '❌';

    console.log(`   ${status} Position ${testCase.position}: ${actual} (expected: ${testCase.expected})`);

    if (isCorrect) logicTestsPassed++;
  });
  
  if (logicTestsPassed === testCases.length) {
    console.log('✅ MLM logic calculation correct');
    testsPassed++;
  } else {
    console.log('❌ MLM logic calculation failed');
  }
  
  // Test 4: Admin Settings
  totalTests++;
  console.log('\n4️⃣ Testing Admin Settings...');
  try {
    const { data: settings, error } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value')
      .in('setting_key', ['mlm_system_enabled', 'mlm_bonus_amount']);
    
    if (error) {
      console.log('❌ Admin settings access failed:', error.message);
    } else {
      console.log('✅ Admin settings accessible');
      
      const mlmEnabled = settings?.find(s => s.setting_key === 'mlm_system_enabled');
      const mlmBonus = settings?.find(s => s.setting_key === 'mlm_bonus_amount');
      
      console.log(`   MLM System Enabled: ${mlmEnabled?.setting_value || 'not set'}`);
      console.log(`   MLM Bonus Amount: ₹${mlmBonus?.setting_value || 'not set'}`);
      
      testsPassed++;
    }
  } catch (error) {
    console.log('❌ Admin settings error:', error.message);
  }
  
  // Test 5: User Data Structure
  totalTests++;
  console.log('\n5️⃣ Testing User Data Structure...');
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, full_name, referral_code, referred_by, is_premium, wallet_balance')
      .limit(3);
    
    if (error) {
      console.log('❌ User data structure test failed:', error.message);
    } else {
      console.log('✅ User data structure correct');
      console.log(`   Found ${users?.length || 0} users in database`);
      
      const usersWithCodes = users?.filter(u => u.referral_code) || [];
      const usersWithReferrers = users?.filter(u => u.referred_by) || [];
      const premiumUsers = users?.filter(u => u.is_premium) || [];
      
      console.log(`   Users with referral codes: ${usersWithCodes.length}`);
      console.log(`   Users with referrers: ${usersWithReferrers.length}`);
      console.log(`   Premium users: ${premiumUsers.length}`);
      
      testsPassed++;
    }
  } catch (error) {
    console.log('❌ User data structure error:', error.message);
  }
  
  // Test 6: Wallet Transactions Structure
  totalTests++;
  console.log('\n6️⃣ Testing Wallet Transactions Structure...');
  try {
    const { data: transactions, error } = await supabase
      .from('wallet_transactions')
      .select('id, user_id, type, amount, description, reference_type')
      .limit(3);
    
    if (error) {
      console.log('❌ Wallet transactions structure test failed:', error.message);
    } else {
      console.log('✅ Wallet transactions structure correct');
      console.log(`   Found ${transactions?.length || 0} transactions in database`);
      testsPassed++;
    }
  } catch (error) {
    console.log('❌ Wallet transactions structure error:', error.message);
  }
  
  // Test 7: Referrals Table Structure
  totalTests++;
  console.log('\n7️⃣ Testing Referrals Table Structure...');
  try {
    const { data: referrals, error } = await supabase
      .from('referrals')
      .select('id, referrer_id, referred_user_id, bonus_amount, status')
      .limit(3);
    
    if (error) {
      console.log('❌ Referrals table structure test failed:', error.message);
    } else {
      console.log('✅ Referrals table structure correct');
      console.log(`   Found ${referrals?.length || 0} referrals in database`);
      testsPassed++;
    }
  } catch (error) {
    console.log('❌ Referrals table structure error:', error.message);
  }
  
  // Test Summary
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${testsPassed}`);
  console.log(`Failed: ${totalTests - testsPassed}`);
  console.log(`Success Rate: ${Math.round((testsPassed / totalTests) * 100)}%`);
  
  if (testsPassed === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! MLM Referral System is ready!');
    console.log('\n🎯 MLM System Configuration:');
    console.log(`   • Bonus Amount: ₹${MLM_CONFIG.BONUS_AMOUNT}`);
    console.log(`   • 1st & 2nd referrals → Direct referrer`);
    console.log(`   • 3rd+ referrals → Grandparent (referrer's referrer)`);
    console.log(`   • Premium requirement: Yes`);
    console.log('\n🚀 Ready to process MLM referrals!');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the issues above.');
    console.log('The MLM system may not work correctly until these are resolved.');
  }
  
  return testsPassed === totalTests;
}

testMLMSystem().then(success => {
  process.exit(success ? 0 : 1);
});
