import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { referralCode, newUserId, newUserEmail } = await req.json()

    console.log('Multi-level referral processing requested:', { referralCode, newUserId, newUserEmail });

    // Validate that the new user is premium (critical requirement)
    const { data: newUser, error: newUserError } = await supabaseClient
      .from('users')
      .select('id, email, is_premium, referred_by')
      .eq('id', newUserId)
      .single()

    if (newUserError || !newUser) {
      throw new Error('New user not found')
    }

    if (!newUser.is_premium) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'User is not premium - referral processing skipped',
          reason: 'premium_required'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // Validate referral code and get direct referrer
    const { data: directReferrer, error: referrerError } = await supabaseClient
      .from('users')
      .select('id, email, full_name, referral_code, referred_by, is_premium, wallet_balance')
      .eq('referral_code', referralCode)
      .single()

    if (referrerError || !directReferrer) {
      throw new Error('Invalid referral code - direct referrer not found')
    }

    if (!directReferrer.is_premium) {
      throw new Error('Direct referrer is not premium - no bonuses will be distributed')
    }

    // Multi-level bonus structure
    const LEVEL_BONUSES = {
      1: 250.00, 2: 100.00, 3: 50.00, 4: 25.00, 5: 10.00,
      6: 5.00, 7: 2.00, 8: 1.00, 9: 0.50, 10: 0.25
    }

    // Build referral chain
    const chain = []
    const visitedUserIds = new Set()
    let currentUser = directReferrer
    let level = 1

    while (currentUser && level <= 10) {
      if (visitedUserIds.has(currentUser.id)) {
        console.warn(`Circular reference detected at level ${level}`)
        break
      }

      visitedUserIds.add(currentUser.id)

      if (currentUser.is_premium) {
        chain.push({
          ...currentUser,
          level: level
        })
      } else {
        console.log(`User at level ${level} is not premium, stopping chain`)
        break
      }

      if (!currentUser.referred_by) break

      const { data: nextUser, error } = await supabaseClient
        .from('users')
        .select('id, email, full_name, referral_code, referred_by, is_premium, wallet_balance')
        .eq('referral_code', currentUser.referred_by)
        .single()

      if (error || !nextUser) break

      currentUser = nextUser
      level++
    }

    // Distribute bonuses to each level
    const distributions = []
    let totalBonusDistributed = 0

    for (const user of chain) {
      const bonusAmount = LEVEL_BONUSES[user.level]

      if (!bonusAmount || bonusAmount <= 0) continue

      try {
        // Update user's wallet balance
        const { error: updateError } = await supabaseClient
          .from('users')
          .update({
            wallet_balance: supabaseClient.sql`wallet_balance + ${bonusAmount}`,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)

        if (updateError) {
          console.error(`Failed to update wallet for user ${user.id}:`, updateError)
          continue
        }

        // Create wallet transaction record
        const { error: transactionError } = await supabaseClient
          .from('wallet_transactions')
          .insert({
            user_id: user.id,
            type: 'credit',
            amount: bonusAmount,
            description: `Level ${user.level} referral bonus for ${newUserEmail} becoming premium`,
            reference_type: 'multi_level_referral',
            reference_id: newUserId
          })

        if (transactionError) {
          console.error(`Failed to create transaction for user ${user.id}:`, transactionError)
        }

        distributions.push({
          userId: user.id,
          level: user.level,
          amount: bonusAmount,
          userName: user.full_name,
          userEmail: user.email
        })

        totalBonusDistributed += bonusAmount

      } catch (error) {
        console.error(`Error distributing bonus to user ${user.id}:`, error)
      }
    }

    // Create referral record
    const { data: referralRecord, error: referralError } = await supabaseClient
      .from('referrals')
      .insert({
        referrer_id: directReferrer.id,
        referred_user_id: newUserId,
        bonus_amount: LEVEL_BONUSES[1],
        status: 'completed'
      })
      .select()
      .single()

    if (referralError) {
      console.error('Failed to create referral record:', referralError)
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Multi-level referral processed successfully',
        totalBonusDistributed,
        levelsProcessed: distributions.length,
        distributions,
        referralId: referralRecord?.id
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
