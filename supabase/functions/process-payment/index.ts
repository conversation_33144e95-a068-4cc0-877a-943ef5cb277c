import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { 
      userId, 
      amount, 
      paymentMethod, 
      orderId, 
      paymentGateway = 'razorpay',
      description = 'Payment processed'
    } = await req.json()

    // Validate user exists
    const { data: user, error: userError } = await supabaseClient
      .from('users')
      .select('id, email, wallet_balance')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      throw new Error('User not found')
    }

    let paymentSuccess = false
    let transactionId = null

    // Process payment based on method
    switch (paymentMethod) {
      case 'wallet':
        // Check wallet balance
        if (user.wallet_balance < amount) {
          throw new Error('Insufficient wallet balance')
        }

        // Deduct from wallet
        const { error: deductError } = await supabaseClient
          .from('wallet_transactions')
          .insert({
            user_id: userId,
            type: 'debit',
            amount: amount,
            description: description,
            reference_type: 'order',
            reference_id: orderId
          })

        if (deductError) {
          throw new Error('Failed to process wallet payment')
        }

        paymentSuccess = true
        transactionId = `wallet_${Date.now()}`
        break

      case 'razorpay':
        // In production, verify Razorpay payment signature here
        // For now, simulate successful payment
        paymentSuccess = true
        transactionId = `razorpay_${Date.now()}`

        // Add to wallet if payment successful
        if (paymentSuccess) {
          await supabaseClient
            .from('wallet_transactions')
            .insert({
              user_id: userId,
              type: 'credit',
              amount: amount,
              description: `Payment via ${paymentGateway}`,
              reference_type: 'payment',
              reference_id: transactionId
            })
        }
        break

      case 'upi':
        // Simulate UPI payment
        paymentSuccess = true
        transactionId = `upi_${Date.now()}`

        if (paymentSuccess) {
          await supabaseClient
            .from('wallet_transactions')
            .insert({
              user_id: userId,
              type: 'credit',
              amount: amount,
              description: `UPI Payment`,
              reference_type: 'payment',
              reference_id: transactionId
            })
        }
        break

      default:
        throw new Error('Unsupported payment method')
    }

    // Update order status if orderId provided
    if (orderId && paymentSuccess) {
      await supabaseClient
        .from('orders')
        .update({ 
          payment_status: 'completed',
          transaction_id: transactionId,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
    }

    // Log payment transaction
    await supabaseClient
      .from('payment_logs')
      .insert({
        user_id: userId,
        amount: amount,
        payment_method: paymentMethod,
        payment_gateway: paymentGateway,
        transaction_id: transactionId,
        status: paymentSuccess ? 'success' : 'failed',
        order_id: orderId,
        metadata: {
          description,
          processed_at: new Date().toISOString()
        }
      })

    return new Response(
      JSON.stringify({
        success: paymentSuccess,
        transactionId,
        message: paymentSuccess ? 'Payment processed successfully' : 'Payment failed',
        walletBalance: paymentMethod === 'wallet' ? user.wallet_balance - amount : user.wallet_balance + amount
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: paymentSuccess ? 200 : 400,
      },
    )

  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
