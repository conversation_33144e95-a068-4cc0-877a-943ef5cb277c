-- Auth Security Configuration Fix
-- This addresses the OTP expiry warning and other auth security settings

-- Note: These settings should be applied via Supabase Dashboard or API
-- Auth settings that need to be updated:

/*
1. OTP Expiry Settings (via Dashboard > Authentication > Settings):
   - Email OTP expiry: 3600 seconds (1 hour) -> Change to 1800 seconds (30 minutes)
   - SMS OTP expiry: 3600 seconds (1 hour) -> Change to 600 seconds (10 minutes)

2. Password Security (via Dashboard > Authentication > Settings):
   - Enable HIBP (Have I Been Pwned) password checking
   - Set minimum password length to 8 characters
   - Require special characters

3. Rate Limiting (via Dashboard > Authentication > Settings):
   - Email sent rate limit: 60 per hour
   - SMS sent rate limit: 10 per hour
   - Token refresh rate limit: 150 per hour

4. Session Security (via Dashboard > Authentication > Settings):
   - Enable refresh token rotation
   - Set session timeout to 24 hours
   - Enable single session per user (optional)
*/

-- Database-level security improvements
-- These can be applied directly to the database

-- Create a function to validate strong passwords
CREATE OR REPLACE FUNCTION validate_password_strength(password TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check minimum length
    IF LENGTH(password) < 8 THEN
        RETURN FALSE;
    END IF;
    
    -- Check for at least one uppercase letter
    IF password !~ '[A-Z]' THEN
        RETURN FALSE;
    END IF;
    
    -- Check for at least one lowercase letter
    IF password !~ '[a-z]' THEN
        RETURN FALSE;
    END IF;
    
    -- Check for at least one digit
    IF password !~ '[0-9]' THEN
        RETURN FALSE;
    END IF;
    
    -- Check for at least one special character
    IF password !~ '[^A-Za-z0-9]' THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    event_type TEXT,
    user_id UUID DEFAULT NULL,
    ip_address INET DEFAULT NULL,
    details JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    INSERT INTO security_logs (
        event_type,
        user_id,
        ip_address,
        details,
        created_at
    ) VALUES (
        event_type,
        user_id,
        ip_address,
        details,
        NOW()
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Silently fail if security_logs table doesn't exist
        NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create security logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS security_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    ip_address INET,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on security logs
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;

-- Only admin can access security logs
CREATE POLICY "Admin only access" ON security_logs
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);

COMMENT ON TABLE security_logs IS 'Security event logging for audit trail';
