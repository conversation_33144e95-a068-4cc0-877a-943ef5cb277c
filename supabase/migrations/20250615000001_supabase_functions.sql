-- =============================================
-- SUPABASE NATIVE FUNCTIONS & TRIGGERS
-- Making the platform 100% Supabase
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- 1. REALTIME SUBSCRIPTIONS
-- =============================================

-- Enable realtime for all tables
ALTER PUBLICATION supabase_realtime ADD TABLE users;
ALTER PUBLICATION supabase_realtime ADD TABLE products;
ALTER PUBLICATION supabase_realtime ADD TABLE orders;
ALTER PUBLICATION supabase_realtime ADD TABLE wallet_transactions;
ALTER PUBLICATION supabase_realtime ADD TABLE referrals;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- =============================================
-- 2. AUTOMATIC USER PROFILE CREATION
-- =============================================

-- Function to automatically create user profile on auth signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  referral_code_generated TEXT;
BEGIN
  -- Generate unique referral code
  referral_code_generated := 'REF' || UPPER(SUBSTRING(MD5(NEW.id::TEXT || EXTRACT(EPOCH FROM NOW())::TEXT) FROM 1 FOR 8));
  
  -- Insert user profile
  INSERT INTO public.users (
    id,
    email,
    full_name,
    referral_code,
    is_premium,
    wallet_balance,
    ewallet_unlocked,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    referral_code_generated,
    FALSE,
    0.00,
    FALSE,
    NOW(),
    NOW()
  );

  -- Welcome bonus logic removed - awaiting new implementation
  -- TODO: Implement new welcome bonus logic if needed

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =============================================
-- 3. REAL-TIME WALLET BALANCE UPDATES
-- =============================================

-- Function to update wallet balance in real-time
CREATE OR REPLACE FUNCTION public.update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Update wallet balance
    IF NEW.type = 'credit' THEN
      UPDATE public.users 
      SET wallet_balance = wallet_balance + NEW.amount,
          updated_at = NOW()
      WHERE id = NEW.user_id;
    ELSE
      UPDATE public.users 
      SET wallet_balance = wallet_balance - NEW.amount,
          updated_at = NOW()
      WHERE id = NEW.user_id;
    END IF;
    
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Reverse old transaction
    IF OLD.type = 'credit' THEN
      UPDATE public.users 
      SET wallet_balance = wallet_balance - OLD.amount,
          updated_at = NOW()
      WHERE id = OLD.user_id;
    ELSE
      UPDATE public.users 
      SET wallet_balance = wallet_balance + OLD.amount,
          updated_at = NOW()
      WHERE id = OLD.user_id;
    END IF;
    
    -- Apply new transaction
    IF NEW.type = 'credit' THEN
      UPDATE public.users 
      SET wallet_balance = wallet_balance + NEW.amount,
          updated_at = NOW()
      WHERE id = NEW.user_id;
    ELSE
      UPDATE public.users 
      SET wallet_balance = wallet_balance - NEW.amount,
          updated_at = NOW()
      WHERE id = NEW.user_id;
    END IF;
    
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Reverse deleted transaction
    IF OLD.type = 'credit' THEN
      UPDATE public.users 
      SET wallet_balance = wallet_balance - OLD.amount,
          updated_at = NOW()
      WHERE id = OLD.user_id;
    ELSE
      UPDATE public.users 
      SET wallet_balance = wallet_balance + OLD.amount,
          updated_at = NOW()
      WHERE id = OLD.user_id;
    END IF;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for wallet balance updates
DROP TRIGGER IF EXISTS wallet_balance_trigger ON public.wallet_transactions;
CREATE TRIGGER wallet_balance_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.wallet_transactions
  FOR EACH ROW EXECUTE FUNCTION public.update_wallet_balance();

-- =============================================
-- 4. AUTOMATIC NOTIFICATIONS
-- =============================================

-- Create notifications table if not exists
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'info',
  read BOOLEAN DEFAULT FALSE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Function to create notifications
CREATE OR REPLACE FUNCTION public.create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info',
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO public.notifications (user_id, title, message, type, metadata)
  VALUES (p_user_id, p_title, p_message, p_type, p_metadata)
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 5. AUTOMATIC ORDER PROCESSING
-- =============================================

-- Function to process order creation
CREATE OR REPLACE FUNCTION public.process_order()
RETURNS TRIGGER AS $$
BEGIN
  -- Create notification for new order
  PERFORM public.create_notification(
    NEW.user_id,
    'Order Placed Successfully',
    'Your order #' || NEW.id || ' has been placed and is being processed.',
    'success',
    jsonb_build_object('order_id', NEW.id, 'amount', NEW.total_amount)
  );
  
  -- Update product stock (if stock tracking is enabled)
  -- This would be implemented based on order_items
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for order processing
DROP TRIGGER IF EXISTS process_order_trigger ON public.orders;
CREATE TRIGGER process_order_trigger
  AFTER INSERT ON public.orders
  FOR EACH ROW EXECUTE FUNCTION public.process_order();

-- =============================================
-- 6. SUPABASE STORAGE POLICIES
-- =============================================

-- Storage policies for product images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('products', 'products', true)
ON CONFLICT (id) DO NOTHING;

-- Allow public read access to product images
CREATE POLICY "Public read access for product images" ON storage.objects
  FOR SELECT USING (bucket_id = 'products');

-- Allow authenticated users to upload product images
CREATE POLICY "Authenticated users can upload product images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'products' 
    AND auth.role() = 'authenticated'
  );

-- Allow users to update their own uploads
CREATE POLICY "Users can update own product images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'products' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- =============================================
-- 7. SCHEDULED FUNCTIONS (using pg_cron if available)
-- =============================================

-- Function to cleanup expired OTPs
CREATE OR REPLACE FUNCTION public.cleanup_expired_otps()
RETURNS void AS $$
BEGIN
  DELETE FROM public.otp_verifications 
  WHERE expires_at < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send reminder notifications
CREATE OR REPLACE FUNCTION public.send_reminder_notifications()
RETURNS void AS $$
BEGIN
  -- Send cart abandonment reminders
  -- Send subscription renewal reminders
  -- Send KYC completion reminders
  NULL; -- Placeholder for future implementation
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 8. ANALYTICS FUNCTIONS
-- =============================================

-- Function to get real-time dashboard stats
CREATE OR REPLACE FUNCTION public.get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM public.users),
    'premium_users', (SELECT COUNT(*) FROM public.users WHERE is_premium = true),
    'total_orders', (SELECT COUNT(*) FROM public.orders),
    'total_revenue', (SELECT COALESCE(SUM(total_amount), 0) FROM public.orders WHERE payment_status = 'completed'),
    'wallet_transactions_total', (SELECT COALESCE(SUM(amount), 0) FROM public.wallet_transactions WHERE type = 'credit'),
    'total_withdrawals', (SELECT COALESCE(SUM(amount), 0) FROM public.wallet_transactions WHERE type = 'debit'),
    'referral_bonuses_paid', (SELECT COALESCE(SUM(amount), 0) FROM public.wallet_transactions WHERE reference_type = 'referral'),
    'last_updated', NOW()
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
