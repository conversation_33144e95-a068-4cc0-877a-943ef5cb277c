-- Switch to service role for elevated permissions
SET ROLE service_role;

-- Grant admin <NAME_EMAIL>
DO $$ 
BEGIN
  -- Get the auth.<NAME_EMAIL>
  WITH admin_user AS (
    SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1
  )
  UPDATE auth.users
  SET raw_app_meta_data = 
    COALESCE(raw_app_meta_data, '{}'::jsonb) || 
    '{"is_admin": true}'::jsonb
  FROM admin_user
  WHERE auth.users.id = admin_user.id;
END $$;

-- Grant necessary permissions to authenticated role
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Create policy for admin access to auth.users
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'auth' 
    AND tablename = 'users' 
    AND policyname = 'Ad<PERSON> can manage auth users'
  ) THEN
    CREATE POLICY "Admin can manage auth users" ON auth.users
      FOR ALL
      USING (
        coalesce(
          current_setting('request.jwt.claims', true)::jsonb -> 'email' = '"<EMAIL>"',
          false
        )
      )
      WITH CHECK (
        coalesce(
          current_setting('request.jwt.claims', true)::jsonb -> 'email' = '"<EMAIL>"',
          false
        )
      );
  END IF;
END $$;

-- Reset role
RESET ROLE; 