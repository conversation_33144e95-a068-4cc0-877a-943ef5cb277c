-- Switch to service role
SET ROLE service_role;

-- Admin policy for categories
CREATE POLICY "Admin can manage all categories" ON categories
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for order_items
CREATE POLICY "Admin can manage all order items" ON order_items
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for orders
CREATE POLICY "Admin can manage all orders" ON orders
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for products
CREATE POLICY "Admin can manage all products" ON products
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for referral_settings
CREATE POLICY "Admin can manage all referral settings" ON referral_settings
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for referrals
CREATE POLICY "Admin can manage all referrals" ON referrals
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for reviews
CREATE POLICY "Admin can manage all reviews" ON reviews
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for users
CREATE POLICY "Admin can manage all users" ON users
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Admin policy for wallet_transactions
CREATE POLICY "Admin can manage all wallet transactions" ON wallet_transactions
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Reset role
RESET ROLE; 