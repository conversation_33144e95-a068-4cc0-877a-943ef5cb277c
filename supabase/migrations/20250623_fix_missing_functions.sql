-- Create referral_audit_log table if not exists
CREATE TABLE IF NOT EXISTS public.referral_audit_log (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on referral_audit_log
ALTER TABLE public.referral_audit_log ENABLE ROW LEVEL SECURITY;

-- Create policy for referral_audit_log
CREATE POLICY "Users can view their own audit logs" ON public.referral_audit_log
    FOR SELECT USING (auth.uid() = user_id);

-- Create safe_wallet_update function
CREATE OR REPLACE FUNCTION public.safe_wallet_update(
    p_user_id UUID,
    p_amount DECIMAL,
    p_description TEXT,
    p_reference_type TEXT DEFAULT NULL,
    p_reference_id TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
R<PERSON>URNS JSON AS $$
DECLARE
    v_current_balance DECIMAL;
    v_new_balance DECIMAL;
    v_transaction_id UUID;
    v_result JSON;
BEGIN
    -- Validate input parameters
    IF p_user_id IS NULL THEN
        RETURN '{"success": false, "error": "User ID is required"}'::JSON;
    END IF;
    
    IF p_amount = 0 THEN
        RETURN '{"success": false, "error": "Amount cannot be zero"}'::JSON;
    END IF;
    
    -- Get current balance with row locking
    SELECT wallet_balance INTO v_current_balance
    FROM public.users 
    WHERE id = p_user_id
    FOR UPDATE;
    
    IF NOT FOUND THEN
        RETURN '{"success": false, "error": "User not found"}'::JSON;
    END IF;
    
    -- Calculate new balance
    v_new_balance := COALESCE(v_current_balance, 0) + p_amount;
    
    -- Prevent negative balance
    IF v_new_balance < 0 THEN
        RETURN '{"success": false, "error": "Insufficient balance"}'::JSON;
    END IF;
    
    -- Update user balance
    UPDATE public.users 
    SET 
        wallet_balance = v_new_balance,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Create transaction record
    INSERT INTO public.wallet_transactions (
        user_id,
        type,
        amount,
        description,
        reference_type,
        reference_id,
        metadata
    ) VALUES (
        p_user_id,
        CASE WHEN p_amount > 0 THEN 'credit' ELSE 'debit' END,
        ABS(p_amount),
        p_description,
        p_reference_type,
        p_reference_id,
        p_metadata
    ) RETURNING id INTO v_transaction_id;
    
    -- Return success result
    RETURN json_build_object(
        'success', true,
        'transaction_id', v_transaction_id,
        'previous_balance', v_current_balance,
        'new_balance', v_new_balance,
        'amount', p_amount,
        'timestamp', NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create batch_wallet_updates function
CREATE OR REPLACE FUNCTION public.batch_wallet_updates(
    p_updates JSONB[]
)
RETURNS JSONB AS $$
DECLARE
    v_update JSONB;
    v_results JSONB[] := '{}';
    v_result JSON;
BEGIN
    -- Process each update in the batch
    FOREACH v_update IN ARRAY p_updates
    LOOP
        -- Call safe_wallet_update for each entry
        v_result := public.safe_wallet_update(
            (v_update->>'user_id')::UUID,
            (v_update->>'amount')::DECIMAL,
            v_update->>'description',
            v_update->>'reference_type',
            v_update->>'reference_id',
            COALESCE(v_update->'metadata', '{}'::JSONB)
        );
        
        -- Collect results
        v_results := array_append(v_results, v_result::JSONB);
    END LOOP;
    
    RETURN jsonb_build_object(
        'success', true,
        'results', v_results
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 