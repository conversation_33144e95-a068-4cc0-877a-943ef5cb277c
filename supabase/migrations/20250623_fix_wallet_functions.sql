-- Drop existing functions to ensure clean slate
DROP FUNCTION IF EXISTS public.safe_wallet_update(UUID, DECIMAL, TEXT, TEXT, TEXT, JSONB);
DROP FUNCTION IF EXISTS public.batch_wallet_updates(JSONB);

-- Create safe_wallet_update function
CREATE OR REPLACE FUNCTION public.safe_wallet_update(
    p_user_id UUID,
    p_amount DECIMAL,
    p_description TEXT,
    p_reference_type TEXT DEFAULT NULL,
    p_reference_id TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS JSON AS $$
DECLARE
    v_current_balance DECIMAL;
    v_new_balance DECIMAL;
    v_transaction_id UUID;
    v_result JSON;
BEGIN
    -- Validate input parameters
    IF p_user_id IS NULL THEN
        RETURN '{"success": false, "error": "User ID is required"}'::JSON;
    END IF;
    
    IF p_amount = 0 THEN
        RETURN '{"success": false, "error": "Amount cannot be zero"}'::JSON;
    END IF;
    
    -- Get current balance with row locking
    SELECT wallet_balance INTO v_current_balance
    FROM public.users 
    WHERE id = p_user_id
    FOR UPDATE;
    
    IF NOT FOUND THEN
        RETURN '{"success": false, "error": "User not found"}'::JSON;
    END IF;
    
    -- Calculate new balance
    v_new_balance := COALESCE(v_current_balance, 0) + p_amount;
    
    -- Prevent negative balance
    IF v_new_balance < 0 THEN
        RETURN '{"success": false, "error": "Insufficient balance"}'::JSON;
    END IF;
    
    -- Update user balance
    UPDATE public.users 
    SET 
        wallet_balance = v_new_balance,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Create transaction record
    INSERT INTO public.wallet_transactions (
        user_id,
        type,
        amount,
        description,
        reference_type,
        reference_id,
        metadata,
        created_at
    ) VALUES (
        p_user_id,
        CASE WHEN p_amount > 0 THEN 'credit' ELSE 'debit' END,
        ABS(p_amount),
        p_description,
        p_reference_type,
        p_reference_id,
        p_metadata,
        NOW()
    ) RETURNING id INTO v_transaction_id;
    
    -- Return success result
    RETURN json_build_object(
        'success', true,
        'transaction_id', v_transaction_id,
        'previous_balance', v_current_balance,
        'new_balance', v_new_balance,
        'amount', p_amount,
        'timestamp', NOW()
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return failure
        INSERT INTO public.referral_audit_log (
            user_id,
            action,
            details
        ) VALUES (
            p_user_id,
            'wallet_update_error',
            jsonb_build_object(
                'error', SQLERRM,
                'error_detail', SQLSTATE,
                'amount', p_amount,
                'description', p_description
            )
        );
        
        RETURN json_build_object(
            'success', false,
            'error', 'Database error: ' || SQLERRM,
            'error_code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create batch_wallet_updates function
CREATE OR REPLACE FUNCTION public.batch_wallet_updates(
    p_updates JSONB[]
)
RETURNS JSONB AS $$
DECLARE
    v_update JSONB;
    v_results JSONB[] := '{}';
    v_result JSON;
BEGIN
    -- Process each update in the batch
    FOREACH v_update IN ARRAY p_updates
    LOOP
        -- Call safe_wallet_update for each entry
        v_result := public.safe_wallet_update(
            (v_update->>'user_id')::UUID,
            (v_update->>'amount')::DECIMAL,
            v_update->>'description',
            v_update->>'reference_type',
            v_update->>'reference_id',
            COALESCE(v_update->'metadata', '{}'::JSONB)
        );
        
        -- Collect results
        v_results := array_append(v_results, v_result::JSONB);
    END LOOP;
    
    RETURN jsonb_build_object(
        'success', true,
        'results', v_results
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Batch processing failed: ' || SQLERRM,
            'error_code', SQLSTATE
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.safe_wallet_update(UUID, DECIMAL, TEXT, TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION public.batch_wallet_updates(JSONB[]) TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.safe_wallet_update IS 'Safely update user wallet balance with validation and transaction logging';
COMMENT ON FUNCTION public.batch_wallet_updates IS 'Process multiple wallet updates in batch for multi-level referral distribution'; 