-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'users') THEN
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) PRIMARY KEY,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            username TEXT UNIQUE,
            is_premium BOOLEAN DEFAULT false,
            premium_expires_at TIMESTAMP WITH TIME ZONE,
            wallet_balance DECIMAL(10,2) DEFAULT 0,
            referral_code TEXT UNIQUE,
            referred_by UUID REFERENCES public.users(id),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create categories table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'categories') THEN
        CREATE TABLE public.categories (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            image_url TEXT,
            parent_id UUID REFERENCES public.categories(id),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create products table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'products') THEN
        CREATE TABLE public.products (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            image_url TEXT NOT NULL,
            images TEXT[],
            category UUID REFERENCES public.categories(id) NOT NULL,
            ingredients TEXT[] NOT NULL,
            benefits TEXT[],
            dosage TEXT,
            warnings TEXT,
            rating DECIMAL(3,2) DEFAULT 0,
            reviews_count INTEGER DEFAULT 0,
            stock_quantity INTEGER NOT NULL DEFAULT 0,
            is_premium_only BOOLEAN DEFAULT false,
            is_featured BOOLEAN DEFAULT false,
            original_price DECIMAL(10,2),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create orders table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'orders') THEN
        CREATE TABLE public.orders (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id UUID REFERENCES public.users(id) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            wallet_used DECIMAL(10,2) DEFAULT 0,
            payment_method TEXT NOT NULL CHECK (payment_method IN ('wallet', 'card', 'mixed')),
            delivery_address TEXT NOT NULL,
            status TEXT NOT NULL CHECK (status IN ('pending', 'confirmed', 'shipped', 'delivered', 'cancelled')),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create order_items table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'order_items') THEN
        CREATE TABLE public.order_items (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            order_id UUID REFERENCES public.orders(id) NOT NULL,
            product_id UUID REFERENCES public.products(id) NOT NULL,
            quantity INTEGER NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create wallet_transactions table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'wallet_transactions') THEN
        CREATE TABLE public.wallet_transactions (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id UUID REFERENCES public.users(id) NOT NULL,
            type TEXT NOT NULL CHECK (type IN ('credit', 'debit')),
            amount DECIMAL(10,2) NOT NULL,
            description TEXT NOT NULL,
            reference_type TEXT CHECK (reference_type IN ('order', 'referral', 'admin', 'payment')),
            reference_id UUID,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create referrals table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'referrals') THEN
        CREATE TABLE public.referrals (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            referrer_id UUID REFERENCES public.users(id) NOT NULL,
            referred_user_id UUID REFERENCES public.users(id) NOT NULL,
            bonus_amount DECIMAL(10,2) NOT NULL,
            status TEXT NOT NULL CHECK (status IN ('pending', 'completed')),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create reviews table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'reviews') THEN
        CREATE TABLE public.reviews (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            product_id UUID REFERENCES public.products(id) NOT NULL,
            user_id UUID REFERENCES public.users(id) NOT NULL,
            rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
            comment TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Create referral_settings table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'referral_settings') THEN
        CREATE TABLE public.referral_settings (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            referrer_bonus DECIMAL(10,2) NOT NULL,
            referred_bonus DECIMAL(10,2) NOT NULL,
            max_referrals_per_user INTEGER NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Insert default referral settings if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM public.referral_settings LIMIT 1) THEN
        INSERT INTO public.referral_settings (referrer_bonus, referred_bonus, max_referrals_per_user)
        VALUES (100.00, 50.00, 10);
    END IF;
END $$;

-- Enable RLS on all tables
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'users') THEN
        ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.wallet_transactions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.referral_settings ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Create basic policies if they don't exist
DO $$ 
BEGIN
    -- Product policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Public products are viewable by everyone') THEN
        CREATE POLICY "Public products are viewable by everyone" ON public.products
            FOR SELECT USING (true);
    END IF;

    -- Category policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Public categories are viewable by everyone') THEN
        CREATE POLICY "Public categories are viewable by everyone" ON public.categories
            FOR SELECT USING (true);
    END IF;

    -- User policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view their own data') THEN
        CREATE POLICY "Users can view their own data" ON public.users
            FOR SELECT USING (auth.uid() = id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can update their own data') THEN
        CREATE POLICY "Users can update their own data" ON public.users
            FOR UPDATE USING (auth.uid() = id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert their own data') THEN
        CREATE POLICY "Users can insert their own data" ON public.users
            FOR INSERT WITH CHECK (auth.uid() = id);
    END IF;

    -- Allow public to check referral codes
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Anyone can check referral codes') THEN
        CREATE POLICY "Anyone can check referral codes" ON public.users
            FOR SELECT USING (true);
    END IF;

    -- Wallet transaction policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view their own transactions') THEN
        CREATE POLICY "Users can view their own transactions" ON public.wallet_transactions
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert their own transactions') THEN
        CREATE POLICY "Users can insert their own transactions" ON public.wallet_transactions
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    -- Referral policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view their own referrals') THEN
        CREATE POLICY "Users can view their own referrals" ON public.referrals
            FOR SELECT USING (auth.uid() = referrer_id OR auth.uid() = referred_user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert their own referrals') THEN
        CREATE POLICY "Users can insert their own referrals" ON public.referrals
            FOR INSERT WITH CHECK (auth.uid() = referrer_id);
    END IF;

    -- Review policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Anyone can view reviews') THEN
        CREATE POLICY "Anyone can view reviews" ON public.reviews
            FOR SELECT USING (true);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert their own reviews') THEN
        CREATE POLICY "Users can insert their own reviews" ON public.reviews
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can update their own reviews') THEN
        CREATE POLICY "Users can update their own reviews" ON public.reviews
            FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    -- Order policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view their own orders') THEN
        CREATE POLICY "Users can view their own orders" ON public.orders
            FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert their own orders') THEN
        CREATE POLICY "Users can insert their own orders" ON public.orders
            FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    -- Order item policies
    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can view their own order items') THEN
        CREATE POLICY "Users can view their own order items" ON public.order_items
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM public.orders
                    WHERE orders.id = order_items.order_id
                    AND orders.user_id = auth.uid()
                )
            );
    END IF;

    IF NOT EXISTS (SELECT FROM pg_policies WHERE policyname = 'Users can insert their own order items') THEN
        CREATE POLICY "Users can insert their own order items" ON public.order_items
            FOR INSERT WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.orders
                    WHERE orders.id = order_items.order_id
                    AND orders.user_id = auth.uid()
                )
            );
    END IF;
END $$;
