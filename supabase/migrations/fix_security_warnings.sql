-- Fix Supabase Security Warnings
-- This migration addresses all the security warnings from Supabase

-- Enable RLS on all tables that don't have it
ALTER TABLE admin_premium_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_performance_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE returns ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_code_rate_limit ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE premium_subscription_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE premium_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Re-enable RLS on users table (it was disabled for testing)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create admin-only policies for sensitive tables
CREATE POLICY "Admin only access" ON admin_premium_audit
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON referral_performance_log
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON admin_settings
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON login_logs
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON premium_subscription_settings
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON premium_subscriptions
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

-- E-commerce related tables - admin only
CREATE POLICY "Admin only access" ON order_notifications
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON returns
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON inventory_transactions
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON shipments
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

CREATE POLICY "Admin only access" ON order_status_history
  FOR ALL
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

-- Rate limiting table - allow public read for validation, admin for management
CREATE POLICY "Public rate limit check" ON referral_code_rate_limit
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Admin manage rate limits" ON referral_code_rate_limit
  FOR INSERT, UPDATE, DELETE
  TO authenticated
  USING (auth.uid() = 'a8ed8e65-aa10-403d-9675-14ed3deacd38'::uuid);

-- Fix function security by setting search_path
-- This addresses the "role mutable search_path" warnings

-- Create a function to fix search paths for all functions
CREATE OR REPLACE FUNCTION fix_function_security()
RETURNS void AS $$
DECLARE
    func_record RECORD;
BEGIN
    -- Get all functions that need fixing
    FOR func_record IN 
        SELECT routine_name 
        FROM information_schema.routines 
        WHERE routine_schema = 'public'
        AND routine_type = 'FUNCTION'
    LOOP
        -- Set secure search path for each function
        EXECUTE format('ALTER FUNCTION public.%I SET search_path = public, pg_temp', func_record.routine_name);
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Execute the fix
SELECT fix_function_security();

-- Drop the helper function
DROP FUNCTION fix_function_security();

-- Create a comment to track this migration
COMMENT ON TABLE users IS 'Security warnings fixed: RLS enabled, policies created, function paths secured';
