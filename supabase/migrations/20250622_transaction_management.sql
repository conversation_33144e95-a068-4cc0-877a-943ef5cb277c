-- =============================================
-- TRANSACTION MANAGEMENT FOR MULTI-LEVEL REFERRALS
-- Add database functions for transaction handling
-- =============================================

-- Create function to begin transaction (placeholder for proper transaction handling)
CREATE OR REPLACE FUNCTION public.begin_transaction()
RETURNS JSON AS $$
BEGIN
  -- In a real implementation, this would start a database transaction
  -- For now, we'll return a success indicator
  RETURN '{"status": "transaction_started", "timestamp": "' || NOW() || '"}'::JSON;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to commit transaction
CREATE OR REPLACE FUNCTION public.commit_transaction()
RETURNS JSON AS $$
BEGIN
  -- In a real implementation, this would commit the transaction
  RETURN '{"status": "transaction_committed", "timestamp": "' || NOW() || '"}'::JSON;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to rollback transaction
CREATE OR REPLACE FUNCTION public.rollback_transaction()
RETURNS JSON AS $$
BEGIN
  -- In a real implementation, this would rollback the transaction
  RETURN '{"status": "transaction_rolled_back", "timestamp": "' || NOW() || '"}'::JSON;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for safe wallet balance update with validation
CREATE OR REPLACE FUNCTION public.safe_wallet_update(
  p_user_id UUID,
  p_amount DECIMAL(10,2),
  p_description TEXT,
  p_reference_type TEXT DEFAULT 'multi_level_referral',
  p_reference_id TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  v_current_balance DECIMAL(10,2);
  v_new_balance DECIMAL(10,2);
  v_transaction_id UUID;
  v_result JSON;
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL THEN
    RETURN '{"success": false, "error": "User ID is required"}'::JSON;
  END IF;
  
  IF p_amount <= 0 THEN
    RETURN '{"success": false, "error": "Amount must be positive"}'::JSON;
  END IF;
  
  IF LENGTH(p_description) < 5 THEN
    RETURN '{"success": false, "error": "Description must be at least 5 characters"}'::JSON;
  END IF;

  -- Get current balance with row locking
  SELECT wallet_balance INTO v_current_balance
  FROM public.users 
  WHERE id = p_user_id
  FOR UPDATE;
  
  IF NOT FOUND THEN
    RETURN '{"success": false, "error": "User not found"}'::JSON;
  END IF;
  
  -- Calculate new balance
  v_new_balance := COALESCE(v_current_balance, 0) + p_amount;
  
  -- Validate new balance (prevent negative balances, set reasonable limits)
  IF v_new_balance < 0 THEN
    RETURN '{"success": false, "error": "Insufficient balance"}'::JSON;
  END IF;
  
  IF v_new_balance > 1000000 THEN -- 10 lakh limit
    RETURN '{"success": false, "error": "Balance limit exceeded"}'::JSON;
  END IF;
  
  -- Update user balance
  UPDATE public.users 
  SET 
    wallet_balance = v_new_balance,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Create transaction record
  INSERT INTO public.wallet_transactions (
    user_id,
    type,
    amount,
    description,
    reference_type,
    reference_id,
    metadata,
    created_at
  ) VALUES (
    p_user_id,
    'credit',
    p_amount,
    p_description,
    p_reference_type,
    p_reference_id,
    p_metadata,
    NOW()
  ) RETURNING id INTO v_transaction_id;
  
  -- Return success result
  v_result := json_build_object(
    'success', true,
    'transaction_id', v_transaction_id,
    'previous_balance', v_current_balance,
    'new_balance', v_new_balance,
    'amount_added', p_amount,
    'timestamp', NOW()
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log error and return failure
    INSERT INTO public.referral_audit_log (
      user_id,
      action,
      details,
      created_at
    ) VALUES (
      p_user_id,
      'wallet_update_error',
      json_build_object(
        'error_message', SQLERRM,
        'error_state', SQLSTATE,
        'amount', p_amount,
        'description', p_description
      ),
      NOW()
    );
    
    RETURN json_build_object(
      'success', false,
      'error', 'Database error: ' || SQLERRM,
      'error_code', SQLSTATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for batch wallet updates (for multi-level processing)
CREATE OR REPLACE FUNCTION public.batch_wallet_updates(
  p_updates JSONB
)
RETURNS JSON AS $$
DECLARE
  v_update JSONB;
  v_result JSON;
  v_results JSONB := '[]'::JSONB;
  v_success_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_total_amount DECIMAL(10,2) := 0;
BEGIN
  -- Validate input
  IF p_updates IS NULL OR jsonb_array_length(p_updates) = 0 THEN
    RETURN '{"success": false, "error": "No updates provided"}'::JSON;
  END IF;
  
  -- Process each update
  FOR v_update IN SELECT * FROM jsonb_array_elements(p_updates)
  LOOP
    -- Call safe_wallet_update for each user
    SELECT public.safe_wallet_update(
      (v_update->>'user_id')::UUID,
      (v_update->>'amount')::DECIMAL(10,2),
      v_update->>'description',
      COALESCE(v_update->>'reference_type', 'multi_level_referral'),
      v_update->>'reference_id',
      v_update->'metadata'
    ) INTO v_result;
    
    -- Add result to results array
    v_results := v_results || jsonb_build_object(
      'user_id', v_update->>'user_id',
      'result', v_result
    );
    
    -- Update counters
    IF (v_result->>'success')::BOOLEAN THEN
      v_success_count := v_success_count + 1;
      v_total_amount := v_total_amount + (v_update->>'amount')::DECIMAL(10,2);
    ELSE
      v_error_count := v_error_count + 1;
    END IF;
  END LOOP;
  
  -- Return summary
  RETURN json_build_object(
    'success', v_error_count = 0,
    'total_updates', jsonb_array_length(p_updates),
    'successful_updates', v_success_count,
    'failed_updates', v_error_count,
    'total_amount_distributed', v_total_amount,
    'results', v_results,
    'timestamp', NOW()
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Batch processing error: ' || SQLERRM,
      'error_code', SQLSTATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.begin_transaction() TO authenticated;
GRANT EXECUTE ON FUNCTION public.commit_transaction() TO authenticated;
GRANT EXECUTE ON FUNCTION public.rollback_transaction() TO authenticated;
GRANT EXECUTE ON FUNCTION public.safe_wallet_update(UUID, DECIMAL, TEXT, TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION public.batch_wallet_updates(JSONB) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_reference 
ON public.wallet_transactions(reference_type, reference_id);

CREATE INDEX IF NOT EXISTS idx_wallet_transactions_user_type 
ON public.wallet_transactions(user_id, type, created_at);

CREATE INDEX IF NOT EXISTS idx_referral_audit_log_action 
ON public.referral_audit_log(action, created_at);

-- Add comments
COMMENT ON FUNCTION public.safe_wallet_update IS 'Safely update user wallet balance with validation and transaction logging';
COMMENT ON FUNCTION public.batch_wallet_updates IS 'Process multiple wallet updates in batch for multi-level referral distribution';
