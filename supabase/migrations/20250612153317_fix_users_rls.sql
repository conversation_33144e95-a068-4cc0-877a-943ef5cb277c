-- Switch to service role for elevated permissions
SET ROLE service_role;

-- Drop existing RLS policies related to public.users table before re-creating them
-- This ensures no conflicting policies remain from previous attempts
DROP POLICY IF EXISTS "Users can create their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read own data" ON public.users;
DROP POLICY IF EXISTS "Users can update own data" ON public.users;
DROP POLICY IF EXISTS "Admin can manage all users (eashwar)" ON public.users;
DROP POLICY IF EXISTS "Anyone can check referral codes" ON public.users; -- Keep if this is intended to be public

-- Core RLS Policies for public.users table

-- 1. Policy for authenticated users to insert their own profile
-- This is crucial for sign-up to create the profile in public.users table
CREATE POLICY "Users can create their own profile" ON public.users
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = id);

-- 2. Policy for authenticated users to read their own data
-- Allows users to fetch their own profile details after login
CREATE POLICY "Users can read own data" ON public.users
  FOR SELECT TO authenticated
  USING (auth.uid() = id);

-- 3. Policy for authenticated users to update their own data
-- Allows users to update their profile details
CREATE POLICY "Users can update own data" ON public.users
  FOR UPDATE TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- 4. Policy for public to check referral codes (assuming this was intended)
CREATE POLICY "Anyone can check referral codes" ON public.users
  FOR SELECT TO anon, authenticated
  USING (true); -- Allows public read for referral code checking

-- Admin Policy for '<EMAIL>'
-- This policy allows the specified admin email to perform ALL operations on the users table
CREATE POLICY "Admin can manage all users (eashwar)" ON public.users
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Ensure admin user profile exists (if not already from previous attempt)
-- This part is idempotent due to ON CONFLICT
INSERT INTO public.users (
  email,
  full_name,
  username,
  is_premium,
  wallet_balance
) VALUES (
  '<EMAIL>',
  'Eashwar Admin',
  'eashwar',
  true,
  0.00
) ON CONFLICT (email) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  username = EXCLUDED.username,
  is_premium = EXCLUDED.is_premium,
  wallet_balance = EXCLUDED.wallet_balance;

-- Reset role
RESET ROLE; 