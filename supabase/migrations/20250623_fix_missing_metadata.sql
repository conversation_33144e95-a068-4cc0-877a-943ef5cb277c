-- Add metadata column to wallet_transactions if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'wallet_transactions' 
        AND column_name = 'metadata'
    ) THEN
        ALTER TABLE public.wallet_transactions 
        ADD COLUMN metadata JSONB DEFAULT '{}';
    END IF;
END $$;

-- Create referral_audit_log table if not exists
CREATE TABLE IF NOT EXISTS public.referral_audit_log (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on referral_audit_log
ALTER TABLE public.referral_audit_log ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own audit logs" ON public.referral_audit_log;
DROP POLICY IF EXISTS "Admin can manage all audit logs" ON public.referral_audit_log;

-- Create policies for referral_audit_log
CREATE POLICY "Users can view their own audit logs" ON public.referral_audit_log
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Admin can manage all audit logs" ON public.referral_audit_log
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_referral_audit_log_user_action 
ON public.referral_audit_log(user_id, action, created_at);

-- Add helpful comment
COMMENT ON TABLE public.referral_audit_log IS 'Audit log for referral-related actions and events'; 