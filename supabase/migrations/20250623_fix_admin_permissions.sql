-- Switch to postgres role for elevated permissions
SET ROLE postgres;

-- Create admin role if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'admin') THEN
    CREATE ROLE admin;
  END IF;
END $$;

-- Grant admin role all necessary permissions
GRANT USAGE ON SCHEMA public TO admin;
GRANT ALL ON ALL TABLES IN SCHEMA public TO admin;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO admin;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO admin;

-- Switch to service_role for user management
SET ROLE service_role;

-- Update <EMAIL> to have admin role and metadata
DO $$ 
BEGIN
  -- Get the auth.<NAME_EMAIL>
  WITH admin_user AS (
    SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1
  )
  UPDATE auth.users
  SET 
    raw_app_meta_data = 
      COALESCE(raw_app_meta_data, '{}'::jsonb) || 
      '{"is_admin": true, "roles": ["admin"]}'::jsonb,
    raw_user_meta_data = 
      COALESCE(raw_user_meta_data, '{}'::jsonb) || 
      '{"is_admin": true}'::jsonb
  FROM admin_user
  WHERE auth.users.id = admin_user.id;

  -- Also grant admin role to the user
  EXECUTE format(
    'GRANT admin TO %I',
    (SELECT email FROM auth.users WHERE email = '<EMAIL>')
  );
END $$;

-- Create admin-specific policies
DO $$ 
BEGIN
  -- Drop existing admin policies if they exist
  DROP POLICY IF EXISTS "Admin full access" ON public.users;
  
  -- Create new admin policy
  CREATE POLICY "Admin full access" ON public.users
    FOR ALL TO admin
    USING (true)
    WITH CHECK (true);
END $$;

-- Reset role back to the default
RESET ROLE; 