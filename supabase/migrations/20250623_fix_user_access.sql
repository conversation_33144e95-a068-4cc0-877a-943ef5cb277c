-- Switch to postgres role for elevated permissions
SET ROLE postgres;

-- Grant necessary permissions to authenticated role
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Switch to service_role for policy management
SET ROLE service_role;

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can read own data" ON public.users;
DROP POLICY IF EXISTS "Users can update own data" ON public.users;
DROP POLICY IF EXISTS "Users can create their own profile" ON public.users;
DROP POLICY IF EXISTS "Admin can manage all users (eashwar)" ON public.users;
DROP POLICY IF EXISTS "Anyone can check referral codes" ON public.users;

-- Create new, more permissive policies

-- 1. Allow users to read their own data
CREATE POLICY "Users can read own data" ON public.users
    FOR SELECT TO authenticated
    USING (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_app_meta_data->>'is_admin' = 'true'
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- 2. Allow users to update their own data
CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE TO authenticated
    USING (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_app_meta_data->>'is_admin' = 'true'
                OR auth.users.email = '<EMAIL>'
            )
        )
    )
    WITH CHECK (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_app_meta_data->>'is_admin' = 'true'
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- 3. Allow users to create their own profile
CREATE POLICY "Users can create their own profile" ON public.users
    FOR INSERT TO authenticated
    WITH CHECK (
        auth.uid() = id 
        OR EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_app_meta_data->>'is_admin' = 'true'
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- 4. Allow public access for referral code checking
CREATE POLICY "Anyone can check referral codes" ON public.users
    FOR SELECT TO anon, authenticated
    USING (true);

-- Ensure RLS is enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Reset role back to the default
RESET ROLE; 