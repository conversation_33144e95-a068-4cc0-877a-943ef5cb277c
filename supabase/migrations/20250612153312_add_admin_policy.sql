-- Ensure unique constraints exist
ALTER TABLE auth.users ADD CONSTRAINT users_email_key UNIQUE (email);
ALTER TABLE public.users ADD CONSTRAINT users_email_key UNIQUE (email);

-- Switch to service role
SET ROLE service_role;

-- Add admin policies
CREATE POLICY "Admin can manage all users" ON users
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all products" ON products
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all categories" ON categories
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all orders" ON orders
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all order items" ON order_items
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all wallet transactions" ON wallet_transactions
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all referrals" ON referrals
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all referral settings" ON referral_settings
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Admin can manage all reviews" ON reviews
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Create admin user profile
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE email = '<EMAIL>') THEN
    INSERT INTO public.users (
      email,
      full_name,
      username,
      is_premium,
      wallet_balance
    ) VALUES (
      '<EMAIL>',
      'Admin User',
      'admin',
      true,
      0.00
    );
  END IF;
END $$;

-- Reset role
RESET ROLE; 