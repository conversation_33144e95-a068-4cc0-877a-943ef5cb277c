-- Fix foreign key cascade behavior for referral deletions
-- This allows proper cleanup when referrals are deleted

-- Switch to service role for elevated permissions
SET ROLE service_role;

-- Drop the existing foreign key constraint
ALTER TABLE wallet_transactions 
DROP CONSTRAINT IF EXISTS wallet_transactions_referral_id_fkey;

-- Recreate the foreign key constraint with CASCADE behavior
-- This will automatically delete related wallet_transactions when a referral is deleted
ALTER TABLE wallet_transactions 
ADD CONSTRAINT wallet_transactions_referral_id_fkey 
FOREIGN KEY (referral_id) 
REFERENCES referrals(id) 
ON DELETE CASCADE;

-- Also check and fix other referral-related foreign keys if needed

-- Check if referrals table has proper cascade behavior for user deletions
-- Drop and recreate referrer_id foreign key with CASCADE
ALTER TABLE referrals 
DROP CONSTRAINT IF EXISTS referrals_referrer_id_fkey;

ALTER TABLE referrals 
ADD CONSTRAINT referrals_referrer_id_fkey 
FOREIGN KEY (referrer_id) 
REFERENCES users(id) 
ON DELETE CASCADE;

-- Drop and recreate referred_user_id foreign key with CASCADE
ALTER TABLE referrals 
DROP CONSTRAINT IF EXISTS referrals_referred_user_id_fkey;

ALTER TABLE referrals 
ADD CONSTRAINT referrals_referred_user_id_fkey 
FOREIGN KEY (referred_user_id) 
REFERENCES users(id) 
ON DELETE CASCADE;

-- Also fix referral_network table if it exists
-- Check if referral_network table exists and fix its constraints
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'referral_network') THEN
        -- Fix referrer_id constraint
        ALTER TABLE referral_network 
        DROP CONSTRAINT IF EXISTS referral_network_referrer_id_fkey;
        
        ALTER TABLE referral_network 
        ADD CONSTRAINT referral_network_referrer_id_fkey 
        FOREIGN KEY (referrer_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE;
        
        -- Fix referred_user_id constraint
        ALTER TABLE referral_network 
        DROP CONSTRAINT IF EXISTS referral_network_referred_user_id_fkey;
        
        ALTER TABLE referral_network 
        ADD CONSTRAINT referral_network_referred_user_id_fkey 
        FOREIGN KEY (referred_user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE;
    END IF;
END $$;

-- Fix premium_subscriptions table if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'premium_subscriptions') THEN
        -- Fix user_id constraint
        ALTER TABLE premium_subscriptions 
        DROP CONSTRAINT IF EXISTS premium_subscriptions_user_id_fkey;
        
        ALTER TABLE premium_subscriptions 
        ADD CONSTRAINT premium_subscriptions_user_id_fkey 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE;
    END IF;
END $$;

-- Fix referral_audit_log table if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'referral_audit_log') THEN
        -- Fix user_id constraint
        ALTER TABLE referral_audit_log 
        DROP CONSTRAINT IF EXISTS referral_audit_log_user_id_fkey;
        
        ALTER TABLE referral_audit_log 
        ADD CONSTRAINT referral_audit_log_user_id_fkey 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE;
        
        -- Fix referral_id constraint if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'referral_audit_log' 
            AND column_name = 'referral_id'
        ) THEN
            ALTER TABLE referral_audit_log 
            DROP CONSTRAINT IF EXISTS referral_audit_log_referral_id_fkey;
            
            ALTER TABLE referral_audit_log 
            ADD CONSTRAINT referral_audit_log_referral_id_fkey 
            FOREIGN KEY (referral_id) 
            REFERENCES referrals(id) 
            ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- Reset role
RESET ROLE;

-- Add helpful comment
COMMENT ON CONSTRAINT wallet_transactions_referral_id_fkey ON wallet_transactions IS 
'Foreign key with CASCADE delete - wallet transactions are automatically deleted when referral is deleted';

COMMENT ON CONSTRAINT referrals_referrer_id_fkey ON referrals IS 
'Foreign key with CASCADE delete - referrals are automatically deleted when referrer user is deleted';

COMMENT ON CONSTRAINT referrals_referred_user_id_fkey ON referrals IS 
'Foreign key with CASCADE delete - referrals are automatically deleted when referred user is deleted';
