-- Switch to service role for elevated permissions
SET ROLE service_role;

-- Drop existing RLS policies for products table to ensure a clean slate
DROP POLICY IF EXISTS "Products are viewable by everyone" ON public.products;
DROP POLICY IF EXISTS "Authenticated users can create product" ON public.products; -- If you had this
DROP POLICY IF EXISTS "Anon users can insert products" ON public.products; -- If you had this from earlier attempts

-- Core RLS Policies for public.products table

-- 1. Allow all users (anon and authenticated) to view products
CREATE POLICY "Products are viewable by everyone" ON public.products
  FOR SELECT TO anon, authenticated
  USING (true);

-- 2. Allow authenticated users to create products (for when logged in as admin)
CREATE POLICY "Authenticated users can create products" ON public.products
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- 3. Allow anon users to create products (for direct access to Add Product page for development)
CREATE POLICY "Anon users can insert products" ON public.products
  FOR INSERT TO anon
  WITH CHECK (true);

-- Drop existing RLS policies for storage.objects for product images
DROP POLICY IF EXISTS "Authenticated users can upload product images" ON storage.objects;
DROP POLICY IF EXISTS "Anon users can upload product images" ON storage.objects;

-- Allow authenticated users to upload product images to the 'products' bucket
CREATE POLICY "Authenticated users can upload product images" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (bucket_id = 'products');

-- Allow anon users to upload product images to the 'products' bucket (for direct access to Add Product page)
CREATE POLICY "Anon users can upload product images" ON storage.objects
  FOR INSERT TO anon
  WITH CHECK (bucket_id = 'products');

-- Reset role
RESET ROLE; 