-- Switch to service role for elevated permissions
SET ROLE service_role;

-- Drop existing RLS policies related to users table before re-creating them
DROP POLICY IF EXISTS "Users can create their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read own data" ON public.users;
DROP POLICY IF EXISTS "Users can update own data" ON public.users;
DROP POLICY IF EXISTS "Admin can manage all users (eashwar)" ON public.users;

-- Corrected policies for public.users

-- Policy for authenticated users to insert their own profile
CREATE POLICY "Users can create their own profile" ON public.users
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = id);

-- Policy for authenticated users to read their own data
CREATE POLICY "Users can read own data" ON public.users
  FOR SELECT TO authenticated
  USING (auth.uid() = id);

-- Policy for authenticated users to update their own data
CREATE POLICY "Users can update own data" ON public.users
  FOR UPDATE TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Admin <NAME_EMAIL>
CREATE POLICY "Admin can manage all users (eashwar)" ON public.users
  FOR ALL TO authenticated
  USING (auth.jwt() ->> 'email' = '<EMAIL>');

-- Create admin user profile (if not already exists from previous attempt)
INSERT INTO public.users (
  email,
  full_name,
  username,
  is_premium,
  wallet_balance
) VALUES (
  '<EMAIL>',
  'Eashwar Admin',
  'eashwar',
  true,
  0.00
) ON CONFLICT (email) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  username = EXCLUDED.username,
  is_premium = EXCLUDED.is_premium,
  wallet_balance = EXCLUDED.wallet_balance;

-- Reset role
RESET ROLE; 