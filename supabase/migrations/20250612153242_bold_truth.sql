/*
  # Complete HerbalStore Database Schema

  1. New Tables
    - `users` - User accounts with premium membership and wallet
    - `categories` - Product categories and subcategories  
    - `products` - Herbal products with images and details
    - `orders` - Customer orders with payment tracking
    - `order_items` - Individual items within orders
    - `wallet_transactions` - Wallet credit/debit history
    - `referrals` - Referral program tracking
    - `referral_settings` - Configurable referral bonuses
    - `reviews` - Product reviews and ratings

  2. Security
    - Enable RLS on all tables
    - Add policies for user data access
    - Admin-only access for management tables

  3. Storage
    - Create bucket for product images
    - Set up public access policies
*/

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  full_name text NOT NULL DEFAULT '',
  phone text,
  address text,
  username text UNIQUE,
  is_premium boolean DEFAULT false,
  premium_expires_at timestamptz,
  wallet_balance decimal(10,2) DEFAULT 0.00,
  referral_code text UNIQUE,
  referred_by text REFERENCES users(referral_code),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  image_url text,
  parent_id uuid REFERENCES categories(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Products table  
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  price decimal(10,2) NOT NULL,
  original_price decimal(10,2),
  image_url text NOT NULL,
  images text[] DEFAULT '{}',
  category text NOT NULL,
  ingredients text[] DEFAULT '{}',
  benefits text[] DEFAULT '{}',
  dosage text,
  warnings text,
  rating decimal(3,2) DEFAULT 0.00,
  reviews_count integer DEFAULT 0,
  stock_quantity integer DEFAULT 0,
  is_premium_only boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id),
  total_amount decimal(10,2) NOT NULL,
  wallet_used decimal(10,2) DEFAULT 0.00,
  payment_method text NOT NULL CHECK (payment_method IN ('wallet', 'card', 'mixed')),
  delivery_address text NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'shipped', 'delivered', 'cancelled')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id uuid NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  product_id uuid NOT NULL REFERENCES products(id),
  quantity integer NOT NULL DEFAULT 1,
  price decimal(10,2) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Wallet transactions table
CREATE TABLE IF NOT EXISTS wallet_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id),
  type text NOT NULL CHECK (type IN ('credit', 'debit')),
  amount decimal(10,2) NOT NULL,
  description text NOT NULL,
  reference_type text CHECK (reference_type IN ('order', 'referral', 'admin', 'payment')),
  reference_id text,
  created_at timestamptz DEFAULT now()
);

-- Referrals table
CREATE TABLE IF NOT EXISTS referrals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id uuid NOT NULL REFERENCES users(id),
  referred_user_id uuid NOT NULL REFERENCES users(id),
  bonus_amount decimal(10,2) NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'completed')),
  created_at timestamptz DEFAULT now()
);

-- Referral settings table
CREATE TABLE IF NOT EXISTS referral_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_bonus decimal(10,2) DEFAULT 100.00,
  referred_bonus decimal(10,2) DEFAULT 50.00,
  max_referrals_per_user integer DEFAULT 10,
  updated_at timestamptz DEFAULT now()
);

-- Reviews table
CREATE TABLE IF NOT EXISTS reviews (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid NOT NULL REFERENCES products(id),
  user_id uuid NOT NULL REFERENCES users(id),
  rating integer NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment text,
  created_at timestamptz DEFAULT now(),
  UNIQUE(product_id, user_id)
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can read own data" ON users
  FOR SELECT TO authenticated
  USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own data" ON users
  FOR UPDATE TO authenticated
  USING (auth.uid()::text = id::text);

-- Categories policies (public read)
CREATE POLICY "Categories are viewable by everyone" ON categories
  FOR SELECT TO authenticated, anon
  USING (true);

-- Products policies (public read)
CREATE POLICY "Products are viewable by everyone" ON products
  FOR SELECT TO authenticated, anon
  USING (true);

-- Orders policies
CREATE POLICY "Users can read own orders" ON orders
  FOR SELECT TO authenticated
  USING (user_id::text = auth.uid()::text);

CREATE POLICY "Users can create own orders" ON orders
  FOR INSERT TO authenticated
  WITH CHECK (user_id::text = auth.uid()::text);

-- Order items policies
CREATE POLICY "Users can read own order items" ON order_items
  FOR SELECT TO authenticated
  USING (EXISTS (
    SELECT 1 FROM orders 
    WHERE orders.id = order_items.order_id 
    AND orders.user_id::text = auth.uid()::text
  ));

-- Wallet transactions policies
CREATE POLICY "Users can read own wallet transactions" ON wallet_transactions
  FOR SELECT TO authenticated
  USING (user_id::text = auth.uid()::text);

-- Referrals policies
CREATE POLICY "Users can read own referrals" ON referrals
  FOR SELECT TO authenticated
  USING (referrer_id::text = auth.uid()::text OR referred_user_id::text = auth.uid()::text);

-- Referral settings policies (public read)
CREATE POLICY "Referral settings are viewable by everyone" ON referral_settings
  FOR SELECT TO authenticated, anon
  USING (true);

-- Reviews policies
CREATE POLICY "Reviews are viewable by everyone" ON reviews
  FOR SELECT TO authenticated, anon
  USING (true);

CREATE POLICY "Users can create own reviews" ON reviews
  FOR INSERT TO authenticated
  WITH CHECK (user_id::text = auth.uid()::text);

-- Functions for wallet operations
CREATE OR REPLACE FUNCTION add_wallet_balance(user_id uuid, amount decimal)
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET wallet_balance = wallet_balance + amount,
      updated_at = now()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update product rating
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS trigger AS $$
BEGIN
  UPDATE products 
  SET 
    rating = (
      SELECT COALESCE(AVG(rating), 0) 
      FROM reviews 
      WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
    ),
    reviews_count = (
      SELECT COUNT(*) 
      FROM reviews 
      WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
    ),
    updated_at = now()
  WHERE id = COALESCE(NEW.product_id, OLD.product_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers
CREATE TRIGGER update_product_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_product_rating();

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers to tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default referral settings
INSERT INTO referral_settings (referrer_bonus, referred_bonus, max_referrals_per_user)
VALUES (100.00, 50.00, 10)
ON CONFLICT DO NOTHING;

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
  ('Immunity', 'Products to boost immune system'),
  ('Digestion', 'Digestive health supplements'),
  ('Energy', 'Natural energy boosters'),
  ('Wellness', 'General wellness products'),
  ('Skincare', 'Natural skincare solutions')
ON CONFLICT DO NOTHING;