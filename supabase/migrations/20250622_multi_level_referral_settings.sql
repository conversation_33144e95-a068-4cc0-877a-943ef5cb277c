-- =============================================
-- MULTI-LEVEL REFERRAL SYSTEM CONFIGURATION
-- Add admin settings for 10-level referral system
-- =============================================

-- Insert multi-level referral system settings
INSERT INTO public.admin_settings (setting_key, setting_value, description, category, created_at, updated_at)
VALUES 
  -- System control settings
  ('multi_level_referral_enabled', 'true', 'Enable/disable the multi-level referral system', 'referral_system', NOW(), NOW()),
  ('max_referral_levels', '10', 'Maximum number of referral levels to process', 'referral_system', NOW(), NOW()),
  
  -- Level-specific bonus amounts (INR)
  ('referral_level_1_bonus', '250.00', 'Bonus amount for Level 1 (direct referrer)', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_2_bonus', '100.00', 'Bonus amount for Level 2 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_3_bonus', '50.00', 'Bonus amount for Level 3 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_4_bonus', '25.00', 'Bonus amount for Level 4 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_5_bonus', '10.00', 'Bonus amount for Level 5 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_6_bonus', '5.00', 'Bonus amount for Level 6 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_7_bonus', '2.00', 'Bonus amount for Level 7 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_8_bonus', '1.00', 'Bonus amount for Level 8 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_9_bonus', '0.50', 'Bonus amount for Level 9 referrer', 'referral_bonuses', NOW(), NOW()),
  ('referral_level_10_bonus', '0.25', 'Bonus amount for Level 10 referrer', 'referral_bonuses', NOW(), NOW()),
  
  -- E-wallet unlock settings
  ('ewallet_unlock_threshold', '3', 'Number of premium referrals required to unlock e-wallet', 'wallet_system', NOW(), NOW()),
  
  -- System performance settings
  ('referral_processing_timeout', '30', 'Timeout in seconds for referral processing', 'referral_system', NOW(), NOW()),
  ('max_concurrent_referral_processing', '5', 'Maximum concurrent referral processing operations', 'referral_system', NOW(), NOW())

ON CONFLICT (setting_key) DO UPDATE SET
  setting_value = EXCLUDED.setting_value,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  updated_at = NOW();

-- Create index for faster lookups of referral settings
CREATE INDEX IF NOT EXISTS idx_admin_settings_referral_category 
ON public.admin_settings(category) 
WHERE category IN ('referral_system', 'referral_bonuses', 'wallet_system');

-- Create index for referral level bonus lookups
CREATE INDEX IF NOT EXISTS idx_admin_settings_referral_level_bonus 
ON public.admin_settings(setting_key) 
WHERE setting_key LIKE 'referral_level_%_bonus';

-- Add helpful comments
COMMENT ON TABLE public.admin_settings IS 'System configuration settings including multi-level referral system parameters';

-- Create a view for easy access to referral settings
CREATE OR REPLACE VIEW public.referral_system_settings AS
SELECT 
  setting_key,
  setting_value,
  description,
  CASE 
    WHEN setting_key LIKE 'referral_level_%_bonus' THEN 
      CAST(SUBSTRING(setting_key FROM 'referral_level_(\d+)_bonus') AS INTEGER)
    ELSE NULL 
  END as referral_level,
  CASE 
    WHEN setting_key LIKE 'referral_level_%_bonus' THEN 
      CAST(setting_value AS DECIMAL(10,2))
    ELSE NULL 
  END as bonus_amount,
  updated_at
FROM public.admin_settings 
WHERE category IN ('referral_system', 'referral_bonuses', 'wallet_system')
ORDER BY 
  CASE 
    WHEN setting_key LIKE 'referral_level_%_bonus' THEN 
      CAST(SUBSTRING(setting_key FROM 'referral_level_(\d+)_bonus') AS INTEGER)
    ELSE 999 
  END,
  setting_key;

-- Grant permissions
GRANT SELECT ON public.referral_system_settings TO authenticated, anon;

-- Create function to get all referral level bonuses as JSON
CREATE OR REPLACE FUNCTION public.get_referral_level_bonuses()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_object_agg(
    CAST(SUBSTRING(setting_key FROM 'referral_level_(\d+)_bonus') AS INTEGER),
    CAST(setting_value AS DECIMAL(10,2))
  )
  INTO result
  FROM public.admin_settings 
  WHERE setting_key LIKE 'referral_level_%_bonus'
  AND SUBSTRING(setting_key FROM 'referral_level_(\d+)_bonus') ~ '^\d+$';
  
  RETURN COALESCE(result, '{}'::JSON);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_referral_level_bonuses() TO authenticated, anon;

-- Create function to update referral level bonus
CREATE OR REPLACE FUNCTION public.update_referral_level_bonus(
  p_level INTEGER,
  p_amount DECIMAL(10,2)
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Validate level
  IF p_level < 1 OR p_level > 20 THEN
    RAISE EXCEPTION 'Referral level must be between 1 and 20';
  END IF;
  
  -- Validate amount
  IF p_amount < 0 THEN
    RAISE EXCEPTION 'Bonus amount cannot be negative';
  END IF;
  
  -- Update or insert the setting
  INSERT INTO public.admin_settings (setting_key, setting_value, description, category, created_at, updated_at)
  VALUES (
    'referral_level_' || p_level || '_bonus',
    p_amount::TEXT,
    'Bonus amount for Level ' || p_level || ' referrer',
    'referral_bonuses',
    NOW(),
    NOW()
  )
  ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();
    
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission (restrict to authenticated users only)
GRANT EXECUTE ON FUNCTION public.update_referral_level_bonus(INTEGER, DECIMAL) TO authenticated;

-- Add RLS policies for admin settings if not exists
DO $$ 
BEGIN
  -- Allow all users to read admin settings
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'admin_settings' 
    AND policyname = 'Allow read access to admin settings'
  ) THEN
    CREATE POLICY "Allow read access to admin settings" ON public.admin_settings
      FOR SELECT USING (true);
  END IF;
  
  -- Allow only authenticated users to modify admin settings
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'admin_settings' 
    AND policyname = 'Allow authenticated users to modify admin settings'
  ) THEN
    CREATE POLICY "Allow authenticated users to modify admin settings" ON public.admin_settings
      FOR ALL USING (auth.role() = 'authenticated');
  END IF;
END $$;
