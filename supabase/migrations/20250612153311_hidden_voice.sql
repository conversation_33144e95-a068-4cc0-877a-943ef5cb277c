/*
  # Create Storage Buckets for Product Images

  1. Storage Buckets
    - `products` - For product images
    - `categories` - For category images

  2. Storage Policies
    - Public read access for product images
    - Authenticated upload access for admin users
*/

-- Create storage bucket for product images
INSERT INTO storage.buckets (id, name, public)
VALUES ('products', 'products', true)
ON CONFLICT DO NOTHING;

-- Create storage bucket for category images  
INSERT INTO storage.buckets (id, name, public)
VALUES ('categories', 'categories', true)
ON CONFLICT DO NOTHING;

-- Allow public read access to product images
CREATE POLICY "Public read access for product images" ON storage.objects
  FOR SELECT TO public
  USING (bucket_id = 'products');

-- Allow authenticated users to upload product images
CREATE POLICY "Authenticated users can upload product images" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (bucket_id = 'products');

-- Allow authenticated users to update product images
CREATE POLICY "Authenticated users can update product images" ON storage.objects
  FOR UPDATE TO authenticated
  USING (bucket_id = 'products');

-- Allow authenticated users to delete product images
CREATE POLICY "Authenticated users can delete product images" ON storage.objects
  FOR DELETE TO authenticated
  USING (bucket_id = 'products');

-- Allow public read access to category images
CREATE POLICY "Public read access for category images" ON storage.objects
  FOR SELECT TO public
  USING (bucket_id = 'categories');

-- Allow authenticated users to upload category images
CREATE POLICY "Authenticated users can upload category images" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (bucket_id = 'categories');