-- Drop ALL existing policies on referrals table
DO $$ 
BEGIN
    -- Drop all policies from the referrals table
    DROP POLICY IF EXISTS "Users can view their referrals" ON public.referrals;
    DROP POLICY IF EXISTS "Users can create referrals" ON public.referrals;
    DROP POLICY IF EXISTS "Users can update their referrals" ON public.referrals;
    DROP POLICY IF EXISTS "Users can view their own referrals" ON public.referrals;
    DROP POLICY IF EXISTS "Users can insert their own referrals" ON public.referrals;
    DROP POLICY IF EXISTS "Admin can manage all referrals" ON public.referrals;
    DROP POLICY IF EXISTS "Enable read access for all users" ON public.referrals;
    DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON public.referrals;
    DROP POLICY IF EXISTS "Enable update access for authenticated users" ON public.referrals;
END $$;

-- Ensure RLS is enabled
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;

-- Create new policies with IF NOT EXISTS
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'referrals' AND policyname = 'Users can view their referrals'
    ) THEN
        CREATE POLICY "Users can view their referrals" ON public.referrals
            FOR SELECT 
            USING (
                auth.uid() = referrer_id 
                OR 
                auth.uid() = referred_user_id
                OR
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() 
                    AND (
                        email = '<EMAIL>' 
                        OR 
                        email = '<EMAIL>'
                    )
                )
            );
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'referrals' AND policyname = 'Users can create referrals'
    ) THEN
        CREATE POLICY "Users can create referrals" ON public.referrals
            FOR INSERT 
            WITH CHECK (
                auth.uid() = referrer_id
                OR
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() 
                    AND (
                        email = '<EMAIL>' 
                        OR 
                        email = '<EMAIL>'
                    )
                )
            );
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'referrals' AND policyname = 'Users can update their referrals'
    ) THEN
        CREATE POLICY "Users can update their referrals" ON public.referrals
            FOR UPDATE
            USING (
                auth.uid() = referrer_id
                OR
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() 
                    AND (
                        email = '<EMAIL>' 
                        OR 
                        email = '<EMAIL>'
                    )
                )
            )
            WITH CHECK (
                auth.uid() = referrer_id
                OR
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() 
                    AND (
                        email = '<EMAIL>' 
                        OR 
                        email = '<EMAIL>'
                    )
                )
            );
    END IF;
END $$; 