{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/cursoranimation.tsx", "./src/components/testingdashboard.tsx", "./src/components/admin/addproductform.tsx", "./src/components/admin/bonussettings.tsx", "./src/components/admin/inventorymanagement.tsx", "./src/components/admin/ordermanagement.tsx", "./src/components/admin/pdfexport.tsx", "./src/components/admin/premiumstatusanalytics.tsx", "./src/components/admin/referraltreevisualization.tsx", "./src/components/admin/securitymonitoring.tsx", "./src/components/admin/usermanagement.tsx", "./src/components/auth/adminguard.tsx", "./src/components/cart/cartslideout.tsx", "./src/components/layout/applayout.tsx", "./src/components/layout/footer.tsx", "./src/components/layout/header.tsx", "./src/components/products/productcard.tsx", "./src/components/products/productfilters.tsx", "./src/components/referral/referralnetworkvisualization.tsx", "./src/components/referral/userreferraldashboard.tsx", "./src/components/ui/productimage.tsx", "./src/components/wallet/walletdashboard.tsx", "./src/config/supabase.ts", "./src/hooks/useadminnotifications.ts", "./src/hooks/usereferralregistration.ts", "./src/hooks/usesupabaserealtime.ts", "./src/lib/adminsupabase.ts", "./src/lib/supabase.ts", "./src/pages/aboutpage.tsx", "./src/pages/addproductpage.tsx", "./src/pages/cartpage.tsx", "./src/pages/checkoutpage.tsx", "./src/pages/contactpage.tsx", "./src/pages/homepage.tsx", "./src/pages/premiumpage.tsx", "./src/pages/productdetailpage.tsx", "./src/pages/productspage.tsx", "./src/pages/walletpage.tsx", "./src/pages/admin/addproductpage.tsx", "./src/pages/admin/adminbonussettingspage.tsx", "./src/pages/admin/admindashboard.tsx", "./src/pages/admin/adminkycpage.tsx", "./src/pages/admin/adminmultilevelreferralpage.tsx", "./src/pages/admin/adminnotificationtestpage.tsx", "./src/pages/admin/adminorderspage.tsx", "./src/pages/admin/adminproductspage.tsx", "./src/pages/admin/adminreferralcodespage.tsx", "./src/pages/admin/adminreferraltreepage.tsx", "./src/pages/admin/adminsettingspage.tsx", "./src/pages/admin/adminstatspage.tsx", "./src/pages/admin/adminuserspage.tsx", "./src/pages/admin/adminwalletpage.tsx", "./src/pages/auth/forgotpasswordpage.tsx", "./src/pages/auth/loginpage.tsx", "./src/pages/auth/registerpage.tsx", "./src/pages/auth/resetpasswordpage.tsx", "./src/pages/dashboard/dashboardoverview.tsx", "./src/pages/dashboard/dashboardpage.tsx", "./src/pages/dashboard/enhancedreferralpage.tsx", "./src/pages/dashboard/kycpage.tsx", "./src/pages/dashboard/orderspage.tsx", "./src/pages/dashboard/premiumpage.tsx", "./src/pages/dashboard/profilepage.tsx", "./src/pages/dashboard/referralpage.tsx", "./src/pages/dashboard/walletpage.tsx", "./src/scripts/applymultilevelreferralmigrations.ts", "./src/scripts/fix_user_sync.ts", "./src/services/adminnotificationservice.ts", "./src/services/authfixservice.ts", "./src/services/automaticreferralmonitor.ts", "./src/services/emailservice.ts", "./src/services/multilevelreferralservice.ts", "./src/services/otpservice.ts", "./src/services/passwordresetservice.ts", "./src/services/premiumservice.ts", "./src/services/referralcodegenerator.ts", "./src/services/referralservice.ts", "./src/services/referraltreeservice.ts", "./src/services/smsservice.ts", "./src/services/supabaseedgefunctions.ts", "./src/services/walletservice.ts", "./src/stores/authstore.ts", "./src/stores/cartstore.ts", "./src/stores/productstore.ts", "./src/stores/referralstore.ts", "./src/stores/walletstore.ts", "./src/types/auth.ts", "./src/types/index.ts", "./src/types/user.ts", "./src/utils/authutils.ts", "./src/utils/fixpraveenauth.ts", "./src/utils/formatcurrency.ts", "./src/utils/imageutils.ts", "./src/utils/walletutils.ts"], "errors": true, "version": "5.6.3"}