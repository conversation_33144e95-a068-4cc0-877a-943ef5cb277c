// Test Admin Referral Tree Functionality
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🌳 Testing Admin Referral Tree Functionality');
console.log('===========================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAdminReferralTree() {
  try {
    console.log('1️⃣ TESTING NETWORK OVERVIEW');
    console.log('─'.repeat(40));

    // Test the simplified network overview
    const { data: userStats, error: userStatsError } = await supabase
      .from('users')
      .select('id, is_premium, wallet_balance');

    if (userStatsError) {
      console.log('❌ User stats error:', userStatsError.message);
      return false;
    }

    const totalUsers = userStats?.length || 0;
    const totalPremiumUsers = userStats?.filter(u => u.is_premium).length || 0;

    console.log(`✅ Total users: ${totalUsers}`);
    console.log(`✅ Premium users: ${totalPremiumUsers}`);

    // Get total earnings
    const { data: earnings, error: earningsError } = await supabase
      .from('wallet_transactions')
      .select('amount')
      .eq('reference_type', 'referral');

    const totalEarnings = earnings?.reduce((sum, t) => sum + parseFloat(t.amount), 0) || 0;
    console.log(`✅ Total earnings: ₹${totalEarnings.toFixed(2)}`);

    // Get root users
    const { data: rootUsers, error: rootError } = await supabase
      .from('users')
      .select('id, email, full_name')
      .or('referred_by.is.null,referred_by.eq.')
      .limit(5);

    const totalNetworks = rootUsers?.length || 0;
    console.log(`✅ Total networks: ${totalNetworks}`);

    if (rootUsers && rootUsers.length > 0) {
      console.log('   Root users:');
      rootUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
      });
    }

    console.log('\n2️⃣ TESTING USER SEARCH');
    console.log('─'.repeat(40));

    // Test search with a common term
    const searchQuery = 'test';
    console.log(`🔍 Searching for: "${searchQuery}"`);

    const { data: searchResults, error: searchError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        referral_code,
        is_premium,
        wallet_balance
      `)
      .or(`email.ilike.%${searchQuery}%,full_name.ilike.%${searchQuery}%,referral_code.ilike.%${searchQuery}%`)
      .limit(10);

    if (searchError) {
      console.log('❌ Search error:', searchError.message);
      return false;
    }

    console.log(`✅ Search results: ${searchResults?.length || 0} users found`);
    
    if (searchResults && searchResults.length > 0) {
      console.log('   Search results:');
      searchResults.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email}) - ${user.referral_code}`);
      });

      // Test getting referrals for first search result
      const testUser = searchResults[0];
      console.log(`\n📝 Testing referrals for: ${testUser.full_name}`);

      const { data: userReferrals, error: refError } = await supabase
        .from('referrals')
        .select(`
          id,
          referral_order,
          referred_user:users!referrals_referred_user_id_fkey(
            id,
            email,
            full_name,
            is_premium
          )
        `)
        .eq('referrer_id', testUser.id);

      if (refError) {
        console.log('❌ Referrals query error:', refError.message);
      } else {
        console.log(`✅ Direct referrals: ${userReferrals?.length || 0}`);
        userReferrals?.forEach((ref, index) => {
          if (ref.referred_user) {
            console.log(`   ${index + 1}. ${ref.referred_user.full_name} (Order: ${ref.referral_order})`);
          }
        });
      }
    }

    console.log('\n3️⃣ TESTING TREE BUILDING');
    console.log('─'.repeat(40));

    if (userStats && userStats.length > 0) {
      // Find a user with referrals
      const { data: usersWithReferrals, error: uwrError } = await supabase
        .from('referrals')
        .select(`
          referrer_id,
          referrer:users!referrals_referrer_id_fkey(
            id,
            email,
            full_name
          )
        `)
        .limit(1);

      if (uwrError) {
        console.log('❌ Users with referrals error:', uwrError.message);
      } else if (usersWithReferrals && usersWithReferrals.length > 0) {
        const testUser = usersWithReferrals[0].referrer;
        console.log(`📝 Building tree for: ${testUser.full_name}`);

        // Get user data
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select(`
            id,
            email,
            full_name,
            referral_code,
            is_premium,
            wallet_balance,
            premium_referral_count,
            ewallet_unlocked,
            created_at,
            premium_purchased_at
          `)
          .eq('id', testUser.id)
          .single();

        if (userError) {
          console.log('❌ User data error:', userError.message);
        } else {
          console.log('✅ User data retrieved successfully');
          console.log(`   Name: ${userData.full_name}`);
          console.log(`   Email: ${userData.email}`);
          console.log(`   Premium: ${userData.is_premium}`);
          console.log(`   Wallet: ₹${userData.wallet_balance || 0}`);
        }
      } else {
        console.log('ℹ️ No users with referrals found for tree testing');
      }
    }

    console.log('\n4️⃣ TESTING ADMIN PANEL COMPATIBILITY');
    console.log('─'.repeat(40));

    // Test the exact query structure used by the admin panel
    const { data: adminCompatTest, error: actError } = await supabase
      .from('users')
      .select('id, is_premium, wallet_balance');

    if (actError) {
      console.log('❌ Admin compatibility error:', actError.message);
      return false;
    }

    console.log('✅ Admin panel query structure working');
    console.log(`   Compatible with ${adminCompatTest?.length || 0} users`);

    console.log('\n🎉 ADMIN REFERRAL TREE TEST SUMMARY');
    console.log('═'.repeat(50));
    console.log('✅ Network overview queries working');
    console.log('✅ User search functionality working');
    console.log('✅ Referral relationships accessible');
    console.log('✅ Tree building data available');
    console.log('✅ Admin panel compatibility confirmed');
    console.log('');
    console.log('🔗 Admin Referral Tree URL: http://localhost:5173/admin/dashboard/referral-tree');
    console.log('');
    console.log('📱 Admin can now:');
    console.log('   • View network overview statistics');
    console.log('   • Search for users by name, email, or referral code');
    console.log('   • View individual user referral trees');
    console.log('   • See earnings and network size for each user');
    console.log('');
    console.log('💡 If the page is still not loading:');
    console.log('   1. Check browser console for JavaScript errors');
    console.log('   2. Ensure you are logged in as admin');
    console.log('   3. Try refreshing the page');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

testAdminReferralTree().then(success => {
  if (success) {
    console.log('\n🚀 Admin referral tree should be working now!');
  } else {
    console.log('\n⚠️ Admin referral tree test failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
