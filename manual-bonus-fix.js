// Manual Bonus Fix for Referral Issue
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('💰 Manual Bonus Fix for Referral Issue');
console.log('=====================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function manualBonusFix() {
  try {
    console.log('1️⃣ FINDING REFERRER ACCOUNT');
    console.log('─'.repeat(40));
    
    // Find the referrer account
    const referrerEmail = '<EMAIL>';
    
    const { data: referrer, error: referrerError } = await supabase
      .from('users')
      .select('*')
      .eq('email', referrerEmail)
      .single();

    if (referrerError) {
      console.log('❌ Referrer not found:', referrerError.message);
      return false;
    }

    console.log('✅ Referrer found:');
    console.log(`   Name: ${referrer.full_name}`);
    console.log(`   Email: ${referrer.email}`);
    console.log(`   Current Wallet: ₹${referrer.wallet_balance || 0}`);
    console.log(`   Referral Code: ${referrer.referral_code}`);

    console.log('\n2️⃣ GETTING MLM BONUS AMOUNT');
    console.log('─'.repeat(40));

    // Get current MLM bonus amount
    const { data: mlmSettings, error: mlmError } = await supabase
      .from('admin_settings')
      .select('setting_value')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    const bonusAmount = parseFloat(mlmSettings?.setting_value || '250');
    console.log(`💰 Current MLM bonus amount: ₹${bonusAmount}`);

    console.log('\n3️⃣ MANUAL BONUS ADDITION');
    console.log('─'.repeat(40));

    console.log('Since the referral relationship creation failed due to RLS policies,');
    console.log('I will manually add the referral bonus for the premium user signup.');
    console.log('');
    console.log(`📝 Adding ₹${bonusAmount} referral bonus...`);

    // Add wallet transaction for the referral bonus
    const { error: transError } = await supabase
      .from('wallet_transactions')
      .insert({
        user_id: referrer.id,
        type: 'credit',
        amount: bonusAmount,
        description: `Manual MLM referral bonus - Premium user signup`,
        reference_type: 'referral',
        reference_id: 'manual_bonus_fix',
        created_at: new Date().toISOString()
      });

    if (transError) {
      console.log('❌ Error adding bonus transaction:', transError.message);
      return false;
    }

    console.log('✅ Bonus transaction added successfully');

    console.log('\n4️⃣ UPDATING WALLET BALANCE');
    console.log('─'.repeat(40));

    // Update wallet balance
    const newBalance = parseFloat(referrer.wallet_balance || '0') + bonusAmount;
    
    const { error: balanceError } = await supabase
      .from('users')
      .update({ 
        wallet_balance: newBalance,
        premium_referral_count: (referrer.premium_referral_count || 0) + 1
      })
      .eq('id', referrer.id);

    if (balanceError) {
      console.log('❌ Error updating wallet balance:', balanceError.message);
      return false;
    }

    console.log(`✅ Wallet balance updated from ₹${referrer.wallet_balance || 0} to ₹${newBalance}`);
    console.log(`✅ Premium referral count updated to: ${(referrer.premium_referral_count || 0) + 1}`);

    console.log('\n5️⃣ VERIFICATION');
    console.log('─'.repeat(40));

    // Verify the changes
    const { data: updatedUser, error: verifyError } = await supabase
      .from('users')
      .select('wallet_balance, premium_referral_count')
      .eq('id', referrer.id)
      .single();

    if (verifyError) {
      console.log('❌ Error verifying changes:', verifyError.message);
    } else {
      console.log('✅ Changes verified:');
      console.log(`   New wallet balance: ₹${updatedUser.wallet_balance}`);
      console.log(`   Premium referral count: ${updatedUser.premium_referral_count}`);
    }

    // Check wallet transactions
    const { data: transactions, error: transCheckError } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', referrer.id)
      .eq('reference_type', 'referral')
      .order('created_at', { ascending: false })
      .limit(5);

    if (transCheckError) {
      console.log('❌ Error checking transactions:', transCheckError.message);
    } else {
      console.log(`✅ Recent referral transactions: ${transactions?.length || 0}`);
      transactions?.forEach((trans, index) => {
        console.log(`   ${index + 1}. ₹${trans.amount} - ${trans.description}`);
        console.log(`      Date: ${new Date(trans.created_at).toLocaleString()}`);
      });
    }

    console.log('\n6️⃣ ADDITIONAL BONUS OPTIONS');
    console.log('─'.repeat(40));

    console.log('If you want to add more bonuses for additional referrals:');
    console.log('');
    console.log('Option 1: Run this script again');
    console.log('Option 2: Use admin panel to manually add wallet credits');
    console.log('Option 3: Tell me how many more bonuses to add');
    console.log('');
    
    // Ask if user wants to add more bonuses
    console.log('💡 BONUS CALCULATION GUIDE:');
    console.log('   1st referral → ₹250 to referrer');
    console.log('   2nd referral → ₹250 to referrer');
    console.log('   3rd referral → ₹250 to grandparent (not you)');
    console.log('   4th+ referrals → ₹250 to referrer');
    console.log('');
    console.log('If you have multiple premium referrals, you may need multiple bonuses.');

    console.log('\n🎉 MANUAL BONUS FIX COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ Referrer: ${referrerEmail}`);
    console.log(`✅ Bonus added: ₹${bonusAmount}`);
    console.log(`✅ New wallet balance: ₹${newBalance}`);
    console.log(`✅ Premium referral count: ${(referrer.premium_referral_count || 0) + 1}`);
    console.log('');
    console.log('🔗 Next steps:');
    console.log('   1. Check wallet balance in user dashboard');
    console.log('   2. Verify transaction in wallet history');
    console.log('   3. If you have more premium referrals, run this script again');
    console.log('');
    console.log('📱 User can check their wallet at:');
    console.log('   http://localhost:5173/dashboard (after login)');
    console.log('');
    console.log('🔧 Admin can verify at:');
    console.log('   http://localhost:5173/admin/dashboard/users');
    console.log('   http://localhost:5173/admin/dashboard/wallet');

    return true;

  } catch (error) {
    console.error('❌ Manual bonus fix failed:', error.message);
    return false;
  }
}

manualBonusFix().then(success => {
  if (success) {
    console.log('\n🚀 Manual bonus fix completed successfully!');
    console.log('💰 The referral bonus has been added to the wallet!');
  } else {
    console.log('\n⚠️ Manual bonus fix failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
