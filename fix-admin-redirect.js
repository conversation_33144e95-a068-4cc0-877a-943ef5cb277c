// Fix Admin Login Redirect Issue
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔐 Fixing Admin Login Redirect Issue');
console.log('===================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function fixAdminRedirect() {
  try {
    console.log('1️⃣ CHECKING EXISTING SESSIONS');
    console.log('─'.repeat(40));

    // Check current session
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    
    if (session?.session) {
      console.log('⚠️ Found existing session:');
      console.log(`   User: ${session.session.user?.email}`);
      console.log(`   ID: ${session.session.user?.id}`);
      console.log('   Signing out...');
      
      await supabase.auth.signOut();
      console.log('✅ Signed out successfully');
    } else {
      console.log('✅ No existing session found');
    }

    console.log('\n2️⃣ CHECKING <EMAIL> ACCOUNT');
    console.log('─'.repeat(40));

    // <NAME_EMAIL> exists and has admin privileges
    const { data: eashUser, error: eashError } = await supabase
      .from('users')
      .select('id, email, full_name, is_admin, is_premium')
      .eq('email', '<EMAIL>')
      .single();

    if (eashError) {
      if (eashError.code === 'PGRST116') {
        console.log('✅ <EMAIL> not found in users table');
      } else {
        console.log('❌ <NAME_EMAIL>:', eashError.message);
      }
    } else {
      console.log('⚠️ Found <EMAIL> in users table:');
      console.log(`   Name: ${eashUser.full_name}`);
      console.log(`   Is Admin: ${eashUser.is_admin}`);
      console.log(`   Is Premium: ${eashUser.is_premium}`);
      
      if (eashUser.is_admin) {
        console.log('');
        console.log('💡 SOLUTION: Use <EMAIL> as admin!');
        console.log('   This account already has admin privileges');
        console.log('   You can use this instead of creating a new admin');
      }
    }

    console.log('\n3️⃣ CHECKING <EMAIL> ACCOUNT');
    console.log('─'.repeat(40));

    const { data: adminUser, error: adminError } = await supabase
      .from('users')
      .select('id, email, full_name, is_admin, is_premium')
      .eq('email', '<EMAIL>')
      .single();

    if (adminError) {
      if (adminError.code === 'PGRST116') {
        console.log('❌ <EMAIL> not found in users table');
        console.log('   Need to create admin user in database');
      } else {
        console.log('❌ <NAME_EMAIL>:', adminError.message);
      }
    } else {
      console.log('✅ Found <EMAIL> in users table:');
      console.log(`   Name: ${adminUser.full_name}`);
      console.log(`   Is Admin: ${adminUser.is_admin}`);
      console.log(`   Is Premium: ${adminUser.is_premium}`);
    }

    console.log('\n4️⃣ MAKING <EMAIL> ADMIN');
    console.log('─'.repeat(40));

    // Make <EMAIL> an admin since it seems to be the main account
    const { error: updateEashError } = await supabase
      .from('users')
      .upsert({
        email: '<EMAIL>',
        full_name: 'Eash Admin',
        is_admin: true,
        is_premium: true
      }, {
        onConflict: 'email'
      });

    if (updateEashError) {
      console.log('❌ <NAME_EMAIL> admin:', updateEashError.message);
    } else {
      console.log('✅ <EMAIL> is now an admin');
    }

    console.log('\n5️⃣ TESTING <NAME_EMAIL>');
    console.log('─'.repeat(40));

    // Check if we can find the auth <NAME_EMAIL>
    console.log('📝 Checking authentication <NAME_EMAIL>...');
    
    // Try to get user info from auth
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.log('❌ Cannot access auth users (need service role key)');
    } else {
      const eashAuthUser = authUsers.users?.find(u => u.email === '<EMAIL>');
      if (eashAuthUser) {
        console.log('✅ Found <EMAIL> in auth:');
        console.log(`   Email confirmed: ${eashAuthUser.email_confirmed_at ? 'Yes' : 'No'}`);
        console.log(`   Created: ${eashAuthUser.created_at}`);
      } else {
        console.log('❌ <EMAIL> not found in auth users');
      }
    }

    console.log('\n6️⃣ CREATING SIMPLE ADMIN LOGIN');
    console.log('─'.repeat(40));

    // Create a simple admin account that should work
    const simpleAdminEmail = '<EMAIL>';
    const simpleAdminPassword = 'admin123';

    console.log(`📝 Creating simple admin: ${simpleAdminEmail}`);

    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: simpleAdminEmail,
      password: simpleAdminPassword,
      options: {
        data: {
          full_name: 'Simple Admin'
        }
      }
    });

    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('ℹ️ Simple admin already exists');
      } else {
        console.log('❌ Simple admin creation failed:', signUpError.message);
      }
    } else {
      console.log('✅ Simple admin created');
    }

    // Add to users table
    const { error: simpleAdminError } = await supabase
      .from('users')
      .upsert({
        email: simpleAdminEmail,
        full_name: 'Simple Admin',
        is_admin: true,
        is_premium: true
      }, {
        onConflict: 'email'
      });

    if (simpleAdminError) {
      console.log('❌ Error adding simple admin to users table:', simpleAdminError.message);
    } else {
      console.log('✅ Simple admin added to users table');
    }

    console.log('\n🎉 ADMIN REDIRECT FIX SUMMARY');
    console.log('═'.repeat(50));
    console.log('✅ Cleared existing sessions');
    console.log('✅ Made <EMAIL> an admin');
    console.log('✅ Created simple admin account');
    console.log('');
    console.log('📋 ADMIN LOGIN OPTIONS:');
    console.log('');
    console.log('OPTION 1: Use <EMAIL> (if you have the password)');
    console.log('   Email: <EMAIL>');
    console.log('   Password: [your existing password]');
    console.log('   Status: ✅ Now has admin privileges');
    console.log('');
    console.log('OPTION 2: Use simple admin');
    console.log(`   Email: ${simpleAdminEmail}`);
    console.log(`   Password: ${simpleAdminPassword}`);
    console.log('   Status: ✅ New admin account');
    console.log('');
    console.log('OPTION 3: Fix email <NAME_EMAIL>');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('   Status: ⚠️ Needs email confirmation in Supabase dashboard');
    console.log('');
    console.log('🔗 LOGIN URL: http://localhost:5173/admin');
    console.log('');
    console.log('💡 RECOMMENDATION:');
    console.log('   Try OPTION 1 first (<EMAIL>)');
    console.log('   If you don\'t know the password, use OPTION 2');

    return true;

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    return false;
  }
}

fixAdminRedirect().then(success => {
  if (success) {
    console.log('\n🚀 Admin redirect fix completed!');
  } else {
    console.log('\n⚠️ Admin redirect fix failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
