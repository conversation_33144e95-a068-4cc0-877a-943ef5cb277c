// Debug and Fix Referral Bonus Issue
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Debugging Referral Bonus Issue');
console.log('=================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugReferralBonus() {
  try {
    const referrerEmail = '<EMAIL>';
    
    console.log('1️⃣ CHECKING REFERRER ACCOUNT');
    console.log('─'.repeat(40));
    
    // Check the referrer account
    const { data: referrer, error: referrerError } = await supabase
      .from('users')
      .select('*')
      .eq('email', referrerEmail)
      .single();

    if (referrerError) {
      console.log('❌ Referrer not found:', referrerError.message);
      return false;
    }

    console.log('✅ Referrer found:');
    console.log(`   Name: ${referrer.full_name}`);
    console.log(`   Email: ${referrer.email}`);
    console.log(`   Referral Code: ${referrer.referral_code}`);
    console.log(`   Is Premium: ${referrer.is_premium}`);
    console.log(`   Wallet Balance: ₹${referrer.wallet_balance || 0}`);
    console.log(`   Premium Referral Count: ${referrer.premium_referral_count || 0}`);

    console.log('\n2️⃣ CHECKING REFERRALS MADE BY THIS USER');
    console.log('─'.repeat(40));

    // Get all referrals made by this user
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select(`
        *,
        referred_user:users!referrals_referred_user_id_fkey(
          id,
          email,
          full_name,
          is_premium,
          premium_purchased_at
        )
      `)
      .eq('referrer_id', referrer.id)
      .order('referral_order');

    if (referralsError) {
      console.log('❌ Error getting referrals:', referralsError.message);
      return false;
    }

    console.log(`✅ Found ${referrals?.length || 0} referrals:`);
    
    if (referrals && referrals.length > 0) {
      referrals.forEach((ref, index) => {
        const user = ref.referred_user;
        console.log(`   ${ref.referral_order}. ${user.full_name} (${user.email})`);
        console.log(`      Premium: ${user.is_premium ? '✅ Yes' : '❌ No'}`);
        console.log(`      Premium Date: ${user.premium_purchased_at || 'Not set'}`);
        console.log(`      Bonus Amount: ₹${ref.bonus_amount || 0}`);
        console.log(`      Status: ${ref.status}`);
        console.log('');
      });
    }

    console.log('\n3️⃣ CHECKING WALLET TRANSACTIONS');
    console.log('─'.repeat(40));

    // Check wallet transactions for referral bonuses
    const { data: transactions, error: transError } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', referrer.id)
      .eq('reference_type', 'referral')
      .order('created_at', { ascending: false });

    if (transError) {
      console.log('❌ Error getting transactions:', transError.message);
    } else {
      console.log(`✅ Found ${transactions?.length || 0} referral transactions:`);
      
      if (transactions && transactions.length > 0) {
        transactions.forEach((trans, index) => {
          console.log(`   ${index + 1}. ₹${trans.amount} - ${trans.description}`);
          console.log(`      Date: ${new Date(trans.created_at).toLocaleString()}`);
          console.log(`      Reference ID: ${trans.reference_id}`);
        });
      } else {
        console.log('   ⚠️ No referral bonus transactions found!');
      }
    }

    console.log('\n4️⃣ CHECKING MLM SYSTEM STATUS');
    console.log('─'.repeat(40));

    // Check MLM system settings
    const { data: mlmSettings, error: mlmError } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value')
      .in('setting_key', ['mlm_system_enabled', 'mlm_bonus_amount']);

    if (mlmError) {
      console.log('❌ Error getting MLM settings:', mlmError.message);
    } else {
      console.log('✅ MLM System Settings:');
      mlmSettings?.forEach(setting => {
        console.log(`   ${setting.setting_key}: ${setting.setting_value}`);
      });
    }

    console.log('\n5️⃣ FINDING LATEST PREMIUM USER');
    console.log('─'.repeat(40));

    // Find the most recent premium user that should have triggered a bonus
    const { data: latestPremium, error: latestError } = await supabase
      .from('users')
      .select('id, email, full_name, referred_by, is_premium, premium_purchased_at')
      .eq('is_premium', true)
      .not('premium_purchased_at', 'is', null)
      .order('premium_purchased_at', { ascending: false })
      .limit(5);

    if (latestError) {
      console.log('❌ Error getting latest premium users:', latestError.message);
    } else {
      console.log('✅ Latest premium users:');
      latestPremium?.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
        console.log(`      Referred by: ${user.referred_by || 'None'}`);
        console.log(`      Premium date: ${user.premium_purchased_at}`);
        
        if (user.referred_by === referrer.referral_code) {
          console.log(`      🎯 This user was referred by ${referrerEmail}!`);
        }
      });
    }

    console.log('\n6️⃣ MANUAL BONUS CALCULATION');
    console.log('─'.repeat(40));

    // Calculate what bonuses should have been paid
    let expectedBonuses = 0;
    let actualBonuses = 0;

    if (referrals && referrals.length > 0) {
      const premiumReferrals = referrals.filter(ref => ref.referred_user.is_premium);
      console.log(`📊 Premium referrals: ${premiumReferrals.length}`);
      
      // Get current MLM bonus amount
      const mlmBonusAmount = parseFloat(mlmSettings?.find(s => s.setting_key === 'mlm_bonus_amount')?.setting_value || '250');
      
      premiumReferrals.forEach((ref, index) => {
        const position = ref.referral_order;
        let shouldGetBonus = false;
        let recipient = 'Unknown';
        
        if (position <= 2) {
          shouldGetBonus = true;
          recipient = 'Direct referrer (you)';
          expectedBonuses += mlmBonusAmount;
        } else if (position === 3) {
          shouldGetBonus = false;
          recipient = 'Grandparent';
        } else {
          shouldGetBonus = true;
          recipient = 'Direct referrer (you)';
          expectedBonuses += mlmBonusAmount;
        }
        
        console.log(`   Position ${position}: ${ref.referred_user.full_name}`);
        console.log(`      Should get bonus: ${shouldGetBonus ? '✅ Yes' : '❌ No'}`);
        console.log(`      Recipient: ${recipient}`);
        console.log(`      Amount: ₹${shouldGetBonus ? mlmBonusAmount : 0}`);
      });
    }

    if (transactions && transactions.length > 0) {
      actualBonuses = transactions.reduce((sum, trans) => sum + parseFloat(trans.amount), 0);
    }

    console.log(`\n📊 BONUS SUMMARY:`);
    console.log(`   Expected bonuses: ₹${expectedBonuses}`);
    console.log(`   Actual bonuses received: ₹${actualBonuses}`);
    console.log(`   Missing bonuses: ₹${expectedBonuses - actualBonuses}`);

    console.log('\n7️⃣ FIXING MISSING BONUSES');
    console.log('─'.repeat(40));

    if (expectedBonuses > actualBonuses) {
      const missingAmount = expectedBonuses - actualBonuses;
      console.log(`💰 Adding missing bonus: ₹${missingAmount}`);
      
      // Add the missing bonus
      const { error: bonusError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: referrer.id,
          type: 'credit',
          amount: missingAmount,
          description: `Manual MLM referral bonus correction`,
          reference_type: 'referral',
          reference_id: 'manual_correction',
          created_at: new Date().toISOString()
        });

      if (bonusError) {
        console.log('❌ Error adding bonus:', bonusError.message);
      } else {
        console.log('✅ Missing bonus added to wallet transactions');
        
        // Update wallet balance
        const newBalance = parseFloat(referrer.wallet_balance || '0') + missingAmount;
        const { error: balanceError } = await supabase
          .from('users')
          .update({ wallet_balance: newBalance })
          .eq('id', referrer.id);

        if (balanceError) {
          console.log('❌ Error updating wallet balance:', balanceError.message);
        } else {
          console.log(`✅ Wallet balance updated: ₹${newBalance}`);
        }
      }
    } else {
      console.log('✅ No missing bonuses found');
    }

    console.log('\n🎉 REFERRAL BONUS DEBUG COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ Referrer: ${referrerEmail}`);
    console.log(`✅ Total referrals: ${referrals?.length || 0}`);
    console.log(`✅ Premium referrals: ${referrals?.filter(r => r.referred_user.is_premium).length || 0}`);
    console.log(`✅ Expected bonuses: ₹${expectedBonuses}`);
    console.log(`✅ Actual bonuses: ₹${actualBonuses}`);
    console.log(`✅ Status: ${expectedBonuses === actualBonuses ? 'All bonuses correct' : 'Bonuses corrected'}`);

    return true;

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    return false;
  }
}

debugReferralBonus().then(success => {
  if (success) {
    console.log('\n🚀 Referral bonus debug completed successfully!');
  } else {
    console.log('\n⚠️ Referral bonus debug failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
