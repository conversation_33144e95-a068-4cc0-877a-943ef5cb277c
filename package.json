{"name": "herbal-ecommerce", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint .", "preview": "npx vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.3.2", "@supabase/supabase-js": "^2.39.0", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "recharts": "^2.15.3", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^24.0.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}