// Setup MLM Bonus Amount in Admin Settings
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🎯 Setting up MLM Bonus Amount in Admin Panel');
console.log('=============================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function setupMLMAdminSetting() {
  try {
    console.log('📝 Creating MLM bonus amount setting...');
    
    // Check if MLM bonus amount setting already exists
    const { data: existingSetting, error: checkError } = await supabase
      .from('admin_settings')
      .select('*')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    if (existingSetting) {
      console.log('✅ MLM bonus amount setting already exists');
      console.log(`   Current Value: ₹${existingSetting.setting_value}`);
      console.log('   No changes needed - setting is ready for admin panel use');
      return true;
    }

    // Insert the MLM bonus amount setting if it doesn't exist
    const { data, error } = await supabase
      .from('admin_settings')
      .insert({
        setting_key: 'mlm_bonus_amount',
        setting_value: '250.00',
        description: 'Fixed bonus amount for each referral in the MLM system',
        setting_type: 'number',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('❌ Failed to create MLM setting:', error.message);
      return false;
    }

    console.log('✅ MLM bonus amount setting created successfully');
    console.log('   Setting Key: mlm_bonus_amount');
    console.log('   Default Value: ₹250.00');
    console.log('   Description: Fixed bonus amount for each referral in the MLM system');

    // Verify the setting was created
    console.log('\n🔍 Verifying setting...');
    const { data: verification, error: verifyError } = await supabase
      .from('admin_settings')
      .select('*')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    if (verifyError) {
      console.error('❌ Failed to verify setting:', verifyError.message);
      return false;
    }

    console.log('✅ Setting verified successfully:');
    console.log(`   Key: ${verification.setting_key}`);
    console.log(`   Value: ₹${verification.setting_value}`);
    console.log(`   Type: ${verification.setting_type}`);
    console.log(`   Description: ${verification.description}`);

    // Check if other bonus settings exist
    console.log('\n📋 Checking other bonus settings...');
    const { data: allBonusSettings, error: allError } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value, description')
      .in('setting_key', [
        'referral_bonus_amount',
        'premium_referral_bonus_amount',
        'mlm_bonus_amount'
      ]);

    if (allError) {
      console.error('❌ Failed to fetch bonus settings:', allError.message);
    } else {
      console.log('✅ All bonus settings:');
      allBonusSettings?.forEach(setting => {
        console.log(`   ${setting.setting_key}: ₹${setting.setting_value}`);
      });
    }

    console.log('\n🎉 MLM Admin Setting Setup Complete!');
    console.log('');
    console.log('📱 You can now:');
    console.log('   1. Go to Admin Panel → Bonus Settings');
    console.log('   2. Change the MLM Referral Bonus amount');
    console.log('   3. The change will apply to all new referrals');
    console.log('');
    console.log('🔗 Admin Panel URL: http://localhost:5173/admin/dashboard/bonus-settings');

    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    return false;
  }
}

setupMLMAdminSetting().then(success => {
  if (success) {
    console.log('\n🚀 Setup completed successfully!');
  } else {
    console.log('\n⚠️ Setup failed. Please check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
