// Simple Node.js script to test database connection
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Testing Supabase Database Connection...');
console.log('📍 URL:', supabaseUrl);
console.log('🔑 Key:', supabaseAnonKey ? '✅ Set' : '❌ Missing');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDatabase() {
  try {
    console.log('\n📡 Testing basic connection...');
    
    // Test 1: Basic connection
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }

    console.log('✅ Connection successful!');

    // Test 2: Check tables
    console.log('\n📋 Checking tables...');
    const tables = ['users', 'products', 'orders', 'categories'];
    
    for (const table of tables) {
      try {
        const { error: tableError } = await supabase
          .from(table)
          .select('id')
          .limit(1);
        
        if (tableError) {
          console.log(`❌ Table '${table}': ${tableError.message}`);
        } else {
          console.log(`✅ Table '${table}': exists`);
        }
      } catch (err) {
        console.log(`❌ Table '${table}': error checking`);
      }
    }

    // Test 3: Auth status
    console.log('\n🔐 Checking auth status...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.log('⚠️ Auth check failed:', authError.message);
    } else {
      console.log('✅ Auth system working, user:', user ? user.email : 'No user logged in');
    }

    console.log('\n🎯 Database test completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    return false;
  }
}

testDatabase().then(success => {
  process.exit(success ? 0 : 1);
});
