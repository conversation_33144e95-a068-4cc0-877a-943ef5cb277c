import{r as b,R as as}from"./router-CHl1fNDu.js";/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Di={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Li=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),y=(t,e)=>{const n=b.forwardRef(({color:s="currentColor",size:i=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:c,...l},u)=>b.createElement("svg",{ref:u,...Di,width:i,height:i,stroke:s,strokeWidth:o?Number(r)*24/Number(i):r,className:["lucide",`lucide-${Li(t)}`,a].join(" "),...l},[...e.map(([h,f])=>b.createElement(h,f)),...Array.isArray(c)?c:[c]]));return n.displayName=`${t}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=y("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kc=y("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zc=y("ArrowDownLeft",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _c=y("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xc=y("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yc=y("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qc=y("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jc=y("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tl=y("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const el=y("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nl=y("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sl=y("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const il=y("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ol=y("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rl=y("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const al=y("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cl=y("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ll=y("Cookie",[["path",{d:"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5",key:"laymnq"}],["path",{d:"M8.5 8.5v.01",key:"ue8clq"}],["path",{d:"M16 15.5v.01",key:"14dtrp"}],["path",{d:"M12 12v.01",key:"u5ubse"}],["path",{d:"M11 17v.01",key:"1hyl5a"}],["path",{d:"M7 14v.01",key:"uct60s"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ul=y("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hl=y("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fl=y("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dl=y("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pl=y("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ml=y("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yl=y("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gl=y("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vl=y("FileCheck",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xl=y("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kl=y("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=y("FlaskConical",[["path",{d:"M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2",key:"pzvekw"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M7 16h10",key:"wp8him"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=y("Flower",[["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}],["path",{d:"M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5",key:"14wa3c"}],["path",{d:"M12 7.5V9",key:"1oy5b0"}],["path",{d:"M7.5 12H9",key:"eltsq1"}],["path",{d:"M16.5 12H15",key:"vk5kw4"}],["path",{d:"M12 16.5V15",key:"k7eayi"}],["path",{d:"m8 8 1.88 1.88",key:"nxy4qf"}],["path",{d:"M14.12 9.88 16 8",key:"1lst6k"}],["path",{d:"m8 16 1.88-1.88",key:"h2eex1"}],["path",{d:"M14.12 14.12 16 16",key:"uqkrx3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bl=y("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=y("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=y("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wl=y("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cl=y("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sl=y("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=y("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=y("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=y("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=y("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=y("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=y("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jl=y("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const El=y("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=y("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=y("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=y("MinusCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=y("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=y("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=y("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ql=y("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wl=y("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gl=y("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=y("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=y("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl=y("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=y("Scale",[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xl=y("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yl=y("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ql=y("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jl=y("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tu=y("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eu=y("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nu=y("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const su=y("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iu=y("Sprout",[["path",{d:"M7 20h10",key:"e6iznv"}],["path",{d:"M10 20c5.5-2.5.8-6.4 3-10",key:"161w41"}],["path",{d:"M9.5 9.4c1.1.8 1.8 2.2 2.3 3.7-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2 2.8-.5 4.4 0 5.5.8z",key:"9gtqwd"}],["path",{d:"M14.1 6a7 7 0 0 0-1.1 4c1.9-.1 3.3-.6 4.3-1.4 1-1 1.6-2.3 1.7-4.6-2.7.1-4 1-4.9 2z",key:"bkxnd2"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ou=y("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ru=y("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const au=y("Syringe",[["path",{d:"m18 2 4 4",key:"22kx64"}],["path",{d:"m17 7 3-3",key:"1w1zoj"}],["path",{d:"M19 9 8.7 19.3c-1 1-2.5 1-3.4 0l-.6-.6c-1-1-1-2.5 0-3.4L15 5",key:"1exhtz"}],["path",{d:"m9 11 4 4",key:"rovt3i"}],["path",{d:"m5 19-3 3",key:"59f2uf"}],["path",{d:"m14 4 6 6",key:"yqp9t2"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cu=y("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lu=y("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uu=y("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hu=y("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fu=y("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const du=y("Unlock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pu=y("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=y("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yu=y("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gu=y("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vu=y("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xu=y("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ku=y("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pu=y("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),cs=b.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Qt=b.createContext({}),De=b.createContext(null),Jt=typeof document<"u",Ri=Jt?b.useLayoutEffect:b.useEffect,ls=b.createContext({strict:!1}),Le=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Bi="framerAppearId",us="data-"+Le(Bi);function Fi(t,e,n,s){const{visualElement:i}=b.useContext(Qt),r=b.useContext(ls),o=b.useContext(De),a=b.useContext(cs).reducedMotion,c=b.useRef();s=s||r.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const l=c.current;b.useInsertionEffect(()=>{l&&l.update(n,o)});const u=b.useRef(!!(n[us]&&!window.HandoffComplete));return Ri(()=>{l&&(l.render(),u.current&&l.animationState&&l.animationState.animateChanges())}),b.useEffect(()=>{l&&(l.updateFeatures(),!u.current&&l.animationState&&l.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),l}function pt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function ji(t,e,n){return b.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):pt(n)&&(n.current=s))},[e])}function Dt(t){return typeof t=="string"||Array.isArray(t)}function te(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const Re=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Be=["initial",...Re];function ee(t){return te(t.animate)||Be.some(e=>Dt(t[e]))}function hs(t){return!!(ee(t)||t.variants)}function Ei(t,e){if(ee(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Dt(n)?n:void 0,animate:Dt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Oi(t){const{initial:e,animate:n}=Ei(t,b.useContext(Qt));return b.useMemo(()=>({initial:e,animate:n}),[nn(e),nn(n)])}function nn(t){return Array.isArray(t)?t.join(" "):t}const sn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Lt={};for(const t in sn)Lt[t]={isEnabled:e=>sn[t].some(n=>!!e[n])};function Ii(t){for(const e in t)Lt[e]={...Lt[e],...t[e]}}const fs=b.createContext({}),ds=b.createContext({}),Ui=Symbol.for("motionComponentSymbol");function zi({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Ii(t);function r(a,c){let l;const u={...b.useContext(cs),...a,layoutId:Hi(a)},{isStatic:h}=u,f=Oi(a),d=s(a,h);if(!h&&Jt){f.visualElement=Fi(i,d,u,e);const p=b.useContext(ds),m=b.useContext(ls).strict;f.visualElement&&(l=f.visualElement.loadFeatures(u,m,t,p))}return b.createElement(Qt.Provider,{value:f},l&&f.visualElement?b.createElement(l,{visualElement:f.visualElement,...u}):null,n(i,a,ji(d,f.visualElement,c),d,h,f.visualElement))}const o=b.forwardRef(r);return o[Ui]=i,o}function Hi({layoutId:t}){const e=b.useContext(fs).id;return e&&t!==void 0?e+"-"+t:t}function Ni(t){function e(s,i={}){return zi(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const qi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Fe(t){return typeof t!="string"||t.includes("-")?!1:!!(qi.indexOf(t)>-1||/[A-Z]/.test(t))}const Wt={};function Wi(t){Object.assign(Wt,t)}const Bt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],lt=new Set(Bt);function ps(t,{layout:e,layoutId:n}){return lt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Wt[t]||t==="opacity")}const I=t=>!!(t&&t.getVelocity),Gi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},$i=Bt.length;function Ki(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},s,i){let r="";for(let o=0;o<$i;o++){const a=Bt[o];if(t[a]!==void 0){const c=Gi[a]||a;r+=`${c}(${t[a]}) `}}return e&&!t.z&&(r+="translateZ(0)"),r=r.trim(),i?r=i(t,s?"":r):n&&s&&(r="none"),r}const ms=t=>e=>typeof e=="string"&&e.startsWith(t),ys=ms("--"),ge=ms("var(--"),Zi=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,_i=(t,e)=>e&&typeof t=="number"?e.transform(t):t,tt=(t,e,n)=>Math.min(Math.max(n,t),e),ut={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},wt={...ut,transform:t=>tt(0,1,t)},It={...ut,default:1},Ct=t=>Math.round(t*1e5)/1e5,ne=/(-)?([\d]*\.?[\d])+/g,gs=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Xi=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Ft(t){return typeof t=="string"}const jt=t=>({test:e=>Ft(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=jt("deg"),q=jt("%"),P=jt("px"),Yi=jt("vh"),Qi=jt("vw"),on={...q,parse:t=>q.parse(t)/100,transform:t=>q.transform(t*100)},rn={...ut,transform:Math.round},vs={borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,size:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:It,scaleX:It,scaleY:It,scaleZ:It,skew:Y,skewX:Y,skewY:Y,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:wt,originX:on,originY:on,originZ:P,zIndex:rn,fillOpacity:wt,strokeOpacity:wt,numOctaves:rn};function je(t,e,n,s){const{style:i,vars:r,transform:o,transformOrigin:a}=t;let c=!1,l=!1,u=!0;for(const h in e){const f=e[h];if(ys(h)){r[h]=f;continue}const d=vs[h],p=_i(f,d);if(lt.has(h)){if(c=!0,o[h]=p,!u)continue;f!==(d.default||0)&&(u=!1)}else h.startsWith("origin")?(l=!0,a[h]=p):i[h]=p}if(e.transform||(c||s?i.transform=Ki(t.transform,n,u,s):i.transform&&(i.transform="none")),l){const{originX:h="50%",originY:f="50%",originZ:d=0}=a;i.transformOrigin=`${h} ${f} ${d}`}}const Ee=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function xs(t,e,n){for(const s in e)!I(e[s])&&!ps(s,n)&&(t[s]=e[s])}function Ji({transformTemplate:t},e,n){return b.useMemo(()=>{const s=Ee();return je(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function to(t,e,n){const s=t.style||{},i={};return xs(i,s,t),Object.assign(i,Ji(t,e,n)),t.transformValues?t.transformValues(i):i}function eo(t,e,n){const s={},i=to(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=i,s}const no=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Gt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||no.has(t)}let ks=t=>!Gt(t);function so(t){t&&(ks=e=>e.startsWith("on")?!Gt(e):t(e))}try{so(require("@emotion/is-prop-valid").default)}catch{}function io(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(ks(i)||n===!0&&Gt(i)||!e&&!Gt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function an(t,e,n){return typeof t=="string"?t:P.transform(e+n*t)}function oo(t,e,n){const s=an(e,t.x,t.width),i=an(n,t.y,t.height);return`${s} ${i}`}const ro={offset:"stroke-dashoffset",array:"stroke-dasharray"},ao={offset:"strokeDashoffset",array:"strokeDasharray"};function co(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?ro:ao;t[r.offset]=P.transform(-s);const o=P.transform(e),a=P.transform(n);t[r.array]=`${o} ${a}`}function Oe(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:c=0,...l},u,h,f){if(je(t,l,u,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(i!==void 0||r!==void 0||p.transform)&&(p.transformOrigin=oo(m,i!==void 0?i:.5,r!==void 0?r:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),o!==void 0&&co(d,o,a,c,!1)}const Ps=()=>({...Ee(),attrs:{}}),Ie=t=>typeof t=="string"&&t.toLowerCase()==="svg";function lo(t,e,n,s){const i=b.useMemo(()=>{const r=Ps();return Oe(r,e,{enableHardwareAcceleration:!1},Ie(s),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};xs(r,t.style,t),i.style={...r,...i.style}}return i}function uo(t=!1){return(n,s,i,{latestValues:r},o)=>{const c=(Fe(n)?lo:eo)(s,r,o,n),u={...io(s,typeof n=="string",t),...c,ref:i},{children:h}=s,f=b.useMemo(()=>I(h)?h.get():h,[h]);return b.createElement(n,{...u,children:f})}}function Ts(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}const bs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ms(t,e,n,s){Ts(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(bs.has(i)?i:Le(i),e.attrs[i])}function Ue(t,e){const{style:n}=t,s={};for(const i in n)(I(n[i])||e.style&&I(e.style[i])||ps(i,t))&&(s[i]=n[i]);return s}function Vs(t,e){const n=Ue(t,e);for(const s in t)if(I(t[s])||I(e[s])){const i=Bt.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;n[i]=t[s]}return n}function ze(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}function ho(t){const e=b.useRef(null);return e.current===null&&(e.current=t()),e.current}const $t=t=>Array.isArray(t),fo=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),po=t=>$t(t)?t[t.length-1]||0:t;function Nt(t){const e=I(t)?t.get():t;return fo(e)?e.toValue():e}function mo({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,r){const o={latestValues:yo(s,i,r,t),renderState:e()};return n&&(o.mount=a=>n(s,a,o)),o}const ws=t=>(e,n)=>{const s=b.useContext(Qt),i=b.useContext(De),r=()=>mo(t,e,s,i);return n?r():ho(r)};function yo(t,e,n,s){const i={},r=s(t,{});for(const f in r)i[f]=Nt(r[f]);let{initial:o,animate:a}=t;const c=ee(t),l=hs(t);e&&l&&!c&&t.inherit!==!1&&(o===void 0&&(o=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||o===!1;const h=u?a:o;return h&&typeof h!="boolean"&&!te(h)&&(Array.isArray(h)?h:[h]).forEach(d=>{const p=ze(t,d);if(!p)return;const{transitionEnd:m,transition:v,...T}=p;for(const x in T){let g=T[x];if(Array.isArray(g)){const k=u?g.length-1:0;g=g[k]}g!==null&&(i[x]=g)}for(const x in m)i[x]=m[x]}),i}const L=t=>t;class cn{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const n=this.order.indexOf(e);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}function go(t){let e=new cn,n=new cn,s=0,i=!1,r=!1;const o=new WeakSet,a={schedule:(c,l=!1,u=!1)=>{const h=u&&i,f=h?e:n;return l&&o.add(c),f.add(c)&&h&&i&&(s=e.order.length),c},cancel:c=>{n.remove(c),o.delete(c)},process:c=>{if(i){r=!0;return}if(i=!0,[e,n]=[n,e],n.clear(),s=e.order.length,s)for(let l=0;l<s;l++){const u=e.order[l];u(c),o.has(u)&&(a.schedule(u),t())}i=!1,r&&(r=!1,a.process(c))}};return a}const Ut=["prepare","read","update","preRender","render","postRender"],vo=40;function xo(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=Ut.reduce((h,f)=>(h[f]=go(()=>n=!0),h),{}),o=h=>r[h].process(i),a=()=>{const h=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(h-i.timestamp,vo),1),i.timestamp=h,i.isProcessing=!0,Ut.forEach(o),i.isProcessing=!1,n&&e&&(s=!1,t(a))},c=()=>{n=!0,s=!0,i.isProcessing||t(a)};return{schedule:Ut.reduce((h,f)=>{const d=r[f];return h[f]=(p,m=!1,v=!1)=>(n||c(),d.schedule(p,m,v)),h},{}),cancel:h=>Ut.forEach(f=>r[f].cancel(h)),state:i,steps:r}}const{schedule:w,cancel:_,state:j,steps:re}=xo(typeof requestAnimationFrame<"u"?requestAnimationFrame:L,!0),ko={useVisualState:ws({scrapeMotionValuesFromProps:Vs,createRenderState:Ps,onMount:(t,e,{renderState:n,latestValues:s})=>{w.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),w.render(()=>{Oe(n,s,{enableHardwareAcceleration:!1},Ie(e.tagName),t.transformTemplate),Ms(e,n)})}})},Po={useVisualState:ws({scrapeMotionValuesFromProps:Ue,createRenderState:Ee})};function To(t,{forwardMotionProps:e=!1},n,s){return{...Fe(t)?ko:Po,preloadedFeatures:n,useRender:uo(e),createVisualElement:s,Component:t}}function $(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const Cs=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function se(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const bo=t=>e=>Cs(e)&&t(e,se(e));function K(t,e,n,s){return $(t,e,bo(n),s)}const Mo=(t,e)=>n=>e(t(n)),J=(...t)=>t.reduce(Mo);function Ss(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const ln=Ss("dragHorizontal"),un=Ss("dragVertical");function As(t){let e=!1;if(t==="y")e=un();else if(t==="x")e=ln();else{const n=ln(),s=un();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Ds(){const t=As(!0);return t?(t(),!1):!0}class nt{constructor(e){this.isMounted=!1,this.node=e}update(){}}function hn(t,e){const n="pointer"+(e?"enter":"leave"),s="onHover"+(e?"Start":"End"),i=(r,o)=>{if(r.pointerType==="touch"||Ds())return;const a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e),a[s]&&w.update(()=>a[s](r,o))};return K(t.current,n,i,{passive:!t.getProps()[s]})}class Vo extends nt{mount(){this.unmount=J(hn(this.node,!0),hn(this.node,!1))}unmount(){}}class wo extends nt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=J($(this.node.current,"focus",()=>this.onFocus()),$(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Ls=(t,e)=>e?t===e?!0:Ls(t,e.parentElement):!1;function ae(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,se(n))}class Co extends nt{constructor(){super(...arguments),this.removeStartListeners=L,this.removeEndListeners=L,this.removeAccessibleListeners=L,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),r=K(window,"pointerup",(a,c)=>{if(!this.checkPressEnd())return;const{onTap:l,onTapCancel:u,globalTapTarget:h}=this.node.getProps();w.update(()=>{!h&&!Ls(this.node.current,a.target)?u&&u(a,c):l&&l(a,c)})},{passive:!(s.onTap||s.onPointerUp)}),o=K(window,"pointercancel",(a,c)=>this.cancelPress(a,c),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=J(r,o),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=r=>{if(r.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||ae("up",(c,l)=>{const{onTap:u}=this.node.getProps();u&&w.update(()=>u(c,l))})};this.removeEndListeners(),this.removeEndListeners=$(this.node.current,"keyup",o),ae("down",(a,c)=>{this.startPress(a,c)})},n=$(this.node.current,"keydown",e),s=()=>{this.isPressing&&ae("cancel",(r,o)=>this.cancelPress(r,o))},i=$(this.node.current,"blur",s);this.removeAccessibleListeners=J(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&w.update(()=>s(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Ds()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&w.update(()=>s(e,n))}mount(){const e=this.node.getProps(),n=K(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=$(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=J(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const ve=new WeakMap,ce=new WeakMap,So=t=>{const e=ve.get(t.target);e&&e(t)},Ao=t=>{t.forEach(So)};function Do({root:t,...e}){const n=t||document;ce.has(n)||ce.set(n,{});const s=ce.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Ao,{root:t,...e})),s[i]}function Lo(t,e,n){const s=Do(e);return ve.set(t,n),s.observe(t),()=>{ve.delete(t),s.unobserve(t)}}const Ro={some:0,all:1};class Bo extends nt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,o={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Ro[i]},a=c=>{const{isIntersecting:l}=c;if(this.isInView===l||(this.isInView=l,r&&!l&&this.hasEnteredView))return;l&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",l);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=l?u:h;f&&f(c)};return Lo(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Fo(e,n))&&this.startObserver()}unmount(){}}function Fo({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const jo={inView:{Feature:Bo},tap:{Feature:Co},focus:{Feature:wo},hover:{Feature:Vo}};function Rs(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function Eo(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function Oo(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function ie(t,e,n){const s=t.getProps();return ze(s,e,n!==void 0?n:s.custom,Eo(t),Oo(t))}let He=L;const ct=t=>t*1e3,Z=t=>t/1e3,Io={current:!1},Bs=t=>Array.isArray(t)&&typeof t[0]=="number";function Fs(t){return!!(!t||typeof t=="string"&&js[t]||Bs(t)||Array.isArray(t)&&t.every(Fs))}const Vt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,js={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Vt([0,.65,.55,1]),circOut:Vt([.55,0,1,.45]),backIn:Vt([.31,.01,.66,-.59]),backOut:Vt([.33,1.53,.69,.99])};function Es(t){if(t)return Bs(t)?Vt(t):Array.isArray(t)?t.map(Es):js[t]}function Uo(t,e,n,{delay:s=0,duration:i,repeat:r=0,repeatType:o="loop",ease:a,times:c}={}){const l={[e]:n};c&&(l.offset=c);const u=Es(a);return Array.isArray(u)&&(l.easing=u),t.animate(l,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"})}function zo(t,{repeat:e,repeatType:n="loop"}){const s=e&&n!=="loop"&&e%2===1?0:t.length-1;return t[s]}const Os=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Ho=1e-7,No=12;function qo(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=Os(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>Ho&&++a<No);return o}function Et(t,e,n,s){if(t===e&&n===s)return L;const i=r=>qo(r,0,1,t,n);return r=>r===0||r===1?r:Os(i(r),e,s)}const Wo=Et(.42,0,1,1),Go=Et(0,0,.58,1),Is=Et(.42,0,.58,1),$o=t=>Array.isArray(t)&&typeof t[0]!="number",Us=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,zs=t=>e=>1-t(1-e),Ne=t=>1-Math.sin(Math.acos(t)),Hs=zs(Ne),Ko=Us(Ne),Ns=Et(.33,1.53,.69,.99),qe=zs(Ns),Zo=Us(qe),_o=t=>(t*=2)<1?.5*qe(t):.5*(2-Math.pow(2,-10*(t-1))),Xo={linear:L,easeIn:Wo,easeInOut:Is,easeOut:Go,circIn:Ne,circInOut:Ko,circOut:Hs,backIn:qe,backInOut:Zo,backOut:Ns,anticipate:_o},fn=t=>{if(Array.isArray(t)){He(t.length===4);const[e,n,s,i]=t;return Et(e,n,s,i)}else if(typeof t=="string")return Xo[t];return t},We=(t,e)=>n=>!!(Ft(n)&&Xi.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),qs=(t,e,n)=>s=>{if(!Ft(s))return s;const[i,r,o,a]=s.match(ne);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},Yo=t=>tt(0,255,t),le={...ut,transform:t=>Math.round(Yo(t))},at={test:We("rgb","red"),parse:qs("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+le.transform(t)+", "+le.transform(e)+", "+le.transform(n)+", "+Ct(wt.transform(s))+")"};function Qo(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const xe={test:We("#"),parse:Qo,transform:at.transform},mt={test:We("hsl","hue"),parse:qs("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+q.transform(Ct(e))+", "+q.transform(Ct(n))+", "+Ct(wt.transform(s))+")"},O={test:t=>at.test(t)||xe.test(t)||mt.test(t),parse:t=>at.test(t)?at.parse(t):mt.test(t)?mt.parse(t):xe.parse(t),transform:t=>Ft(t)?t:t.hasOwnProperty("red")?at.transform(t):mt.transform(t)},A=(t,e,n)=>-n*t+n*e+t;function ue(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Jo({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;i=ue(c,a,t+1/3),r=ue(c,a,t),o=ue(c,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}const he=(t,e,n)=>{const s=t*t;return Math.sqrt(Math.max(0,n*(e*e-s)+s))},tr=[xe,at,mt],er=t=>tr.find(e=>e.test(t));function dn(t){const e=er(t);let n=e.parse(t);return e===mt&&(n=Jo(n)),n}const Ws=(t,e)=>{const n=dn(t),s=dn(e),i={...n};return r=>(i.red=he(n.red,s.red,r),i.green=he(n.green,s.green,r),i.blue=he(n.blue,s.blue,r),i.alpha=A(n.alpha,s.alpha,r),at.transform(i))};function nr(t){var e,n;return isNaN(t)&&Ft(t)&&(((e=t.match(ne))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(gs))===null||n===void 0?void 0:n.length)||0)>0}const Gs={regex:Zi,countKey:"Vars",token:"${v}",parse:L},$s={regex:gs,countKey:"Colors",token:"${c}",parse:O.parse},Ks={regex:ne,countKey:"Numbers",token:"${n}",parse:ut.parse};function fe(t,{regex:e,countKey:n,token:s,parse:i}){const r=t.tokenised.match(e);r&&(t["num"+n]=r.length,t.tokenised=t.tokenised.replace(e,s),t.values.push(...r.map(i)))}function Kt(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&fe(n,Gs),fe(n,$s),fe(n,Ks),n}function Zs(t){return Kt(t).values}function _s(t){const{values:e,numColors:n,numVars:s,tokenised:i}=Kt(t),r=e.length;return o=>{let a=i;for(let c=0;c<r;c++)c<s?a=a.replace(Gs.token,o[c]):c<s+n?a=a.replace($s.token,O.transform(o[c])):a=a.replace(Ks.token,Ct(o[c]));return a}}const sr=t=>typeof t=="number"?0:t;function ir(t){const e=Zs(t);return _s(t)(e.map(sr))}const et={test:nr,parse:Zs,createTransformer:_s,getAnimatableNone:ir},Xs=(t,e)=>n=>`${n>0?e:t}`;function Ys(t,e){return typeof t=="number"?n=>A(t,e,n):O.test(t)?Ws(t,e):t.startsWith("var(")?Xs(t,e):Js(t,e)}const Qs=(t,e)=>{const n=[...t],s=n.length,i=t.map((r,o)=>Ys(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}},or=(t,e)=>{const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ys(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}},Js=(t,e)=>{const n=et.createTransformer(e),s=Kt(t),i=Kt(e);return s.numVars===i.numVars&&s.numColors===i.numColors&&s.numNumbers>=i.numNumbers?J(Qs(s.values,i.values),n):Xs(t,e)},Rt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},pn=(t,e)=>n=>A(t,e,n);function rr(t){return typeof t=="number"?pn:typeof t=="string"?O.test(t)?Ws:Js:Array.isArray(t)?Qs:typeof t=="object"?or:pn}function ar(t,e,n){const s=[],i=n||rr(t[0]),r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]||L:e;a=J(c,a)}s.push(a)}return s}function ti(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(He(r===e.length),r===1)return()=>e[0];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=ar(e,s,i),a=o.length,c=l=>{let u=0;if(a>1)for(;u<t.length-2&&!(l<t[u+1]);u++);const h=Rt(t[u],t[u+1],l);return o[u](h)};return n?l=>c(tt(t[0],t[r-1],l)):c}function cr(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Rt(0,e,s);t.push(A(n,1,i))}}function lr(t){const e=[0];return cr(e,t.length-1),e}function ur(t,e){return t.map(n=>n*e)}function hr(t,e){return t.map(()=>e||Is).splice(0,t.length-1)}function Zt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=$o(s)?s.map(fn):fn(s),r={done:!1,value:e[0]},o=ur(n&&n.length===e.length?n:lr(e),t),a=ti(o,e,{ease:Array.isArray(i)?i:hr(e,i)});return{calculatedDuration:t,next:c=>(r.value=a(c),r.done=c>=t,r)}}function ei(t,e){return e?t*(1e3/e):0}const fr=5;function ni(t,e,n){const s=Math.max(e-fr,0);return ei(n-t(s),e-s)}const mn=.001,dr=.01,pr=10,mr=.05,yr=1;function gr({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,r,o=1-e;o=tt(mr,yr,o),t=tt(dr,pr,Z(t)),o<1?(i=l=>{const u=l*o,h=u*t,f=u-n,d=ke(l,o),p=Math.exp(-h);return mn-f/d*p},r=l=>{const h=l*o*t,f=h*n+n,d=Math.pow(o,2)*Math.pow(l,2)*t,p=Math.exp(-h),m=ke(Math.pow(l,2),o);return(-i(l)+mn>0?-1:1)*((f-d)*p)/m}):(i=l=>{const u=Math.exp(-l*t),h=(l-n)*t+1;return-.001+u*h},r=l=>{const u=Math.exp(-l*t),h=(n-l)*(t*t);return u*h});const a=5/t,c=xr(i,r,a);if(t=ct(t),isNaN(c))return{stiffness:100,damping:10,duration:t};{const l=Math.pow(c,2)*s;return{stiffness:l,damping:o*2*Math.sqrt(s*l),duration:t}}}const vr=12;function xr(t,e,n){let s=n;for(let i=1;i<vr;i++)s=s-t(s)/e(s);return s}function ke(t,e){return t*Math.sqrt(1-e*e)}const kr=["duration","bounce"],Pr=["stiffness","damping","mass"];function yn(t,e){return e.some(n=>t[n]!==void 0)}function Tr(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!yn(t,Pr)&&yn(t,kr)){const n=gr(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function si({keyframes:t,restDelta:e,restSpeed:n,...s}){const i=t[0],r=t[t.length-1],o={done:!1,value:i},{stiffness:a,damping:c,mass:l,duration:u,velocity:h,isResolvedFromDuration:f}=Tr({...s,velocity:-Z(s.velocity||0)}),d=h||0,p=c/(2*Math.sqrt(a*l)),m=r-i,v=Z(Math.sqrt(a/l)),T=Math.abs(m)<5;n||(n=T?.01:2),e||(e=T?.005:.5);let x;if(p<1){const g=ke(v,p);x=k=>{const M=Math.exp(-p*v*k);return r-M*((d+p*v*m)/g*Math.sin(g*k)+m*Math.cos(g*k))}}else if(p===1)x=g=>r-Math.exp(-v*g)*(m+(d+v*m)*g);else{const g=v*Math.sqrt(p*p-1);x=k=>{const M=Math.exp(-p*v*k),R=Math.min(g*k,300);return r-M*((d+p*v*m)*Math.sinh(R)+g*m*Math.cosh(R))/g}}return{calculatedDuration:f&&u||null,next:g=>{const k=x(g);if(f)o.done=g>=u;else{let M=d;g!==0&&(p<1?M=ni(x,g,k):M=0);const R=Math.abs(M)<=n,C=Math.abs(r-k)<=e;o.done=R&&C}return o.value=o.done?r:k,o}}}function gn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:c,restDelta:l=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=V=>a!==void 0&&V<a||c!==void 0&&V>c,p=V=>a===void 0?c:c===void 0||Math.abs(a-V)<Math.abs(c-V)?a:c;let m=n*e;const v=h+m,T=o===void 0?v:o(v);T!==v&&(m=T-h);const x=V=>-m*Math.exp(-V/s),g=V=>T+x(V),k=V=>{const D=x(V),W=g(V);f.done=Math.abs(D)<=l,f.value=f.done?T:W};let M,R;const C=V=>{d(f.value)&&(M=V,R=si({keyframes:[f.value,p(f.value)],velocity:ni(g,V,f.value),damping:i,stiffness:r,restDelta:l,restSpeed:u}))};return C(0),{calculatedDuration:null,next:V=>{let D=!1;return!R&&M===void 0&&(D=!0,k(V),C(V)),M!==void 0&&V>M?R.next(V-M):(!D&&k(V),f)}}}const br=t=>{const e=({timestamp:n})=>t(n);return{start:()=>w.update(e,!0),stop:()=>_(e),now:()=>j.isProcessing?j.timestamp:performance.now()}},vn=2e4;function xn(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<vn;)e+=n,s=t.next(e);return e>=vn?1/0:e}const Mr={decay:gn,inertia:gn,tween:Zt,keyframes:Zt,spring:si};function _t({autoplay:t=!0,delay:e=0,driver:n=br,keyframes:s,type:i="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:a="loop",onPlay:c,onStop:l,onComplete:u,onUpdate:h,...f}){let d=1,p=!1,m,v;const T=()=>{v=new Promise(S=>{m=S})};T();let x;const g=Mr[i]||Zt;let k;g!==Zt&&typeof s[0]!="number"&&(k=ti([0,100],s,{clamp:!1}),s=[0,100]);const M=g({...f,keyframes:s});let R;a==="mirror"&&(R=g({...f,keyframes:[...s].reverse(),velocity:-(f.velocity||0)}));let C="idle",V=null,D=null,W=null;M.calculatedDuration===null&&r&&(M.calculatedDuration=xn(M));const{calculatedDuration:ht}=M;let N=1/0,G=1/0;ht!==null&&(N=ht+o,G=N*(r+1)-o);let E=0;const ft=S=>{if(D===null)return;d>0&&(D=Math.min(D,S)),d<0&&(D=Math.min(S-G/d,D)),V!==null?E=V:E=Math.round(S-D)*d;const Pt=E-e*(d>=0?1:-1),Qe=d>=0?Pt<0:Pt>G;E=Math.max(Pt,0),C==="finished"&&V===null&&(E=G);let Je=E,tn=M;if(r){const oe=Math.min(E,G)/N;let Ot=Math.floor(oe),st=oe%1;!st&&oe>=1&&(st=1),st===1&&Ot--,Ot=Math.min(Ot,r+1),!!(Ot%2)&&(a==="reverse"?(st=1-st,o&&(st-=o/N)):a==="mirror"&&(tn=R)),Je=tt(0,1,st)*N}const Tt=Qe?{done:!1,value:s[0]}:tn.next(Je);k&&(Tt.value=k(Tt.value));let{done:en}=Tt;!Qe&&ht!==null&&(en=d>=0?E>=G:E<=0);const Ai=V===null&&(C==="finished"||C==="running"&&en);return h&&h(Tt.value),Ai&&kt(),Tt},F=()=>{x&&x.stop(),x=void 0},X=()=>{C="idle",F(),m(),T(),D=W=null},kt=()=>{C="finished",u&&u(),F(),m()},dt=()=>{if(p)return;x||(x=n(ft));const S=x.now();c&&c(),V!==null?D=S-V:(!D||C==="finished")&&(D=S),C==="finished"&&T(),W=D,V=null,C="running",x.start()};t&&dt();const Ye={then(S,Pt){return v.then(S,Pt)},get time(){return Z(E)},set time(S){S=ct(S),E=S,V!==null||!x||d===0?V=S:D=x.now()-S/d},get duration(){const S=M.calculatedDuration===null?xn(M):M.calculatedDuration;return Z(S)},get speed(){return d},set speed(S){S===d||!x||(d=S,Ye.time=Z(E))},get state(){return C},play:dt,pause:()=>{C="paused",V=E},stop:()=>{p=!0,C!=="idle"&&(C="idle",l&&l(),X())},cancel:()=>{W!==null&&ft(W),X()},complete:()=>{C="finished"},sample:S=>(D=0,ft(S))};return Ye}function Vr(t){let e;return()=>(e===void 0&&(e=t()),e)}const wr=Vr(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Cr=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),zt=10,Sr=2e4,Ar=(t,e)=>e.type==="spring"||t==="backgroundColor"||!Fs(e.ease);function Dr(t,e,{onUpdate:n,onComplete:s,...i}){if(!(wr()&&Cr.has(e)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,a,c,l=!1;const u=()=>{c=new Promise(g=>{a=g})};u();let{keyframes:h,duration:f=300,ease:d,times:p}=i;if(Ar(e,i)){const g=_t({...i,repeat:0,delay:0});let k={done:!1,value:h[0]};const M=[];let R=0;for(;!k.done&&R<Sr;)k=g.sample(R),M.push(k.value),R+=zt;p=void 0,h=M,f=R-zt,d="linear"}const m=Uo(t.owner.current,e,h,{...i,duration:f,ease:d,times:p}),v=()=>{l=!1,m.cancel()},T=()=>{l=!0,w.update(v),a(),u()};return m.onfinish=()=>{l||(t.set(zo(h,i)),s&&s(),T())},{then(g,k){return c.then(g,k)},attachTimeline(g){return m.timeline=g,m.onfinish=null,L},get time(){return Z(m.currentTime||0)},set time(g){m.currentTime=ct(g)},get speed(){return m.playbackRate},set speed(g){m.playbackRate=g},get duration(){return Z(f)},play:()=>{o||(m.play(),_(v))},pause:()=>m.pause(),stop:()=>{if(o=!0,m.playState==="idle")return;const{currentTime:g}=m;if(g){const k=_t({...i,autoplay:!1});t.setWithVelocity(k.sample(g-zt).value,k.sample(g).value,zt)}T()},complete:()=>{l||m.finish()},cancel:T}}function Lr({keyframes:t,delay:e,onUpdate:n,onComplete:s}){const i=()=>(n&&n(t[t.length-1]),s&&s(),{time:0,speed:1,duration:0,play:L,pause:L,stop:L,then:r=>(r(),Promise.resolve()),cancel:L,complete:L});return e?_t({keyframes:[0,1],duration:0,delay:e,onComplete:i}):i()}const Rr={type:"spring",stiffness:500,damping:25,restSpeed:10},Br=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Fr={type:"keyframes",duration:.8},jr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Er=(t,{keyframes:e})=>e.length>2?Fr:lt.has(t)?t.startsWith("scale")?Br(e[1]):Rr:jr,Pe=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(et.test(e)||e==="0")&&!e.startsWith("url(")),Or=new Set(["brightness","contrast","saturate","opacity"]);function Ir(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(ne)||[];if(!s)return t;const i=n.replace(s,"");let r=Or.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Ur=/([a-z-]*)\(.*?\)/g,Te={...et,getAnimatableNone:t=>{const e=t.match(Ur);return e?e.map(Ir).join(" "):t}},zr={...vs,color:O,backgroundColor:O,outlineColor:O,fill:O,stroke:O,borderColor:O,borderTopColor:O,borderRightColor:O,borderBottomColor:O,borderLeftColor:O,filter:Te,WebkitFilter:Te},Ge=t=>zr[t];function ii(t,e){let n=Ge(t);return n!==Te&&(n=et),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const oi=t=>/^0[^.\s]+$/.test(t);function Hr(t){if(typeof t=="number")return t===0;if(t!==null)return t==="none"||t==="0"||oi(t)}function Nr(t,e,n,s){const i=Pe(e,n);let r;Array.isArray(n)?r=[...n]:r=[null,n];const o=s.from!==void 0?s.from:t.get();let a;const c=[];for(let l=0;l<r.length;l++)r[l]===null&&(r[l]=l===0?o:r[l-1]),Hr(r[l])&&c.push(l),typeof r[l]=="string"&&r[l]!=="none"&&r[l]!=="0"&&(a=r[l]);if(i&&c.length&&a)for(let l=0;l<c.length;l++){const u=c[l];r[u]=ii(e,a)}return r}function qr({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:c,elapsed:l,...u}){return!!Object.keys(u).length}function $e(t,e){return t[e]||t.default||t}const Wr={skipAnimations:!1},Ke=(t,e,n,s={})=>i=>{const r=$e(s,t)||{},o=r.delay||s.delay||0;let{elapsed:a=0}=s;a=a-ct(o);const c=Nr(e,t,n,r),l=c[0],u=c[c.length-1],h=Pe(t,l),f=Pe(t,u);let d={keyframes:c,velocity:e.getVelocity(),ease:"easeOut",...r,delay:-a,onUpdate:p=>{e.set(p),r.onUpdate&&r.onUpdate(p)},onComplete:()=>{i(),r.onComplete&&r.onComplete()}};if(qr(r)||(d={...d,...Er(t,d)}),d.duration&&(d.duration=ct(d.duration)),d.repeatDelay&&(d.repeatDelay=ct(d.repeatDelay)),!h||!f||Io.current||r.type===!1||Wr.skipAnimations)return Lr(d);if(!s.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const p=Dr(e,t,d);if(p)return p}return _t(d)};function Xt(t){return!!(I(t)&&t.add)}const ri=t=>/^\-?\d*\.?\d+$/.test(t);function Ze(t,e){t.indexOf(e)===-1&&t.push(e)}function _e(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Xe{constructor(){this.subscriptions=[]}add(e){return Ze(this.subscriptions,e),()=>_e(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Gr=t=>!isNaN(parseFloat(t));class $r{constructor(e,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{this.prev=this.current,this.current=s;const{delta:r,timestamp:o}=j;this.lastUpdated!==o&&(this.timeDelta=r,this.lastUpdated=o,w.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>w.postRender(this.velocityCheck),this.velocityCheck=({timestamp:s})=>{s!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Gr(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Xe);const s=this.events[e].add(n);return e==="change"?()=>{s(),w.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ei(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function vt(t,e){return new $r(t,e)}const ai=t=>e=>e.test(t),Kr={test:t=>t==="auto",parse:t=>t},ci=[ut,P,q,Y,Qi,Yi,Kr],bt=t=>ci.find(ai(t)),Zr=[...ci,O,et],_r=t=>Zr.find(ai(t));function Xr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,vt(n))}function Yr(t,e){const n=ie(t,e);let{transitionEnd:s={},transition:i={},...r}=n?t.makeTargetAnimatable(n,!1):{};r={...r,...s};for(const o in r){const a=po(r[o]);Xr(t,o,a)}}function Qr(t,e,n){var s,i;const r=Object.keys(e).filter(a=>!t.hasValue(a)),o=r.length;if(o)for(let a=0;a<o;a++){const c=r[a],l=e[c];let u=null;Array.isArray(l)&&(u=l[0]),u===null&&(u=(i=(s=n[c])!==null&&s!==void 0?s:t.readValue(c))!==null&&i!==void 0?i:e[c]),u!=null&&(typeof u=="string"&&(ri(u)||oi(u))?u=parseFloat(u):!_r(u)&&et.test(l)&&(u=ii(c,l)),t.addValue(c,vt(u,{owner:t})),n[c]===void 0&&(n[c]=u),u!==null&&t.setBaseTarget(c,u))}}function Jr(t,e){return e?(e[t]||e.default||e).from:void 0}function ta(t,e,n){const s={};for(const i in t){const r=Jr(i,e);if(r!==void 0)s[i]=r;else{const o=n.getValue(i);o&&(s[i]=o.get())}}return s}function ea({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function na(t,e){const n=t.get();if(Array.isArray(e)){for(let s=0;s<e.length;s++)if(e[s]!==n)return!0}else return n!==e}function li(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e);const c=t.getValue("willChange");s&&(r=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const h in a){const f=t.getValue(h),d=a[h];if(!f||d===void 0||u&&ea(u,h))continue;const p={delay:n,elapsed:0,...$e(r||{},h)};if(window.HandoffAppearAnimations){const T=t.getProps()[us];if(T){const x=window.HandoffAppearAnimations(T,h,f,w);x!==null&&(p.elapsed=x,p.isHandoff=!0)}}let m=!p.isHandoff&&!na(f,d);if(p.type==="spring"&&(f.getVelocity()||p.velocity)&&(m=!1),f.animation&&(m=!1),m)continue;f.start(Ke(h,f,d,t.shouldReduceMotion&&lt.has(h)?{type:!1}:p));const v=f.animation;Xt(c)&&(c.add(h),v.then(()=>c.remove(h))),l.push(v)}return o&&Promise.all(l).then(()=>{o&&Yr(t,o)}),l}function be(t,e,n={}){const s=ie(t,e,n.custom);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(li(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:l=0,staggerChildren:u,staggerDirection:h}=i;return sa(t,e,l+c,u,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[c,l]=a==="beforeChildren"?[r,o]:[o,r];return c().then(()=>l())}else return Promise.all([r(),o(n.delay)])}function sa(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,c=i===1?(l=0)=>l*s:(l=0)=>a-l*s;return Array.from(t.variantChildren).sort(ia).forEach((l,u)=>{l.notify("AnimationStart",e),o.push(be(l,e,{...r,delay:n+c(u)}).then(()=>l.notify("AnimationComplete",e)))}),Promise.all(o)}function ia(t,e){return t.sortNodePosition(e)}function oa(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>be(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=be(t,e,n);else{const i=typeof e=="function"?ie(t,e,n.custom):e;s=Promise.all(li(t,i,n))}return s.then(()=>t.notify("AnimationComplete",e))}const ra=[...Re].reverse(),aa=Re.length;function ca(t){return e=>Promise.all(e.map(({animation:n,options:s})=>oa(t,n,s)))}function la(t){let e=ca(t);const n=ha();let s=!0;const i=(c,l)=>{const u=ie(t,l);if(u){const{transition:h,transitionEnd:f,...d}=u;c={...c,...d,...f}}return c};function r(c){e=c(t)}function o(c,l){const u=t.getProps(),h=t.getVariantContext(!0)||{},f=[],d=new Set;let p={},m=1/0;for(let T=0;T<aa;T++){const x=ra[T],g=n[x],k=u[x]!==void 0?u[x]:h[x],M=Dt(k),R=x===l?g.isActive:null;R===!1&&(m=T);let C=k===h[x]&&k!==u[x]&&M;if(C&&s&&t.manuallyAnimateOnMount&&(C=!1),g.protectedKeys={...p},!g.isActive&&R===null||!k&&!g.prevProp||te(k)||typeof k=="boolean")continue;let D=ua(g.prevProp,k)||x===l&&g.isActive&&!C&&M||T>m&&M,W=!1;const ht=Array.isArray(k)?k:[k];let N=ht.reduce(i,{});R===!1&&(N={});const{prevResolvedValues:G={}}=g,E={...G,...N},ft=F=>{D=!0,d.has(F)&&(W=!0,d.delete(F)),g.needsAnimating[F]=!0};for(const F in E){const X=N[F],kt=G[F];if(p.hasOwnProperty(F))continue;let dt=!1;$t(X)&&$t(kt)?dt=!Rs(X,kt):dt=X!==kt,dt?X!==void 0?ft(F):d.add(F):X!==void 0&&d.has(F)?ft(F):g.protectedKeys[F]=!0}g.prevProp=k,g.prevResolvedValues=N,g.isActive&&(p={...p,...N}),s&&t.blockInitialAnimation&&(D=!1),D&&(!C||W)&&f.push(...ht.map(F=>({animation:F,options:{type:x,...c}})))}if(d.size){const T={};d.forEach(x=>{const g=t.getBaseTarget(x);g!==void 0&&(T[x]=g)}),f.push({animation:T})}let v=!!f.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(v=!1),s=!1,v?e(f):Promise.resolve()}function a(c,l,u){var h;if(n[c].isActive===l)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(d=>{var p;return(p=d.animationState)===null||p===void 0?void 0:p.setActive(c,l)}),n[c].isActive=l;const f=o(u,c);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n}}function ua(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Rs(e,t):!1}function it(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ha(){return{animate:it(!0),whileInView:it(),whileHover:it(),whileTap:it(),whileDrag:it(),whileFocus:it(),exit:it()}}class fa extends nt{constructor(e){super(e),e.animationState||(e.animationState=la(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),te(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let da=0;class pa extends nt{constructor(){super(...arguments),this.id=da++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n,custom:s}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const r=this.node.animationState.setActive("exit",!e,{custom:s??this.node.getProps().custom});n&&!e&&r.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const ma={animation:{Feature:fa},exit:{Feature:pa}},kn=(t,e)=>Math.abs(t-e);function ya(t,e){const n=kn(t.x,e.x),s=kn(t.y,e.y);return Math.sqrt(n**2+s**2)}class ui{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=pe(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=ya(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:p}=h,{timestamp:m}=j;this.history.push({...p,timestamp:m});const{onStart:v,onMove:T}=this.handlers;f||(v&&v(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),T&&T(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=de(f,this.transformPagePoint),w.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:p,resumeAnimation:m}=this.handlers;if(this.dragSnapToOrigin&&m&&m(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=pe(h.type==="pointercancel"?this.lastMoveEventInfo:de(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,v),p&&p(h,v)},!Cs(e))return;this.dragSnapToOrigin=r,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const o=se(e),a=de(o,this.transformPagePoint),{point:c}=a,{timestamp:l}=j;this.history=[{...c,timestamp:l}];const{onSessionStart:u}=n;u&&u(e,pe(a,this.history)),this.removeListeners=J(K(this.contextWindow,"pointermove",this.handlePointerMove),K(this.contextWindow,"pointerup",this.handlePointerUp),K(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),_(this.updatePoint)}}function de(t,e){return e?{point:e(t.point)}:t}function Pn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function pe({point:t},e){return{point:t,delta:Pn(t,hi(e)),offset:Pn(t,ga(e)),velocity:va(e,.1)}}function ga(t){return t[0]}function hi(t){return t[t.length-1]}function va(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=hi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>ct(e)));)n--;if(!s)return{x:0,y:0};const r=Z(i.timestamp-s.timestamp);if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function U(t){return t.max-t.min}function Me(t,e=0,n=.01){return Math.abs(t-e)<=n}function Tn(t,e,n,s=.5){t.origin=s,t.originPoint=A(e.min,e.max,t.origin),t.scale=U(n)/U(e),(Me(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=A(n.min,n.max,t.origin)-t.originPoint,(Me(t.translate)||isNaN(t.translate))&&(t.translate=0)}function St(t,e,n,s){Tn(t.x,e.x,n.x,s?s.originX:void 0),Tn(t.y,e.y,n.y,s?s.originY:void 0)}function bn(t,e,n){t.min=n.min+e.min,t.max=t.min+U(e)}function xa(t,e,n){bn(t.x,e.x,n.x),bn(t.y,e.y,n.y)}function Mn(t,e,n){t.min=e.min-n.min,t.max=t.min+U(e)}function At(t,e,n){Mn(t.x,e.x,n.x),Mn(t.y,e.y,n.y)}function ka(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?A(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?A(n,t,s.max):Math.min(t,n)),t}function Vn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Pa(t,{top:e,left:n,bottom:s,right:i}){return{x:Vn(t.x,n,i),y:Vn(t.y,e,s)}}function wn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Ta(t,e){return{x:wn(t.x,e.x),y:wn(t.y,e.y)}}function ba(t,e){let n=.5;const s=U(t),i=U(e);return i>s?n=Rt(e.min,e.max-s,t.min):s>i&&(n=Rt(t.min,t.max-i,e.min)),tt(0,1,n)}function Ma(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Ve=.35;function Va(t=Ve){return t===!1?t=0:t===!0&&(t=Ve),{x:Cn(t,"left","right"),y:Cn(t,"top","bottom")}}function Cn(t,e,n){return{min:Sn(t,e),max:Sn(t,n)}}function Sn(t,e){return typeof t=="number"?t:t[e]||0}const An=()=>({translate:0,scale:1,origin:0,originPoint:0}),yt=()=>({x:An(),y:An()}),Dn=()=>({min:0,max:0}),B=()=>({x:Dn(),y:Dn()});function H(t){return[t("x"),t("y")]}function fi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function wa({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Ca(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function me(t){return t===void 0||t===1}function we({scale:t,scaleX:e,scaleY:n}){return!me(t)||!me(e)||!me(n)}function ot(t){return we(t)||di(t)||t.z||t.rotate||t.rotateX||t.rotateY}function di(t){return Ln(t.x)||Ln(t.y)}function Ln(t){return t&&t!=="0%"}function Yt(t,e,n){const s=t-n,i=e*s;return n+i}function Rn(t,e,n,s,i){return i!==void 0&&(t=Yt(t,i,s)),Yt(t,n,s)+e}function Ce(t,e=0,n=1,s,i){t.min=Rn(t.min,e,n,s,i),t.max=Rn(t.max,e,n,s,i)}function pi(t,{x:e,y:n}){Ce(t.x,e.translate,e.scale,e.originPoint),Ce(t.y,n.translate,n.scale,n.originPoint)}function Sa(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,o;for(let a=0;a<i;a++){r=n[a],o=r.projectionDelta;const c=r.instance;c&&c.style&&c.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&gt(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,pi(t,o)),s&&ot(r.latestValues)&&gt(t,r.latestValues))}e.x=Bn(e.x),e.y=Bn(e.y)}function Bn(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Q(t,e){t.min=t.min+e,t.max=t.max+e}function Fn(t,e,[n,s,i]){const r=e[i]!==void 0?e[i]:.5,o=A(t.min,t.max,r);Ce(t,e[n],e[s],o,e.scale)}const Aa=["x","scaleX","originX"],Da=["y","scaleY","originY"];function gt(t,e){Fn(t.x,e,Aa),Fn(t.y,e,Da)}function mi(t,e){return fi(Ca(t.getBoundingClientRect(),e))}function La(t,e,n){const s=mi(t,n),{scroll:i}=e;return i&&(Q(s.x,i.offset.x),Q(s.y,i.offset.y)),s}const yi=({current:t})=>t?t.ownerDocument.defaultView:null,Ra=new WeakMap;class Ba{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=B(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(se(u,"page").point)},r=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:p}=this.getProps();if(f&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=As(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),H(v=>{let T=this.getAxisMotionValue(v).get()||0;if(q.test(T)){const{projection:x}=this.visualElement;if(x&&x.layout){const g=x.layout.layoutBox[v];g&&(T=U(g)*(parseFloat(T)/100))}}this.originPoint[v]=T}),p&&w.update(()=>p(u,h),!1,!0);const{animationState:m}=this.visualElement;m&&m.setActive("whileDrag",!0)},o=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:p,onDrag:m}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:v}=h;if(d&&this.currentDirection===null){this.currentDirection=Fa(v),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",h.point,v),this.updateAxis("y",h.point,v),this.visualElement.render(),m&&m(u,h)},a=(u,h)=>this.stop(u,h),c=()=>H(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:l}=this.getProps();this.panSession=new ui(e,{onSessionStart:i,onStart:r,onMove:o,onSessionEnd:a,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,contextWindow:yi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r&&w.update(()=>r(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Ht(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=ka(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,r=this.constraints;n&&pt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Pa(i.layoutBox,n):this.constraints=!1,this.elastic=Va(s),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&H(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=Ma(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!pt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=La(s,i.root,this.visualElement.getTransformPagePoint());let o=Ta(i.layout.layoutBox,r);if(n){const a=n(wa(o));this.hasMutatedConstraints=!!a,a&&(o=fi(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},l=H(u=>{if(!Ht(u,n,this.currentDirection))return;let h=c&&c[u]||{};o&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,p={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...r,...h};return this.startAxisValueAnimation(u,p)});return Promise.all(l).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(Ke(e,s,0,n))}stopAnimation(){H(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){H(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n="_drag"+e.toUpperCase(),s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){H(n=>{const{drag:s}=this.getProps();if(!Ht(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-A(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!pt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};H(o=>{const a=this.getAxisMotionValue(o);if(a){const c=a.get();i[o]=ba({min:c,max:c},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),H(o=>{if(!Ht(o,e,null))return;const a=this.getAxisMotionValue(o),{min:c,max:l}=this.constraints[o];a.set(A(c,l,i[o]))})}addListeners(){if(!this.visualElement.current)return;Ra.set(this.visualElement,this);const e=this.visualElement.current,n=K(e,"pointerdown",c=>{const{drag:l,dragListener:u=!0}=this.getProps();l&&u&&this.start(c)}),s=()=>{const{dragConstraints:c}=this.getProps();pt(c)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),s();const o=$(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:c,hasLayoutChanged:l})=>{this.isDragging&&l&&(H(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=c[u].translate,h.set(h.get()+c[u].translate))}),this.visualElement.render())});return()=>{o(),n(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=Ve,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function Ht(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Fa(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class ja extends nt{constructor(e){super(e),this.removeGroupControls=L,this.removeListeners=L,this.controls=new Ba(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||L}unmount(){this.removeGroupControls(),this.removeListeners()}}const jn=t=>(e,n)=>{t&&w.update(()=>t(e,n))};class Ea extends nt{constructor(){super(...arguments),this.removePointerDownListener=L}onPointerDown(e){this.session=new ui(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:yi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:jn(e),onStart:jn(n),onMove:s,onEnd:(r,o)=>{delete this.session,i&&w.update(()=>i(r,o))}}}mount(){this.removePointerDownListener=K(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function Oa(){const t=b.useContext(De);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=b.useId();return b.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}const qt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function En(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Mt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(P.test(t))t=parseFloat(t);else return t;const n=En(t,e.target.x),s=En(t,e.target.y);return`${n}% ${s}%`}},Ia={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=et.parse(t);if(i.length>5)return s;const r=et.createTransformer(t),o=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,c=n.y.scale*e.y;i[0+o]/=a,i[1+o]/=c;const l=A(a,c,.5);return typeof i[2+o]=="number"&&(i[2+o]/=l),typeof i[3+o]=="number"&&(i[3+o]/=l),r(i)}};class Ua extends as.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;Wi(za),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),qt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,o=s.projection;return o&&(o.isPresent=r,i||e.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?o.promote():o.relegate()||w.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function gi(t){const[e,n]=Oa(),s=b.useContext(fs);return as.createElement(Ua,{...t,layoutGroup:s,switchLayoutGroup:b.useContext(ds),isPresent:e,safeToRemove:n})}const za={borderRadius:{...Mt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Mt,borderTopRightRadius:Mt,borderBottomLeftRadius:Mt,borderBottomRightRadius:Mt,boxShadow:Ia},vi=["TopLeft","TopRight","BottomLeft","BottomRight"],Ha=vi.length,On=t=>typeof t=="string"?parseFloat(t):t,In=t=>typeof t=="number"||P.test(t);function Na(t,e,n,s,i,r){i?(t.opacity=A(0,n.opacity!==void 0?n.opacity:1,qa(s)),t.opacityExit=A(e.opacity!==void 0?e.opacity:1,0,Wa(s))):r&&(t.opacity=A(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let o=0;o<Ha;o++){const a=`border${vi[o]}Radius`;let c=Un(e,a),l=Un(n,a);if(c===void 0&&l===void 0)continue;c||(c=0),l||(l=0),c===0||l===0||In(c)===In(l)?(t[a]=Math.max(A(On(c),On(l),s),0),(q.test(l)||q.test(c))&&(t[a]+="%")):t[a]=l}(e.rotate||n.rotate)&&(t.rotate=A(e.rotate||0,n.rotate||0,s))}function Un(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const qa=xi(0,.5,Hs),Wa=xi(.5,.95,L);function xi(t,e,n){return s=>s<t?0:s>e?1:n(Rt(t,e,s))}function zn(t,e){t.min=e.min,t.max=e.max}function z(t,e){zn(t.x,e.x),zn(t.y,e.y)}function Hn(t,e,n,s,i){return t-=e,t=Yt(t,1/n,s),i!==void 0&&(t=Yt(t,1/i,s)),t}function Ga(t,e=0,n=1,s=.5,i,r=t,o=t){if(q.test(e)&&(e=parseFloat(e),e=A(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=A(r.min,r.max,s);t===r&&(a-=e),t.min=Hn(t.min,e,n,a,i),t.max=Hn(t.max,e,n,a,i)}function Nn(t,e,[n,s,i],r,o){Ga(t,e[n],e[s],e[i],e.scale,r,o)}const $a=["x","scaleX","originX"],Ka=["y","scaleY","originY"];function qn(t,e,n,s){Nn(t.x,e,$a,n?n.x:void 0,s?s.x:void 0),Nn(t.y,e,Ka,n?n.y:void 0,s?s.y:void 0)}function Wn(t){return t.translate===0&&t.scale===1}function ki(t){return Wn(t.x)&&Wn(t.y)}function Za(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function Pi(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Gn(t){return U(t.x)/U(t.y)}class _a{constructor(){this.members=[]}add(e){Ze(this.members,e),e.scheduleRender()}remove(e){if(_e(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function $n(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y;if((i||r)&&(s=`translate3d(${i}px, ${r}px, 0) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:c,rotateX:l,rotateY:u}=n;c&&(s+=`rotate(${c}deg) `),l&&(s+=`rotateX(${l}deg) `),u&&(s+=`rotateY(${u}deg) `)}const o=t.x.scale*e.x,a=t.y.scale*e.y;return(o!==1||a!==1)&&(s+=`scale(${o}, ${a})`),s||"none"}const Xa=(t,e)=>t.depth-e.depth;class Ya{constructor(){this.children=[],this.isDirty=!1}add(e){Ze(this.children,e),this.isDirty=!0}remove(e){_e(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Xa),this.isDirty=!1,this.children.forEach(e)}}function Qa(t,e){const n=performance.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(_(s),t(r-e))};return w.read(s,!0),()=>_(s)}function Ja(t){window.MotionDebug&&window.MotionDebug.record(t)}function tc(t){return t instanceof SVGElement&&t.tagName!=="svg"}function ec(t,e,n){const s=I(t)?t:vt(t);return s.start(Ke("",s,e,n)),s.animation}const Kn=["","X","Y","Z"],nc={visibility:"hidden"},Zn=1e3;let sc=0;const rt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Ti({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o={},a=e?.()){this.id=sc++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rt.totalNodes=rt.resolvedTargetDeltas=rt.recalculatedProjection=0,this.nodes.forEach(rc),this.nodes.forEach(hc),this.nodes.forEach(fc),this.nodes.forEach(ac),Ja(rt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ya)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Xe),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const c=this.eventHandlers.get(o);c&&c.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=tc(o),this.instance=o;const{layoutId:c,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(l||c)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Qa(f,250),qt.hasAnimatedSinceResize&&(qt.hasAnimatedSinceResize=!1,this.nodes.forEach(Xn))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&u&&(c||l)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:d,layout:p})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||u.getDefaultTransition()||gc,{onLayoutAnimationStart:v,onLayoutAnimationComplete:T}=u.getProps(),x=!this.targetLayout||!Pi(this.targetLayout,p)||d,g=!f&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,g);const k={...$e(m,"layout"),onPlay:v,onComplete:T};(u.shouldReduceMotion||this.options.layoutRoot)&&(k.delay=0,k.type=!1),this.startAnimation(k)}else f||Xn(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=p})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,_(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(dc),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:c}=this.options;if(a===void 0&&!c)return;const l=this.getTransformTemplate();this.prevTransformTemplateValue=l?l(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(_n);return}this.isUpdating||this.nodes.forEach(lc),this.isUpdating=!1,this.nodes.forEach(uc),this.nodes.forEach(ic),this.nodes.forEach(oc),this.clearAllSnapshots();const a=performance.now();j.delta=tt(0,1e3/60,a-j.timestamp),j.timestamp=a,j.isProcessing=!0,re.update.process(j),re.preRender.process(j),re.render.process(j),j.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(cc),this.sharedNodes.forEach(pc)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,w.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){w.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=B(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!ki(this.projectionDelta),c=this.getTransformTemplate(),l=c?c(this.latestValues,""):void 0,u=l!==this.prevTransformTemplateValue;o&&(a||ot(this.latestValues)||u)&&(i(this.instance,l),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return o&&(c=this.removeTransform(c)),vc(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return B();const a=o.measureViewportBox(),{scroll:c}=this.root;return c&&(Q(a.x,c.offset.x),Q(a.y,c.offset.y)),a}removeElementScroll(o){const a=B();z(a,o);for(let c=0;c<this.path.length;c++){const l=this.path[c],{scroll:u,options:h}=l;if(l!==this.root&&u&&h.layoutScroll){if(u.isRoot){z(a,o);const{scroll:f}=this.root;f&&(Q(a.x,-f.offset.x),Q(a.y,-f.offset.y))}Q(a.x,u.offset.x),Q(a.y,u.offset.y)}}return a}applyTransform(o,a=!1){const c=B();z(c,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&gt(c,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),ot(u.latestValues)&&gt(c,u.latestValues)}return ot(this.latestValues)&&gt(c,this.latestValues),c}removeTransform(o){const a=B();z(a,o);for(let c=0;c<this.path.length;c++){const l=this.path[c];if(!l.instance||!ot(l.latestValues))continue;we(l.latestValues)&&l.updateSnapshot();const u=B(),h=l.measurePageBox();z(u,h),qn(a,l.latestValues,l.snapshot?l.snapshot.layoutBox:void 0,u)}return ot(this.latestValues)&&qn(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==j.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const c=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=c.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=c.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=c.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==c;if(!(o||l&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=j.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=B(),this.relativeTargetOrigin=B(),At(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),z(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=B(),this.targetWithTransforms=B()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),xa(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):z(this.target,this.layout.layoutBox),pi(this.target,this.targetDelta)):z(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=B(),this.relativeTargetOrigin=B(),At(this.relativeTargetOrigin,this.target,d.target),z(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||we(this.parent.latestValues)||di(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),c=!!this.resumingFrom||this!==a;let l=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(l=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===j.timestamp&&(l=!1),l)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;z(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Sa(this.layoutCorrected,this.treeScale,this.path,c),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:p}=a;if(!p){this.projectionTransform&&(this.projectionDelta=yt(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=yt(),this.projectionDeltaWithTransform=yt());const m=this.projectionTransform;St(this.projectionDelta,this.layoutCorrected,p,this.latestValues),this.projectionTransform=$n(this.projectionDelta,this.treeScale),(this.projectionTransform!==m||this.treeScale.x!==f||this.treeScale.y!==d)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p)),rt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const c=this.snapshot,l=c?c.latestValues:{},u={...this.latestValues},h=yt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=B(),d=c?c.source:void 0,p=this.layout?this.layout.source:void 0,m=d!==p,v=this.getStack(),T=!v||v.members.length<=1,x=!!(m&&!T&&this.options.crossfade===!0&&!this.path.some(yc));this.animationProgress=0;let g;this.mixTargetDelta=k=>{const M=k/1e3;Yn(h.x,o.x,M),Yn(h.y,o.y,M),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(At(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),mc(this.relativeTarget,this.relativeTargetOrigin,f,M),g&&Za(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=B()),z(g,this.relativeTarget)),m&&(this.animationValues=u,Na(u,l,this.latestValues,M,x,T)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=M},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(_(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=w.update(()=>{qt.hasAnimatedSinceResize=!0,this.currentAnimation=ec(0,Zn,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Zn),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:c,layout:l,latestValues:u}=o;if(!(!a||!c||!l)){if(this!==o&&this.layout&&l&&bi(this.options.animationType,this.layout.layoutBox,l.layoutBox)){c=this.target||B();const h=U(this.layout.layoutBox.x);c.x.min=o.target.x.min,c.x.max=c.x.min+h;const f=U(this.layout.layoutBox.y);c.y.min=o.target.y.min,c.y.max=c.y.min+f}z(a,c),gt(a,u),St(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new _a),this.sharedNodes.get(o).add(a);const l=a.options.initialPromotionConfig;a.promote({transition:l?l.transition:void 0,preserveFollowOpacity:l&&l.shouldPreserveFollowOpacity?l.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:c}={}){const l=this.getStack();l&&l.promote(this,c),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:c}=o;if((c.rotate||c.rotateX||c.rotateY||c.rotateZ)&&(a=!0),!a)return;const l={};for(let u=0;u<Kn.length;u++){const h="rotate"+Kn[u];c[h]&&(l[h]=c[h],o.setStaticValue(h,0))}o.render();for(const u in l)o.setStaticValue(u,l[u]);o.scheduleRender()}getProjectionStyles(o){var a,c;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nc;const l={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,l.opacity="",l.pointerEvents=Nt(o?.pointerEvents)||"",l.transform=u?u(this.latestValues,""):"none",l;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const m={};return this.options.layoutId&&(m.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,m.pointerEvents=Nt(o?.pointerEvents)||""),this.hasProjected&&!ot(this.latestValues)&&(m.transform=u?u({},""):"none",this.hasProjected=!1),m}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),l.transform=$n(this.projectionDeltaWithTransform,this.treeScale,f),u&&(l.transform=u(f,l.transform));const{x:d,y:p}=this.projectionDelta;l.transformOrigin=`${d.origin*100}% ${p.origin*100}% 0`,h.animationValues?l.opacity=h===this?(c=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:l.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const m in Wt){if(f[m]===void 0)continue;const{correct:v,applyTo:T}=Wt[m],x=l.transform==="none"?f[m]:v(f[m],h);if(T){const g=T.length;for(let k=0;k<g;k++)l[T[k]]=x}else l[m]=x}return this.options.layoutId&&(l.pointerEvents=h===this?Nt(o?.pointerEvents)||"":"none"),l}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(_n),this.root.sharedNodes.clear()}}}function ic(t){t.updateLayout()}function oc(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:r}=t.options,o=n.source!==t.layout.source;r==="size"?H(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],d=U(f);f.min=s[h].min,f.max=f.min+d}):bi(r,n.layoutBox,s)&&H(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],d=U(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=yt();St(a,s,n.layoutBox);const c=yt();o?St(c,t.applyTransform(i,!0),n.measuredBox):St(c,s,n.layoutBox);const l=!ki(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const p=B();At(p,n.layoutBox,f.layoutBox);const m=B();At(m,s,d.layoutBox),Pi(p,m)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=m,t.relativeTargetOrigin=p,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:c,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function rc(t){rt.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ac(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function cc(t){t.clearSnapshot()}function _n(t){t.clearMeasurements()}function lc(t){t.isLayoutDirty=!1}function uc(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Xn(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function hc(t){t.resolveTargetDelta()}function fc(t){t.calcProjection()}function dc(t){t.resetRotation()}function pc(t){t.removeLeadSnapshot()}function Yn(t,e,n){t.translate=A(e.translate,0,n),t.scale=A(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Qn(t,e,n,s){t.min=A(e.min,n.min,s),t.max=A(e.max,n.max,s)}function mc(t,e,n,s){Qn(t.x,e.x,n.x,s),Qn(t.y,e.y,n.y,s)}function yc(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const gc={duration:.45,ease:[.4,0,.1,1]},Jn=t=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(t),ts=Jn("applewebkit/")&&!Jn("chrome/")?Math.round:L;function es(t){t.min=ts(t.min),t.max=ts(t.max)}function vc(t){es(t.x),es(t.y)}function bi(t,e,n){return t==="position"||t==="preserve-aspect"&&!Me(Gn(e),Gn(n),.2)}const xc=Ti({attachResizeListener:(t,e)=>$(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ye={current:void 0},Mi=Ti({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ye.current){const t=new xc({});t.mount(window),t.setOptions({layoutScroll:!0}),ye.current=t}return ye.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),kc={pan:{Feature:Ea},drag:{Feature:ja,ProjectionNode:Mi,MeasureLayout:gi}},Pc=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Tc(t){const e=Pc.exec(t);if(!e)return[,];const[,n,s]=e;return[n,s]}function Se(t,e,n=1){const[s,i]=Tc(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return ri(o)?parseFloat(o):o}else return ge(i)?Se(i,e,n+1):i}function bc(t,{...e},n){const s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const r=i.get();if(!ge(r))return;const o=Se(r,s);o&&i.set(o)});for(const i in e){const r=e[i];if(!ge(r))continue;const o=Se(r,s);o&&(e[i]=o,n||(n={}),n[i]===void 0&&(n[i]=r))}return{target:e,transitionEnd:n}}const Mc=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Vi=t=>Mc.has(t),Vc=t=>Object.keys(t).some(Vi),ns=t=>t===ut||t===P,ss=(t,e)=>parseFloat(t.split(", ")[e]),is=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/);if(i)return ss(i[1],e);{const r=s.match(/^matrix\((.+)\)$/);return r?ss(r[1],t):0}},wc=new Set(["x","y","z"]),Cc=Bt.filter(t=>!wc.has(t));function Sc(t){const e=[];return Cc.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const xt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:is(4,13),y:is(5,14)};xt.translateX=xt.x;xt.translateY=xt.y;const Ac=(t,e,n)=>{const s=e.measureViewportBox(),i=e.current,r=getComputedStyle(i),{display:o}=r,a={};o==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(l=>{a[l]=xt[l](s,r)}),e.render();const c=e.measureViewportBox();return n.forEach(l=>{const u=e.getValue(l);u&&u.jump(a[l]),t[l]=xt[l](c,r)}),t},Dc=(t,e,n={},s={})=>{e={...e},s={...s};const i=Object.keys(e).filter(Vi);let r=[],o=!1;const a=[];if(i.forEach(c=>{const l=t.getValue(c);if(!t.hasValue(c))return;let u=n[c],h=bt(u);const f=e[c];let d;if($t(f)){const p=f.length,m=f[0]===null?1:0;u=f[m],h=bt(u);for(let v=m;v<p&&f[v]!==null;v++)d?He(bt(f[v])===d):d=bt(f[v])}else d=bt(f);if(h!==d)if(ns(h)&&ns(d)){const p=l.get();typeof p=="string"&&l.set(parseFloat(p)),typeof f=="string"?e[c]=parseFloat(f):Array.isArray(f)&&d===P&&(e[c]=f.map(parseFloat))}else h?.transform&&d?.transform&&(u===0||f===0)?u===0?l.set(d.transform(u)):e[c]=h.transform(f):(o||(r=Sc(t),o=!0),a.push(c),s[c]=s[c]!==void 0?s[c]:e[c],l.jump(f))}),a.length){const c=a.indexOf("height")>=0?window.pageYOffset:null,l=Ac(e,t,a);return r.length&&r.forEach(([u,h])=>{t.getValue(u).set(h)}),t.render(),Jt&&c!==null&&window.scrollTo({top:c}),{target:l,transitionEnd:s}}else return{target:e,transitionEnd:s}};function Lc(t,e,n,s){return Vc(e)?Dc(t,e,n,s):{target:e,transitionEnd:s}}const Rc=(t,e,n,s)=>{const i=bc(t,e,s);return e=i.target,s=i.transitionEnd,Lc(t,e,n,s)},Ae={current:null},wi={current:!1};function Bc(){if(wi.current=!0,!!Jt)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ae.current=t.matches;t.addListener(e),e()}else Ae.current=!1}function Fc(t,e,n){const{willChange:s}=e;for(const i in e){const r=e[i],o=n[i];if(I(r))t.addValue(i,r),Xt(s)&&s.add(i);else if(I(o))t.addValue(i,vt(r,{owner:t})),Xt(s)&&s.remove(i);else if(o!==r)if(t.hasValue(i)){const a=t.getValue(i);!a.hasAnimated&&a.set(r)}else{const a=t.getStaticValue(i);t.addValue(i,vt(a!==void 0?a:r,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const os=new WeakMap,Ci=Object.keys(Lt),jc=Ci.length,rs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Ec=Be.length;class Oc{constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>w.render(this.render,!1,!0);const{latestValues:a,renderState:c}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=ee(n),this.isVariantNode=hs(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(n,{});for(const h in u){const f=u[h];a[h]!==void 0&&I(f)&&(f.set(a[h],!1),Xt(l)&&l.add(h))}}scrapeMotionValuesFromProps(e,n){return{}}mount(e){this.current=e,os.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),wi.current||Bc(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ae.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){os.delete(this.current),this.projection&&this.projection.unmount(),_(this.notifyUpdate),_(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,n){const s=lt.has(e),i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&w.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),r()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},s,i,r){let o,a;for(let c=0;c<jc;c++){const l=Ci[c],{isEnabled:u,Feature:h,ProjectionNode:f,MeasureLayout:d}=Lt[l];f&&(o=f),u(n)&&(!this.features[l]&&h&&(this.features[l]=new h(this)),d&&(a=d))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:c,layout:l,drag:u,dragConstraints:h,layoutScroll:f,layoutRoot:d}=n;this.projection.setOptions({layoutId:c,layout:l,alwaysMeasureLayout:!!u||h&&pt(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof l=="string"?l:"both",initialPromotionConfig:r,layoutScroll:f,layoutRoot:d})}return a}updateFeatures(){for(const e in this.features){const n=this.features[e];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):B()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<rs.length;s++){const i=rs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Fc(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const s=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(s.initial=this.props.initial),s}const n={};for(let s=0;s<Ec;s++){const i=Be[s],r=this.props[i];(Dt(r)||r===!1)&&(n[i]=r)}return n}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=vt(n,{owner:this}),this.addValue(e,s)),s}readValue(e){var n;return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props,i=typeof s=="string"||typeof s=="object"?(n=ze(this.props,s))===null||n===void 0?void 0:n[e]:void 0;if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!I(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Xe),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Si extends Oc{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s},{transformValues:i},r){let o=ta(s,e||{},this);if(i&&(n&&(n=i(n)),s&&(s=i(s)),o&&(o=i(o))),r){Qr(this,s,o);const a=Rc(this,s,o,n);n=a.transitionEnd,s=a.target}return{transition:e,transitionEnd:n,...s}}}function Ic(t){return window.getComputedStyle(t)}class Uc extends Si{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,n){if(lt.has(n)){const s=Ge(n);return s&&s.default||0}else{const s=Ic(e),i=(ys(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return mi(e,n)}build(e,n,s,i){je(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,n){return Ue(e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;I(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,s,i){Ts(e,n,s,i)}}class zc extends Si{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(lt.has(n)){const s=Ge(n);return s&&s.default||0}return n=bs.has(n)?n:Le(n),e.getAttribute(n)}measureInstanceViewportBox(){return B()}scrapeMotionValuesFromProps(e,n){return Vs(e,n)}build(e,n,s,i){Oe(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){Ms(e,n,s,i)}mount(e){this.isSVGTag=Ie(e.tagName),super.mount(e)}}const Hc=(t,e)=>Fe(t)?new zc(e,{enableHardwareAcceleration:!1}):new Uc(e,{enableHardwareAcceleration:!0}),Nc={layout:{ProjectionNode:Mi,MeasureLayout:gi}},qc={...ma,...jo,...kc,...Nc},bu=Ni((t,e)=>To(t,e,qc,Hc));export{Yl as $,Kc as A,hl as B,fl as C,ml as D,yl as E,gl as F,Ml as G,Vl as H,Al as I,ol as J,il as K,Bl as L,Ol as M,xl as N,pu as O,Gl as P,nl as Q,Kl as R,Xl as S,fu as T,mu as U,bu as V,gu as W,xu as X,ku as Y,Pu as Z,$c as _,su as a,vl as a0,bl as a1,wl as a2,uu as a3,ul as a4,Jl as a5,vu as a6,cl as a7,ql as a8,Zl as a9,sl as aa,dl as ab,du as ac,Ql as ad,Cl as ae,Fl as af,Ul as ag,pl as ah,Ll as ai,Jc as aj,Wl as ak,ou as al,Zc as am,Yc as an,Hl as ao,rl as ap,al as aq,tl as ar,el as as,tu as at,Il as au,jl as b,El as c,Sl as d,nu as e,zl as f,$l as g,eu as h,ru as i,Xc as j,hu as k,Qc as l,yu as m,Dl as n,ll as o,Tl as p,au as q,_l as r,iu as s,cu as t,Pl as u,Nl as v,kl as w,Rl as x,_c as y,lu as z};
