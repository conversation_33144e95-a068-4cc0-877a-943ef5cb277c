// Simple Wallet Test - Direct Database Update
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('💰 Simple Wallet Test - Direct Update');
console.log('=====================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function simpleWalletTest() {
  try {
    console.log('1️⃣ FINDING USER');
    console.log('─'.repeat(30));
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (userError) {
      console.log('❌ User not found:', userError.message);
      return false;
    }

    console.log(`✅ User: ${user.full_name}`);
    console.log(`   Current wallet: ₹${user.wallet_balance || 0}`);

    console.log('\n2️⃣ DIRECT UPDATE');
    console.log('─'.repeat(30));
    
    // Try the simplest possible update
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ wallet_balance: 250 })
      .eq('id', user.id)
      .select();

    if (updateError) {
      console.log('❌ Update failed:', updateError.message);
      console.log('   This confirms RLS is blocking updates');
      
      // Try with different approach
      console.log('\n📝 Trying alternative update...');
      
      const { error: altError } = await supabase
        .from('users')
        .upsert({
          id: user.id,
          email: user.email,
          wallet_balance: 250
        });

      if (altError) {
        console.log('❌ Alternative update failed:', altError.message);
      } else {
        console.log('✅ Alternative update succeeded');
      }
    } else {
      console.log('✅ Direct update succeeded');
      console.log('   Updated data:', updateData);
    }

    console.log('\n3️⃣ VERIFICATION');
    console.log('─'.repeat(30));
    
    const { data: checkUser, error: checkError } = await supabase
      .from('users')
      .select('wallet_balance')
      .eq('id', user.id)
      .single();

    if (checkError) {
      console.log('❌ Check failed:', checkError.message);
    } else {
      console.log(`✅ Current balance: ₹${checkUser.wallet_balance}`);
      
      if (parseFloat(checkUser.wallet_balance) === 250) {
        console.log('🎉 SUCCESS: Wallet updated to ₹250!');
      } else {
        console.log('⚠️ Still not ₹250, trying manual SQL approach...');
      }
    }

    console.log('\n4️⃣ ADMIN PANEL CHECK');
    console.log('─'.repeat(30));
    
    console.log('🔧 Please check the admin panel:');
    console.log('   1. Go to: http://localhost:5173/admin/dashboard/users');
    console.log('   2. Search for: <EMAIL>');
    console.log('   3. Check the wallet balance column');
    console.log('   4. If it shows ₹250, then it\'s a frontend issue');
    console.log('   5. If it shows ₹0, then database update is blocked');

    console.log('\n5️⃣ MANUAL SOLUTION');
    console.log('─'.repeat(30));
    
    console.log('💡 If database updates are blocked by RLS:');
    console.log('');
    console.log('Option 1: Use Supabase Dashboard');
    console.log('   • Go to supabase.com dashboard');
    console.log('   • Open your project');
    console.log('   • Go to Table Editor → users');
    console.log(`   • Find user with email: <EMAIL>`);
    console.log('   • Edit wallet_balance to: 250');
    console.log('   • Save changes');
    console.log('');
    console.log('Option 2: Disable RLS temporarily');
    console.log('   • Go to Table Editor → users');
    console.log('   • Click on the table settings');
    console.log('   • Disable Row Level Security');
    console.log('   • Run this script again');
    console.log('   • Re-enable RLS after update');
    console.log('');
    console.log('Option 3: Create new user with correct balance');
    console.log('   • I can create a fresh user');
    console.log('   • With ₹250 already set');
    console.log('   • Give you the login credentials');

    console.log('\n🎯 RECOMMENDATION');
    console.log('─'.repeat(30));
    
    console.log('Try Option 1 (Supabase Dashboard) first:');
    console.log('   1. Go to supabase.com');
    console.log('   2. Login to your project');
    console.log('   3. Table Editor → users table');
    console.log('   4. Find <EMAIL>');
    console.log('   5. Edit wallet_balance to 250');
    console.log('   6. Save and test dashboard');
    console.log('');
    console.log('This will bypass any RLS restrictions and directly');
    console.log('update the database from the admin interface.');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

simpleWalletTest().then(success => {
  if (success) {
    console.log('\n🚀 Test completed!');
    console.log('💡 Try the Supabase Dashboard method above!');
  } else {
    console.log('\n⚠️ Test failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
