// Quick Admin Login Test
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔐 Quick Admin Login Test');
console.log('========================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAdminLogin() {
  try {
    console.log('1️⃣ TESTING ADMIN LOGIN');
    console.log('─'.repeat(40));

    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456';

    console.log(`📝 Attempting login with: ${adminEmail}`);

    // Try to sign in
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: adminEmail,
      password: adminPassword
    });

    if (signInError) {
      console.log('❌ Sign in failed:', signInError.message);
      
      if (signInError.message.includes('Email not confirmed')) {
        console.log('');
        console.log('🔧 EMAIL CONFIRMATION ISSUE DETECTED');
        console.log('');
        console.log('SOLUTION 1: Manual Confirmation (Recommended)');
        console.log('1. Go to: https://supabase.com/dashboard');
        console.log('2. Select your project');
        console.log('3. Go to Authentication > Users');
        console.log(`4. Find user: ${adminEmail}`);
        console.log('5. Click on the user');
        console.log('6. Toggle "Email Confirmed" to ON');
        console.log('7. Save changes');
        console.log('');
        console.log('SOLUTION 2: Disable Email Confirmation');
        console.log('1. Go to Authentication > Settings');
        console.log('2. Turn OFF "Enable email confirmations"');
        console.log('3. Save settings');
        console.log('4. Try logging in again');
        console.log('');
        console.log('SOLUTION 3: Create New Admin Without Confirmation');
        console.log('1. Go to Authentication > Users');
        console.log('2. Click "Add user"');
        console.log(`3. Email: ${adminEmail}`);
        console.log(`4. Password: ${adminPassword}`);
        console.log('5. Check "Auto Confirm User"');
        console.log('6. Add user');
      }
      
      return false;
    }

    console.log('✅ Admin login successful!');
    console.log(`   User ID: ${signInData.user?.id}`);
    console.log(`   Email: ${signInData.user?.email}`);
    console.log(`   Email confirmed: ${signInData.user?.email_confirmed_at ? 'Yes' : 'No'}`);

    // Check if user has admin privileges
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, is_admin')
      .eq('email', adminEmail)
      .single();

    if (userError) {
      console.log('❌ Error checking user data:', userError.message);
    } else {
      console.log('✅ User data found:');
      console.log(`   Name: ${userData.full_name}`);
      console.log(`   Is Admin: ${userData.is_admin}`);
    }

    // Test admin access to a protected resource
    const { data: adminTest, error: adminTestError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (adminTestError) {
      console.log('❌ Admin access test failed:', adminTestError.message);
    } else {
      console.log('✅ Admin can access protected resources');
    }

    console.log('\n🎉 ADMIN LOGIN SUCCESS!');
    console.log('═'.repeat(40));
    console.log('✅ Authentication working');
    console.log('✅ Admin privileges confirmed');
    console.log('✅ Database access working');
    console.log('');
    console.log('🔗 You can now access:');
    console.log('   Admin Panel: http://localhost:5173/admin');
    console.log('   Dashboard: http://localhost:5173/admin/dashboard');
    console.log('   MLM Settings: http://localhost:5173/admin/dashboard/mlm-settings');
    console.log('   Referral Tree: http://localhost:5173/admin/dashboard/referral-tree');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Also create a simple test user for regular login
async function createTestUser() {
  try {
    console.log('\n2️⃣ CREATING TEST USER');
    console.log('─'.repeat(40));

    const testEmail = '<EMAIL>';
    const testPassword = 'test123456';
    const testName = 'Test User';

    console.log(`📝 Creating test user: ${testEmail}`);

    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: testName
        }
      }
    });

    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('ℹ️ Test user already exists');
      } else {
        console.log('❌ Test user creation failed:', signUpError.message);
      }
    } else {
      console.log('✅ Test user created successfully');
      console.log(`   User ID: ${signUpData.user?.id}`);
    }

    // Update user profile
    const { error: updateError } = await supabase
      .from('users')
      .upsert({
        email: testEmail,
        full_name: testName,
        is_premium: false,
        is_admin: false
      }, {
        onConflict: 'email'
      });

    if (updateError) {
      console.log('❌ Error updating test user profile:', updateError.message);
    } else {
      console.log('✅ Test user profile updated');
    }

    console.log('');
    console.log('📋 TEST USER CREDENTIALS:');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Password: ${testPassword}`);
    console.log('   Use this for testing regular user features');

    return true;

  } catch (error) {
    console.error('❌ Test user creation failed:', error.message);
    return false;
  }
}

async function runTests() {
  const adminSuccess = await testAdminLogin();
  const userSuccess = await createTestUser();

  console.log('\n🎯 SUMMARY');
  console.log('═'.repeat(30));
  console.log(`Admin Login: ${adminSuccess ? '✅ Working' : '❌ Failed'}`);
  console.log(`Test User: ${userSuccess ? '✅ Created' : '❌ Failed'}`);
  
  if (!adminSuccess) {
    console.log('');
    console.log('⚠️ NEXT STEPS:');
    console.log('1. Follow the email confirmation steps above');
    console.log('2. Or disable email confirmation in Supabase');
    console.log('3. Then try logging in again');
  }

  return adminSuccess;
}

runTests().then(success => {
  process.exit(success ? 0 : 1);
});
