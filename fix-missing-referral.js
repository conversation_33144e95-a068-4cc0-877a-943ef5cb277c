// Fix Missing Referral Relationship
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔗 Fixing Missing Referral Relationship');
console.log('======================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function fixMissingReferral() {
  try {
    const referrerEmail = '<EMAIL>';
    
    console.log('1️⃣ FINDING THE REFERRER');
    console.log('─'.repeat(40));
    
    // Get the referrer
    const { data: referrer, error: referrerError } = await supabase
      .from('users')
      .select('*')
      .eq('email', referrerEmail)
      .single();

    if (referrerError) {
      console.log('❌ Referrer not found:', referrerError.message);
      return false;
    }

    console.log('✅ Referrer found:');
    console.log(`   Name: ${referrer.full_name}`);
    console.log(`   Email: ${referrer.email}`);
    console.log(`   Referral Code: ${referrer.referral_code}`);

    console.log('\n2️⃣ FINDING RECENT PREMIUM USERS');
    console.log('─'.repeat(40));

    // Find recent premium users who might have used this referral code
    const { data: recentUsers, error: recentError } = await supabase
      .from('users')
      .select('*')
      .eq('is_premium', true)
      .eq('referred_by', referrer.referral_code)
      .order('created_at', { ascending: false });

    if (recentError) {
      console.log('❌ Error finding recent users:', recentError.message);
      return false;
    }

    console.log(`✅ Found ${recentUsers?.length || 0} users who used this referral code:`);
    
    if (recentUsers && recentUsers.length > 0) {
      recentUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
        console.log(`      Premium: ${user.is_premium ? '✅ Yes' : '❌ No'}`);
        console.log(`      Created: ${user.created_at}`);
        console.log(`      Premium Date: ${user.premium_purchased_at || 'Not set'}`);
      });
    }

    console.log('\n3️⃣ CHECKING EXISTING REFERRALS TABLE');
    console.log('─'.repeat(40));

    // Check if referrals exist in the referrals table
    const { data: existingReferrals, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referred_user:users!referrals_referred_user_id_fkey(email, full_name, is_premium)
      `)
      .eq('referrer_id', referrer.id);

    if (refError) {
      console.log('❌ Error checking referrals:', refError.message);
    } else {
      console.log(`✅ Found ${existingReferrals?.length || 0} referrals in referrals table`);
      existingReferrals?.forEach((ref, index) => {
        console.log(`   ${index + 1}. ${ref.referred_user.full_name} (${ref.referred_user.email})`);
      });
    }

    console.log('\n4️⃣ CREATING MISSING REFERRAL RELATIONSHIPS');
    console.log('─'.repeat(40));

    if (recentUsers && recentUsers.length > 0) {
      for (let i = 0; i < recentUsers.length; i++) {
        const user = recentUsers[i];
        
        // Check if referral already exists
        const { data: existingRef, error: checkError } = await supabase
          .from('referrals')
          .select('id')
          .eq('referrer_id', referrer.id)
          .eq('referred_user_id', user.id)
          .single();

        if (existingRef) {
          console.log(`   ✅ Referral already exists for ${user.full_name}`);
          continue;
        }

        // Get current referral count for this referrer
        const { data: currentReferrals, error: countError } = await supabase
          .from('referrals')
          .select('referral_order')
          .eq('referrer_id', referrer.id)
          .order('referral_order', { ascending: false })
          .limit(1);

        const nextOrder = currentReferrals && currentReferrals.length > 0 
          ? currentReferrals[0].referral_order + 1 
          : 1;

        console.log(`📝 Creating referral for ${user.full_name} (position ${nextOrder})`);

        // Create the referral relationship
        const { error: createError } = await supabase
          .from('referrals')
          .insert({
            referrer_id: referrer.id,
            referred_user_id: user.id,
            referral_order: nextOrder,
            bonus_amount: 0, // Will be calculated later
            status: 'completed',
            created_at: user.created_at || new Date().toISOString()
          });

        if (createError) {
          console.log(`❌ Error creating referral for ${user.full_name}:`, createError.message);
        } else {
          console.log(`✅ Referral created for ${user.full_name} at position ${nextOrder}`);
        }
      }
    }

    console.log('\n5️⃣ CALCULATING AND ADDING BONUSES');
    console.log('─'.repeat(40));

    // Get MLM bonus amount
    const { data: mlmSettings, error: mlmError } = await supabase
      .from('admin_settings')
      .select('setting_value')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    const bonusAmount = parseFloat(mlmSettings?.setting_value || '250');
    console.log(`💰 MLM bonus amount: ₹${bonusAmount}`);

    // Get all referrals for this referrer
    const { data: allReferrals, error: allRefError } = await supabase
      .from('referrals')
      .select(`
        *,
        referred_user:users!referrals_referred_user_id_fkey(id, email, full_name, is_premium)
      `)
      .eq('referrer_id', referrer.id)
      .order('referral_order');

    if (allRefError) {
      console.log('❌ Error getting all referrals:', allRefError.message);
      return false;
    }

    let totalBonusToAdd = 0;
    const bonusTransactions = [];

    if (allReferrals && allReferrals.length > 0) {
      console.log('📊 Calculating bonuses for each referral:');
      
      allReferrals.forEach(ref => {
        const user = ref.referred_user;
        const position = ref.referral_order;
        
        if (user.is_premium) {
          let shouldGetBonus = false;
          let reason = '';
          
          if (position <= 2) {
            shouldGetBonus = true;
            reason = `Direct referral #${position}`;
          } else if (position === 3) {
            shouldGetBonus = false;
            reason = `3rd referral goes to grandparent`;
          } else {
            shouldGetBonus = true;
            reason = `${position}th referral back to direct referrer`;
          }
          
          console.log(`   Position ${position}: ${user.full_name}`);
          console.log(`      Premium: ✅ Yes`);
          console.log(`      Should get bonus: ${shouldGetBonus ? '✅ Yes' : '❌ No'}`);
          console.log(`      Reason: ${reason}`);
          
          if (shouldGetBonus) {
            totalBonusToAdd += bonusAmount;
            bonusTransactions.push({
              user_id: referrer.id,
              type: 'credit',
              amount: bonusAmount,
              description: `MLM referral bonus - ${user.full_name} (position ${position})`,
              reference_type: 'referral',
              reference_id: ref.id,
              created_at: new Date().toISOString()
            });
          }
        } else {
          console.log(`   Position ${position}: ${user.full_name}`);
          console.log(`      Premium: ❌ No - No bonus`);
        }
      });
    }

    console.log(`\n💰 Total bonus to add: ₹${totalBonusToAdd}`);

    if (totalBonusToAdd > 0) {
      // Add wallet transactions
      const { error: transError } = await supabase
        .from('wallet_transactions')
        .insert(bonusTransactions);

      if (transError) {
        console.log('❌ Error adding bonus transactions:', transError.message);
      } else {
        console.log('✅ Bonus transactions added');
        
        // Update wallet balance
        const newBalance = parseFloat(referrer.wallet_balance || '0') + totalBonusToAdd;
        const { error: balanceError } = await supabase
          .from('users')
          .update({ 
            wallet_balance: newBalance,
            premium_referral_count: allReferrals.filter(r => r.referred_user.is_premium).length
          })
          .eq('id', referrer.id);

        if (balanceError) {
          console.log('❌ Error updating wallet balance:', balanceError.message);
        } else {
          console.log(`✅ Wallet balance updated to: ₹${newBalance}`);
        }
      }
    }

    console.log('\n🎉 REFERRAL FIX COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ Referrer: ${referrerEmail}`);
    console.log(`✅ Referrals created: ${recentUsers?.length || 0}`);
    console.log(`✅ Total bonus added: ₹${totalBonusToAdd}`);
    console.log(`✅ New wallet balance: ₹${parseFloat(referrer.wallet_balance || '0') + totalBonusToAdd}`);
    console.log('');
    console.log('🔗 Check the results in:');
    console.log('   Admin Panel: http://localhost:5173/admin/dashboard/users');
    console.log('   Referral Tree: http://localhost:5173/admin/dashboard/referral-tree');

    return true;

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    return false;
  }
}

fixMissingReferral().then(success => {
  if (success) {
    console.log('\n🚀 Missing referral fix completed successfully!');
  } else {
    console.log('\n⚠️ Missing referral fix failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
