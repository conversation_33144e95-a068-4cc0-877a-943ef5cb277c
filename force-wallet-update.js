// Force Wallet Update and Debug Dashboard Issue
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔄 Force Wallet Update and Debug Dashboard');
console.log('==========================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function forceWalletUpdate() {
  try {
    console.log('1️⃣ CHECKING CURRENT DATABASE STATE');
    console.log('─'.repeat(40));
    
    const userEmail = '<EMAIL>';
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', userEmail)
      .single();

    if (userError) {
      console.log('❌ User not found:', userError.message);
      return false;
    }

    console.log('✅ Current database state:');
    console.log(`   Name: ${user.full_name}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Wallet Balance: ₹${user.wallet_balance || 0}`);
    console.log(`   Premium Referral Count: ${user.premium_referral_count || 0}`);
    console.log(`   Is Premium: ${user.is_premium}`);
    console.log(`   Created: ${user.created_at}`);

    console.log('\n2️⃣ FORCE UPDATE WITH TIMESTAMP');
    console.log('─'.repeat(40));

    // Force update with current timestamp to trigger any cache refresh
    const now = new Date().toISOString();
    
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        wallet_balance: 250.00,
        premium_referral_count: 1,
        updated_at: now
      })
      .eq('id', user.id);

    if (updateError) {
      console.log('❌ Error forcing update:', updateError.message);
      return false;
    }

    console.log('✅ Forced update with timestamp');
    console.log(`   Updated at: ${now}`);

    console.log('\n3️⃣ VERIFICATION WITH FRESH QUERY');
    console.log('─'.repeat(40));

    // Wait a moment and query again
    await new Promise(resolve => setTimeout(resolve, 1000));

    const { data: freshUser, error: freshError } = await supabase
      .from('users')
      .select('wallet_balance, premium_referral_count, updated_at')
      .eq('id', user.id)
      .single();

    if (freshError) {
      console.log('❌ Error with fresh query:', freshError.message);
    } else {
      console.log('✅ Fresh query results:');
      console.log(`   Wallet Balance: ₹${freshUser.wallet_balance}`);
      console.log(`   Referral Count: ${freshUser.premium_referral_count}`);
      console.log(`   Last Updated: ${freshUser.updated_at}`);
    }

    console.log('\n4️⃣ CHECKING AUTH SESSION');
    console.log('─'.repeat(40));

    // Check if there's an active session that might be caching old data
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    
    if (session?.session) {
      console.log('⚠️ Found active session:');
      console.log(`   User: ${session.session.user?.email}`);
      console.log(`   Session ID: ${session.session.user?.id}`);
      
      if (session.session.user?.email === userEmail) {
        console.log('✅ Session matches the user we updated');
      } else {
        console.log('⚠️ Session is for a different user');
      }
    } else {
      console.log('ℹ️ No active session found');
    }

    console.log('\n5️⃣ TESTING WALLET QUERY DIRECTLY');
    console.log('─'.repeat(40));

    // Test the exact query the dashboard might be using
    const { data: walletTest, error: walletError } = await supabase
      .from('users')
      .select('wallet_balance')
      .eq('email', userEmail)
      .single();

    if (walletError) {
      console.log('❌ Wallet query error:', walletError.message);
    } else {
      console.log(`✅ Direct wallet query: ₹${walletTest.wallet_balance}`);
    }

    console.log('\n6️⃣ DASHBOARD TROUBLESHOOTING STEPS');
    console.log('─'.repeat(40));

    console.log('🔧 Try these steps to fix the dashboard:');
    console.log('');
    console.log('Step 1: Hard Refresh Browser');
    console.log('   • Press Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)');
    console.log('   • Or press F12 → Application → Clear Storage → Clear site data');
    console.log('');
    console.log('Step 2: Clear Browser Cache');
    console.log('   • Press F12 to open developer tools');
    console.log('   • Right-click refresh button → Empty Cache and Hard Reload');
    console.log('');
    console.log('Step 3: Use Incognito/Private Window');
    console.log('   • Open new incognito window');
    console.log('   • Go to http://localhost:5173/dashboard');
    console.log('   • Login again');
    console.log('');
    console.log('Step 4: Check Browser Console');
    console.log('   • Press F12 → Console tab');
    console.log('   • Look for any error messages');
    console.log('   • Refresh page and check for errors');
    console.log('');
    console.log('Step 5: Logout and Login Again');
    console.log('   • Logout from dashboard');
    console.log('   • Clear browser data');
    console.log('   • Login again');

    console.log('\n7️⃣ ALTERNATIVE: ADMIN PANEL CHECK');
    console.log('─'.repeat(40));

    console.log('You can verify the wallet balance in admin panel:');
    console.log('   1. Go to: http://localhost:5173/admin/dashboard/users');
    console.log(`   2. Search for: ${userEmail}`);
    console.log('   3. Check if wallet shows ₹250');
    console.log('');
    console.log('If admin panel shows ₹250 but user dashboard shows ₹0,');
    console.log('then it\'s a frontend caching issue.');

    console.log('\n🎉 FORCE UPDATE COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ Database wallet balance: ₹250`);
    console.log(`✅ Database referral count: 1`);
    console.log(`✅ Timestamp updated: ${now}`);
    console.log('');
    console.log('🔄 Next steps:');
    console.log('   1. Try hard refresh (Ctrl+Shift+R)');
    console.log('   2. Clear browser cache');
    console.log('   3. Use incognito window');
    console.log('   4. Check admin panel to verify data');

    return true;

  } catch (error) {
    console.error('❌ Force update failed:', error.message);
    return false;
  }
}

forceWalletUpdate().then(success => {
  if (success) {
    console.log('\n🚀 Force update completed!');
    console.log('💡 Try refreshing your dashboard now!');
  } else {
    console.log('\n⚠️ Force update failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
