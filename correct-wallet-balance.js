// Correct Wallet Balance to ₹250 for 1 Referral
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 Correcting Wallet Balance to ₹250');
console.log('===================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function correctWalletBalance() {
  try {
    console.log('1️⃣ FINDING USER ACCOUNT');
    console.log('─'.repeat(40));
    
    const userEmail = '<EMAIL>';
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', userEmail)
      .single();

    if (userError) {
      console.log('❌ User not found:', userError.message);
      return false;
    }

    console.log('✅ User found:');
    console.log(`   Name: ${user.full_name}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Current Wallet: ₹${user.wallet_balance || 0}`);
    console.log(`   Current Referral Count: ${user.premium_referral_count || 0}`);

    console.log('\n2️⃣ CORRECTING TO PROPER AMOUNT');
    console.log('─'.repeat(40));

    // You said you have 1 referral, so should get ₹250
    const correctBalance = 250.00;
    const correctReferralCount = 1;

    console.log(`📝 Setting correct values:`);
    console.log(`   Wallet Balance: ₹${correctBalance} (for 1 referral)`);
    console.log(`   Referral Count: ${correctReferralCount}`);

    // Update to correct values
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        wallet_balance: correctBalance,
        premium_referral_count: correctReferralCount
      })
      .eq('id', user.id);

    if (updateError) {
      console.log('❌ Error updating wallet:', updateError.message);
      return false;
    }

    console.log('✅ Wallet balance corrected successfully!');

    console.log('\n3️⃣ VERIFICATION');
    console.log('─'.repeat(40));

    // Verify the correction
    const { data: updatedUser, error: verifyError } = await supabase
      .from('users')
      .select('wallet_balance, premium_referral_count')
      .eq('id', user.id)
      .single();

    if (verifyError) {
      console.log('❌ Error verifying update:', verifyError.message);
    } else {
      console.log('✅ Correction verified:');
      console.log(`   New wallet balance: ₹${updatedUser.wallet_balance}`);
      console.log(`   New referral count: ${updatedUser.premium_referral_count}`);
    }

    console.log('\n4️⃣ MLM LOGIC EXPLANATION');
    console.log('─'.repeat(40));

    console.log('For 1 premium referral:');
    console.log('   1st referral → ₹250 to you ✅');
    console.log('');
    console.log('This is correct according to MLM logic:');
    console.log('   • 1st & 2nd referrals go to direct referrer (you)');
    console.log('   • 3rd referral goes to grandparent');
    console.log('   • 4th+ referrals go back to direct referrer (you)');

    console.log('\n5️⃣ CHECKING DASHBOARD DISPLAY');
    console.log('─'.repeat(40));

    console.log('The dashboard should now show:');
    console.log(`   💰 Wallet Balance: ₹${correctBalance}`);
    console.log(`   👥 Referrals: ${correctReferralCount}`);
    console.log(`   📊 Bonus Earned: ₹${correctBalance}`);

    console.log('\n🎉 WALLET CORRECTION COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ User: ${userEmail}`);
    console.log(`✅ Corrected wallet balance: ₹${correctBalance}`);
    console.log(`✅ Corrected referral count: ${correctReferralCount}`);
    console.log('');
    console.log('🔗 User can verify at:');
    console.log('   http://localhost:5173/dashboard');
    console.log('   (Should now show ₹250 wallet balance)');
    console.log('');
    console.log('🔧 Admin can verify at:');
    console.log('   http://localhost:5173/admin/dashboard/users');
    console.log('   (Search for user to confirm ₹250 balance)');
    console.log('');
    console.log('💡 If you get more premium referrals in the future:');
    console.log('   • 2nd referral → +₹250 (total ₹500)');
    console.log('   • 3rd referral → +₹0 (goes to your referrer)');
    console.log('   • 4th referral → +₹250 (total ₹750)');

    return true;

  } catch (error) {
    console.error('❌ Correction failed:', error.message);
    return false;
  }
}

correctWalletBalance().then(success => {
  if (success) {
    console.log('\n🚀 Wallet balance corrected to ₹250!');
    console.log('💰 Now showing the correct amount for 1 referral!');
  } else {
    console.log('\n⚠️ Wallet correction failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
