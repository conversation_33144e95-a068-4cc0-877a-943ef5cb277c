// Force Wallet Update to ₹250 - Direct Database Update
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('💰 Force Wallet Update to ₹250');
console.log('==============================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function forceWalletUpdate250() {
  try {
    console.log('1️⃣ FINDING REFERRER ACCOUNT');
    console.log('─'.repeat(40));
    
    const { data: referrer, error: referrerError } = await supabase
      .from('users')
      .select('*')
      .eq('referral_code', 'REFEASFCDB98')
      .single();

    if (referrerError) {
      console.log('❌ Referrer not found:', referrerError.message);
      return false;
    }

    console.log('✅ Referrer found:');
    console.log(`   Name: ${referrer.full_name}`);
    console.log(`   Email: ${referrer.email}`);
    console.log(`   Current Wallet: ₹${referrer.wallet_balance || 0}`);

    console.log('\n2️⃣ FORCE UPDATING WALLET TO ₹250');
    console.log('─'.repeat(40));

    const targetBalance = 250.00;
    const targetReferralCount = 1;

    console.log(`📝 Setting wallet balance to: ₹${targetBalance}`);
    console.log(`📝 Setting referral count to: ${targetReferralCount}`);

    // Multiple update attempts to ensure it works
    for (let attempt = 1; attempt <= 3; attempt++) {
      console.log(`   Attempt ${attempt}...`);
      
      const { error: updateError } = await supabase
        .from('users')
        .update({ 
          wallet_balance: targetBalance,
          premium_referral_count: targetReferralCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', referrer.id);

      if (updateError) {
        console.log(`   ❌ Attempt ${attempt} failed:`, updateError.message);
      } else {
        console.log(`   ✅ Attempt ${attempt} succeeded`);
        break;
      }

      // Wait a bit between attempts
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n3️⃣ VERIFICATION WITH MULTIPLE QUERIES');
    console.log('─'.repeat(40));

    // Multiple verification queries
    for (let i = 1; i <= 3; i++) {
      console.log(`   Verification ${i}:`);
      
      const { data: checkUser, error: checkError } = await supabase
        .from('users')
        .select('wallet_balance, premium_referral_count, updated_at')
        .eq('id', referrer.id)
        .single();

      if (checkError) {
        console.log(`   ❌ Query ${i} failed:`, checkError.message);
      } else {
        console.log(`   ✅ Wallet: ₹${checkUser.wallet_balance}`);
        console.log(`   ✅ Referrals: ${checkUser.premium_referral_count}`);
        console.log(`   ✅ Updated: ${checkUser.updated_at}`);
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n4️⃣ ALTERNATIVE UPDATE METHOD');
    console.log('─'.repeat(40));

    // Try using upsert instead of update
    console.log('📝 Trying upsert method...');
    
    const { error: upsertError } = await supabase
      .from('users')
      .upsert({
        id: referrer.id,
        email: referrer.email,
        full_name: referrer.full_name,
        wallet_balance: targetBalance,
        premium_referral_count: targetReferralCount,
        is_premium: referrer.is_premium,
        is_admin: referrer.is_admin,
        referral_code: referrer.referral_code,
        referred_by: referrer.referred_by,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      });

    if (upsertError) {
      console.log('❌ Upsert failed:', upsertError.message);
    } else {
      console.log('✅ Upsert succeeded');
    }

    console.log('\n5️⃣ FINAL VERIFICATION');
    console.log('─'.repeat(40));

    const { data: finalUser, error: finalError } = await supabase
      .from('users')
      .select('wallet_balance, premium_referral_count')
      .eq('id', referrer.id)
      .single();

    if (finalError) {
      console.log('❌ Final verification failed:', finalError.message);
    } else {
      console.log('✅ Final state:');
      console.log(`   Wallet Balance: ₹${finalUser.wallet_balance}`);
      console.log(`   Referral Count: ${finalUser.premium_referral_count}`);
      
      if (parseFloat(finalUser.wallet_balance) === targetBalance) {
        console.log('🎉 SUCCESS: Wallet balance is correct!');
      } else {
        console.log('⚠️ WARNING: Wallet balance still not updated');
      }
    }

    console.log('\n6️⃣ DASHBOARD REFRESH INSTRUCTIONS');
    console.log('─'.repeat(40));

    console.log('🔄 To see the updated balance on dashboard:');
    console.log('');
    console.log('Step 1: Clear browser cache');
    console.log('   • Press Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)');
    console.log('   • Clear "Cookies and site data" + "Cached images and files"');
    console.log('');
    console.log('Step 2: Use incognito window');
    console.log('   • Open new incognito/private window');
    console.log('   • Go to: http://localhost:5173/dashboard');
    console.log('   • Login with your credentials');
    console.log('');
    console.log('Step 3: Hard refresh');
    console.log('   • Press Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)');
    console.log('   • Or F12 → Right-click refresh → Empty Cache and Hard Reload');

    console.log('\n🎉 FORCE UPDATE COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ Target wallet balance: ₹${targetBalance}`);
    console.log(`✅ Target referral count: ${targetReferralCount}`);
    console.log(`✅ Multiple update methods attempted`);
    console.log('');
    console.log('🔗 Check results at:');
    console.log('   User Dashboard: http://localhost:5173/dashboard');
    console.log('   Admin Panel: http://localhost:5173/admin/dashboard/users');
    console.log('');
    console.log('💡 If still showing ₹0, it\'s a browser caching issue.');
    console.log('   Try the incognito window method above.');

    return true;

  } catch (error) {
    console.error('❌ Force update failed:', error.message);
    return false;
  }
}

forceWalletUpdate250().then(success => {
  if (success) {
    console.log('\n🚀 Force wallet update completed!');
    console.log('💰 Database should now have ₹250 - try incognito window!');
  } else {
    console.log('\n⚠️ Force wallet update failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
