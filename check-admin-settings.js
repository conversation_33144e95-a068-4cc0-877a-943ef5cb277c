// Check admin_settings table and create if needed
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Checking admin_settings table...');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAdminSettings() {
  try {
    // Try to access admin_settings table
    console.log('📋 Checking admin_settings table access...');
    
    const { data, error } = await supabase
      .from('admin_settings')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('❌ admin_settings table error:', error.message);
      
      // Check if it's a column issue or table doesn't exist
      if (error.message.includes('does not exist')) {
        console.log('📝 admin_settings table or column missing');
        
        // Try without category column
        const { data: simpleData, error: simpleError } = await supabase
          .from('admin_settings')
          .select('setting_key, setting_value')
          .limit(1);
        
        if (simpleError) {
          console.log('❌ Table completely missing:', simpleError.message);
          console.log('💡 You may need to run database migrations');
        } else {
          console.log('✅ Table exists but missing category column');
          console.log('📊 Sample data:', simpleData);
        }
      }
    } else {
      console.log('✅ admin_settings table accessible');
      console.log('📊 Sample data:', data);
    }
    
    // Test MLM settings insertion (if table exists)
    console.log('\n🎯 Testing MLM settings...');
    
    const mlmSettings = [
      {
        setting_key: 'mlm_system_enabled',
        setting_value: 'true',
        description: 'Enable MLM referral system'
      },
      {
        setting_key: 'mlm_bonus_amount',
        setting_value: '250.00',
        description: 'Fixed bonus amount for each MLM referral'
      }
    ];
    
    for (const setting of mlmSettings) {
      try {
        // Try to upsert without category first
        const { error: upsertError } = await supabase
          .from('admin_settings')
          .upsert({
            setting_key: setting.setting_key,
            setting_value: setting.setting_value,
            description: setting.description
          });
        
        if (upsertError) {
          console.log(`❌ Failed to upsert ${setting.setting_key}:`, upsertError.message);
        } else {
          console.log(`✅ Successfully upserted ${setting.setting_key}`);
        }
      } catch (err) {
        console.log(`❌ Error with ${setting.setting_key}:`, err.message);
      }
    }
    
    // Check final state
    console.log('\n📊 Final admin_settings check...');
    const { data: finalData, error: finalError } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value, description')
      .in('setting_key', ['mlm_system_enabled', 'mlm_bonus_amount']);
    
    if (finalError) {
      console.log('❌ Final check failed:', finalError.message);
    } else {
      console.log('✅ MLM settings in database:');
      finalData?.forEach(setting => {
        console.log(`   ${setting.setting_key}: ${setting.setting_value}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

checkAdminSettings();
