// Test Referral Tree Service
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🌳 Testing Referral Tree Service');
console.log('================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testReferralTree() {
  try {
    console.log('1️⃣ CHECKING DATABASE STRUCTURE');
    console.log('─'.repeat(40));

    // Check if users table has required columns
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, full_name, referral_code, is_premium, wallet_balance, referred_by')
      .limit(5);

    if (usersError) {
      console.log('❌ Users table error:', usersError.message);
      return false;
    }

    console.log(`✅ Users table accessible: ${users?.length || 0} users found`);
    if (users && users.length > 0) {
      console.log('   Sample user:', {
        id: users[0].id,
        email: users[0].email,
        has_referral_code: !!users[0].referral_code,
        is_premium: users[0].is_premium,
        has_referrer: !!users[0].referred_by
      });
    }

    // Check referrals table
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select('id, referrer_id, referred_user_id, referral_order')
      .limit(5);

    if (referralsError) {
      console.log('❌ Referrals table error:', referralsError.message);
      return false;
    }

    console.log(`✅ Referrals table accessible: ${referrals?.length || 0} referrals found`);

    // Check wallet transactions
    const { data: transactions, error: transError } = await supabase
      .from('wallet_transactions')
      .select('id, user_id, amount, reference_type')
      .eq('reference_type', 'referral')
      .limit(5);

    if (transError) {
      console.log('❌ Wallet transactions error:', transError.message);
      return false;
    }

    console.log(`✅ Wallet transactions accessible: ${transactions?.length || 0} referral transactions found`);

    console.log('\n2️⃣ TESTING NETWORK OVERVIEW');
    console.log('─'.repeat(40));

    // Test getting root users (users with no referrer)
    const { data: rootUsers, error: rootError } = await supabase
      .from('users')
      .select('id, email, full_name, referred_by')
      .or('referred_by.is.null,referred_by.eq.')
      .limit(10);

    if (rootError) {
      console.log('❌ Root users query error:', rootError.message);
      return false;
    }

    console.log(`✅ Root users found: ${rootUsers?.length || 0}`);
    rootUsers?.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
    });

    console.log('\n3️⃣ TESTING USER SEARCH');
    console.log('─'.repeat(40));

    // Test search functionality
    const { data: searchResults, error: searchError } = await supabase
      .from('users')
      .select('id, email, full_name, referral_code')
      .or('email.ilike.%test%,full_name.ilike.%test%')
      .limit(5);

    if (searchError) {
      console.log('❌ Search query error:', searchError.message);
      return false;
    }

    console.log(`✅ Search results: ${searchResults?.length || 0} users found`);
    searchResults?.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.full_name} (${user.email}) - ${user.referral_code}`);
    });

    console.log('\n4️⃣ TESTING REFERRAL TREE BUILDING');
    console.log('─'.repeat(40));

    if (users && users.length > 0) {
      const testUserId = users[0].id;
      console.log(`📝 Building tree for user: ${users[0].email}`);

      // Get user's direct referrals
      const { data: directReferrals, error: refError } = await supabase
        .from('referrals')
        .select(`
          id,
          referral_order,
          referred_user:users!referrals_referred_user_id_fkey(
            id,
            email,
            full_name,
            is_premium
          )
        `)
        .eq('referrer_id', testUserId);

      if (refError) {
        console.log('❌ Direct referrals query error:', refError.message);
      } else {
        console.log(`✅ Direct referrals found: ${directReferrals?.length || 0}`);
        directReferrals?.forEach((ref, index) => {
          if (ref.referred_user) {
            console.log(`   ${index + 1}. ${ref.referred_user.full_name} (Order: ${ref.referral_order})`);
          }
        });
      }

      // Get user's earnings
      const { data: earnings, error: earningsError } = await supabase
        .from('wallet_transactions')
        .select('amount')
        .eq('user_id', testUserId)
        .eq('reference_type', 'referral');

      if (earningsError) {
        console.log('❌ Earnings query error:', earningsError.message);
      } else {
        const totalEarnings = earnings?.reduce((sum, t) => sum + parseFloat(t.amount), 0) || 0;
        console.log(`✅ Total earnings: ₹${totalEarnings.toFixed(2)}`);
      }
    }

    console.log('\n5️⃣ TESTING ADMIN PANEL COMPATIBILITY');
    console.log('─'.repeat(40));

    // Check if the data structure matches what the admin panel expects
    const { data: adminTestData, error: adminError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        referral_code,
        is_premium,
        wallet_balance,
        premium_referral_count,
        ewallet_unlocked,
        created_at,
        premium_purchased_at
      `)
      .limit(1);

    if (adminError) {
      console.log('❌ Admin data structure error:', adminError.message);
      console.log('   This might be why the referral tree is not working');
      return false;
    }

    console.log('✅ Admin data structure compatible');
    if (adminTestData && adminTestData.length > 0) {
      const user = adminTestData[0];
      console.log('   Sample data structure:');
      console.log(`   - ID: ${user.id}`);
      console.log(`   - Email: ${user.email}`);
      console.log(`   - Premium: ${user.is_premium}`);
      console.log(`   - Wallet: ₹${user.wallet_balance || 0}`);
      console.log(`   - Premium Referrals: ${user.premium_referral_count || 0}`);
      console.log(`   - E-wallet Unlocked: ${user.ewallet_unlocked || false}`);
    }

    console.log('\n🎉 REFERRAL TREE TEST SUMMARY');
    console.log('═'.repeat(50));
    console.log('✅ Database structure is compatible');
    console.log('✅ Users table accessible');
    console.log('✅ Referrals table accessible');
    console.log('✅ Wallet transactions accessible');
    console.log('✅ Search functionality working');
    console.log('✅ Data structure matches admin panel requirements');
    console.log('');
    console.log('🔗 Admin Referral Tree URL: http://localhost:5173/admin/dashboard/referral-tree');
    console.log('');
    console.log('📝 If the referral tree is still not working, check:');
    console.log('   1. Browser console for JavaScript errors');
    console.log('   2. Network tab for failed API requests');
    console.log('   3. Make sure you have users with referral relationships');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

testReferralTree().then(success => {
  if (success) {
    console.log('\n🚀 Referral tree service should be working!');
  } else {
    console.log('\n⚠️ Referral tree test failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
