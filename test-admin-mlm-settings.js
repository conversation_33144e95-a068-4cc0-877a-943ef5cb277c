// Test Admin MLM Settings Functionality
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🎯 Testing Admin MLM Settings Functionality');
console.log('==========================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAdminMLMSettings() {
  try {
    console.log('🔍 Testing MLM admin settings functionality...\n');

    // Test 1: Check current MLM bonus amount
    console.log('1️⃣ CHECKING CURRENT MLM BONUS AMOUNT');
    console.log('─'.repeat(40));
    
    const { data: currentSetting, error: currentError } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value, description')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    if (currentError) {
      console.log('❌ Failed to get current setting:', currentError.message);
      return false;
    }

    const originalAmount = parseFloat(currentSetting.setting_value);
    console.log(`✅ Current MLM bonus amount: ₹${originalAmount}`);
    console.log(`   Description: ${currentSetting.description}`);

    // Test 2: Update MLM bonus amount (simulate admin change)
    console.log('\n2️⃣ TESTING ADMIN BONUS AMOUNT UPDATE');
    console.log('─'.repeat(40));
    
    const testAmount = 300.00; // Change from 250 to 300
    console.log(`📝 Updating MLM bonus amount to ₹${testAmount}...`);

    const { error: updateError } = await supabase
      .from('admin_settings')
      .update({
        setting_value: testAmount.toString(),
        updated_at: new Date().toISOString()
      })
      .eq('setting_key', 'mlm_bonus_amount');

    if (updateError) {
      console.log('❌ Failed to update setting:', updateError.message);
      return false;
    }

    console.log('✅ MLM bonus amount updated successfully');

    // Test 3: Verify the change
    console.log('\n3️⃣ VERIFYING THE CHANGE');
    console.log('─'.repeat(40));
    
    const { data: updatedSetting, error: verifyError } = await supabase
      .from('admin_settings')
      .select('setting_value, updated_at')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    if (verifyError) {
      console.log('❌ Failed to verify update:', verifyError.message);
      return false;
    }

    const newAmount = parseFloat(updatedSetting.setting_value);
    console.log(`✅ Verified new amount: ₹${newAmount}`);
    console.log(`   Updated at: ${updatedSetting.updated_at}`);

    if (newAmount === testAmount) {
      console.log('✅ Amount change verified successfully');
    } else {
      console.log('❌ Amount change verification failed');
      return false;
    }

    // Test 4: Test MLM service reading the new amount
    console.log('\n4️⃣ TESTING MLM SERVICE READS NEW AMOUNT');
    console.log('─'.repeat(40));
    
    // Simulate what the MLM service does
    const { data: serviceSetting, error: serviceError } = await supabase
      .from('admin_settings')
      .select('setting_value')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    if (serviceError) {
      console.log('❌ MLM service failed to read setting:', serviceError.message);
      return false;
    }

    const serviceAmount = parseFloat(serviceSetting.setting_value);
    console.log(`✅ MLM service reads amount: ₹${serviceAmount}`);

    if (serviceAmount === testAmount) {
      console.log('✅ MLM service correctly reads updated amount');
    } else {
      console.log('❌ MLM service reading incorrect amount');
      return false;
    }

    // Test 5: Test system status setting
    console.log('\n5️⃣ TESTING MLM SYSTEM STATUS');
    console.log('─'.repeat(40));
    
    const { data: statusSetting, error: statusError } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value')
      .eq('setting_key', 'mlm_system_enabled')
      .single();

    if (statusError) {
      console.log('❌ Failed to get system status:', statusError.message);
    } else {
      const isEnabled = statusSetting.setting_value === 'true';
      console.log(`✅ MLM System Status: ${isEnabled ? 'Enabled' : 'Disabled'}`);
    }

    // Test 6: Restore original amount
    console.log('\n6️⃣ RESTORING ORIGINAL AMOUNT');
    console.log('─'.repeat(40));
    
    console.log(`📝 Restoring original amount: ₹${originalAmount}...`);

    const { error: restoreError } = await supabase
      .from('admin_settings')
      .update({
        setting_value: originalAmount.toString(),
        updated_at: new Date().toISOString()
      })
      .eq('setting_key', 'mlm_bonus_amount');

    if (restoreError) {
      console.log('❌ Failed to restore original amount:', restoreError.message);
      return false;
    }

    console.log('✅ Original amount restored successfully');

    // Final verification
    const { data: finalSetting, error: finalError } = await supabase
      .from('admin_settings')
      .select('setting_value')
      .eq('setting_key', 'mlm_bonus_amount')
      .single();

    if (finalError) {
      console.log('❌ Failed to verify restoration:', finalError.message);
      return false;
    }

    const finalAmount = parseFloat(finalSetting.setting_value);
    console.log(`✅ Final verification: ₹${finalAmount}`);

    if (finalAmount === originalAmount) {
      console.log('✅ Original amount restored successfully');
    } else {
      console.log('❌ Failed to restore original amount');
      return false;
    }

    console.log('\n🎉 ALL ADMIN MLM SETTINGS TESTS PASSED!');
    console.log('═'.repeat(50));
    console.log('✅ Admin can change MLM bonus amount');
    console.log('✅ Changes are saved to database');
    console.log('✅ MLM service reads updated amounts');
    console.log('✅ System status can be checked');
    console.log('✅ Changes can be reverted');
    console.log('');
    console.log('🔗 Admin Panel URL: http://localhost:5173/admin/dashboard/mlm-settings');
    console.log('');
    console.log('📱 Admin can now:');
    console.log('   • Change MLM bonus amount from ₹250 to any value');
    console.log('   • Enable/disable MLM system');
    console.log('   • See real-time impact of changes');
    console.log('   • All new referrals will use the updated amount');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

testAdminMLMSettings().then(success => {
  if (success) {
    console.log('\n🚀 Admin MLM Settings functionality is working perfectly!');
  } else {
    console.log('\n⚠️ Admin MLM Settings test failed. Please check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
