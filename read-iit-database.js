// Read IIT Database - Complete Data Analysis
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('📊 Reading IIT Database - Complete Data Analysis');
console.log('===============================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function readIITDatabase() {
  try {
    console.log('🔗 Database Connection Info:');
    console.log(`   URL: ${supabaseUrl}`);
    console.log(`   Project: ${supabaseUrl.split('//')[1]?.split('.')[0] || 'Unknown'}`);
    console.log('');

    console.log('1️⃣ USERS TABLE ANALYSIS');
    console.log('─'.repeat(50));
    
    // Get all users
    const { data: allUsers, error: usersError } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (usersError) {
      console.log('❌ Error reading users:', usersError.message);
    } else {
      console.log(`✅ Found ${allUsers?.length || 0} total users:`);
      console.log('');
      
      if (allUsers && allUsers.length > 0) {
        allUsers.forEach((user, index) => {
          console.log(`${index + 1}. ${user.full_name || 'No Name'} (${user.email})`);
          console.log(`   ID: ${user.id}`);
          console.log(`   Wallet: ₹${user.wallet_balance || 0}`);
          console.log(`   Premium: ${user.is_premium ? '✅ Yes' : '❌ No'}`);
          console.log(`   Admin: ${user.is_admin ? '✅ Yes' : '❌ No'}`);
          console.log(`   Referral Code: ${user.referral_code || 'None'}`);
          console.log(`   Referred By: ${user.referred_by || 'None'}`);
          console.log(`   Premium Referrals: ${user.premium_referral_count || 0}`);
          console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`);
          console.log(`   Updated: ${user.updated_at ? new Date(user.updated_at).toLocaleString() : 'Never'}`);
          console.log('');
        });
      }
    }

    console.log('\n2️⃣ WALLET TRANSACTIONS ANALYSIS');
    console.log('─'.repeat(50));
    
    const { data: transactions, error: transError } = await supabase
      .from('wallet_transactions')
      .select(`
        *,
        user:users(email, full_name)
      `)
      .order('created_at', { ascending: false });

    if (transError) {
      console.log('❌ Error reading transactions:', transError.message);
    } else {
      console.log(`✅ Found ${transactions?.length || 0} wallet transactions:`);
      console.log('');
      
      if (transactions && transactions.length > 0) {
        transactions.forEach((trans, index) => {
          console.log(`${index + 1}. ${trans.type.toUpperCase()}: ₹${trans.amount}`);
          console.log(`   User: ${trans.user?.full_name} (${trans.user?.email})`);
          console.log(`   Description: ${trans.description}`);
          console.log(`   Reference: ${trans.reference_type} - ${trans.reference_id}`);
          console.log(`   Date: ${new Date(trans.created_at).toLocaleString()}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No wallet transactions found');
      }
    }

    console.log('\n3️⃣ REFERRALS TABLE ANALYSIS');
    console.log('─'.repeat(50));
    
    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referrer:users!referrals_referrer_id_fkey(email, full_name),
        referred_user:users!referrals_referred_user_id_fkey(email, full_name, is_premium)
      `)
      .order('created_at', { ascending: false });

    if (refError) {
      console.log('❌ Error reading referrals:', refError.message);
    } else {
      console.log(`✅ Found ${referrals?.length || 0} referral relationships:`);
      console.log('');
      
      if (referrals && referrals.length > 0) {
        referrals.forEach((ref, index) => {
          console.log(`${index + 1}. Referral #${ref.referral_order}`);
          console.log(`   Referrer: ${ref.referrer?.full_name} (${ref.referrer?.email})`);
          console.log(`   Referred: ${ref.referred_user?.full_name} (${ref.referred_user?.email})`);
          console.log(`   Premium: ${ref.referred_user?.is_premium ? '✅ Yes' : '❌ No'}`);
          console.log(`   Bonus: ₹${ref.bonus_amount || 0}`);
          console.log(`   Status: ${ref.status}`);
          console.log(`   Date: ${new Date(ref.created_at).toLocaleString()}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No referral relationships found');
      }
    }

    console.log('\n4️⃣ ADMIN SETTINGS ANALYSIS');
    console.log('─'.repeat(50));
    
    const { data: settings, error: settingsError } = await supabase
      .from('admin_settings')
      .select('*')
      .order('setting_key');

    if (settingsError) {
      console.log('❌ Error reading admin settings:', settingsError.message);
    } else {
      console.log(`✅ Found ${settings?.length || 0} admin settings:`);
      console.log('');
      
      if (settings && settings.length > 0) {
        settings.forEach((setting, index) => {
          console.log(`${index + 1}. ${setting.setting_key}`);
          console.log(`   Value: ${setting.setting_value}`);
          console.log(`   Description: ${setting.description || 'No description'}`);
          console.log(`   Updated: ${setting.updated_at ? new Date(setting.updated_at).toLocaleString() : 'Never'}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No admin settings found');
      }
    }

    console.log('\n5️⃣ PRODUCTS TABLE ANALYSIS');
    console.log('─'.repeat(50));
    
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .order('created_at', { ascending: false });

    if (productsError) {
      console.log('❌ Error reading products:', productsError.message);
    } else {
      console.log(`✅ Found ${products?.length || 0} products:`);
      console.log('');
      
      if (products && products.length > 0) {
        products.forEach((product, index) => {
          console.log(`${index + 1}. ${product.name}`);
          console.log(`   Price: ₹${product.price}`);
          console.log(`   Category: ${product.category}`);
          console.log(`   Stock: ${product.stock_quantity}`);
          console.log(`   Status: ${product.status}`);
          console.log(`   Created: ${new Date(product.created_at).toLocaleString()}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No products found');
      }
    }

    console.log('\n6️⃣ ORDERS TABLE ANALYSIS');
    console.log('─'.repeat(50));
    
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        *,
        user:users(email, full_name)
      `)
      .order('created_at', { ascending: false });

    if (ordersError) {
      console.log('❌ Error reading orders:', ordersError.message);
    } else {
      console.log(`✅ Found ${orders?.length || 0} orders:`);
      console.log('');
      
      if (orders && orders.length > 0) {
        orders.forEach((order, index) => {
          console.log(`${index + 1}. Order #${order.id}`);
          console.log(`   User: ${order.user?.full_name} (${order.user?.email})`);
          console.log(`   Total: ₹${order.total_amount}`);
          console.log(`   Status: ${order.status}`);
          console.log(`   Payment: ${order.payment_status}`);
          console.log(`   Date: ${new Date(order.created_at).toLocaleString()}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No orders found');
      }
    }

    console.log('\n7️⃣ SPECIFIC USER ANALYSIS: <EMAIL>');
    console.log('─'.repeat(50));
    
    const targetEmail = '<EMAIL>';
    const targetUser = allUsers?.find(u => u.email === targetEmail);
    
    if (targetUser) {
      console.log('✅ Target user detailed analysis:');
      console.log(`   Full Name: ${targetUser.full_name}`);
      console.log(`   Email: ${targetUser.email}`);
      console.log(`   ID: ${targetUser.id}`);
      console.log(`   Wallet Balance: ₹${targetUser.wallet_balance || 0}`);
      console.log(`   Premium Status: ${targetUser.is_premium ? '✅ Premium' : '❌ Not Premium'}`);
      console.log(`   Admin Status: ${targetUser.is_admin ? '✅ Admin' : '❌ Not Admin'}`);
      console.log(`   Referral Code: ${targetUser.referral_code}`);
      console.log(`   Referred By: ${targetUser.referred_by || 'None'}`);
      console.log(`   Premium Referrals: ${targetUser.premium_referral_count || 0}`);
      console.log(`   Account Created: ${new Date(targetUser.created_at).toLocaleString()}`);
      console.log(`   Last Updated: ${targetUser.updated_at ? new Date(targetUser.updated_at).toLocaleString() : 'Never'}`);
      
      // Check transactions for this user
      const userTransactions = transactions?.filter(t => t.user?.email === targetEmail) || [];
      console.log(`   Wallet Transactions: ${userTransactions.length}`);
      
      if (userTransactions.length > 0) {
        userTransactions.forEach((trans, index) => {
          console.log(`     ${index + 1}. ${trans.type.toUpperCase()}: ₹${trans.amount} - ${trans.description}`);
        });
      }
      
      // Check referrals by this user
      const userReferrals = referrals?.filter(r => r.referrer?.email === targetEmail) || [];
      console.log(`   Referrals Made: ${userReferrals.length}`);
      
      if (userReferrals.length > 0) {
        userReferrals.forEach((ref, index) => {
          console.log(`     ${index + 1}. ${ref.referred_user?.full_name} (${ref.referred_user?.email}) - ₹${ref.bonus_amount}`);
        });
      }
      
    } else {
      console.log('❌ Target user not found in database');
    }

    console.log('\n8️⃣ DATABASE SUMMARY');
    console.log('─'.repeat(50));
    
    const totalUsers = allUsers?.length || 0;
    const premiumUsers = allUsers?.filter(u => u.is_premium).length || 0;
    const totalWalletBalance = allUsers?.reduce((sum, u) => sum + (parseFloat(u.wallet_balance) || 0), 0) || 0;
    const totalTransactions = transactions?.length || 0;
    const totalReferrals = referrals?.length || 0;
    const totalProducts = products?.length || 0;
    const totalOrders = orders?.length || 0;
    
    console.log('📊 Database Statistics:');
    console.log(`   Total Users: ${totalUsers}`);
    console.log(`   Premium Users: ${premiumUsers}`);
    console.log(`   Total Wallet Balance: ₹${totalWalletBalance.toFixed(2)}`);
    console.log(`   Total Transactions: ${totalTransactions}`);
    console.log(`   Total Referrals: ${totalReferrals}`);
    console.log(`   Total Products: ${totalProducts}`);
    console.log(`   Total Orders: ${totalOrders}`);
    console.log('');
    console.log('🎯 Key Findings:');
    
    if (targetUser) {
      const targetWallet = parseFloat(targetUser.wallet_balance) || 0;
      console.log(`   • Target user wallet: ₹${targetWallet}`);
      console.log(`   • Target user premium: ${targetUser.is_premium ? 'Yes' : 'No'}`);
      console.log(`   • Target user referrals: ${targetUser.premium_referral_count || 0}`);
      
      if (targetWallet === 0) {
        console.log('   ⚠️ Target user wallet is ₹0 - needs manual update');
      } else if (targetWallet === 250) {
        console.log('   ✅ Target user wallet is ₹250 - correct amount');
      } else {
        console.log(`   ⚠️ Target user wallet is ₹${targetWallet} - unexpected amount`);
      }
    }

    console.log('\n🎉 DATABASE READ COMPLETE');
    console.log('═'.repeat(50));
    console.log('📋 Complete database state analyzed');
    console.log('💾 All tables and relationships checked');
    console.log('🔍 Target user status verified');

    return {
      totalUsers,
      premiumUsers,
      totalWalletBalance,
      targetUser,
      transactions: totalTransactions,
      referrals: totalReferrals
    };

  } catch (error) {
    console.error('❌ Database read failed:', error.message);
    return false;
  }
}

readIITDatabase().then(result => {
  if (result) {
    console.log('\n🚀 Database analysis completed successfully!');
    console.log('📊 Check the detailed analysis above for complete data state.');
  } else {
    console.log('\n⚠️ Database read failed. Check the errors above.');
  }
  process.exit(result ? 0 : 1);
});
