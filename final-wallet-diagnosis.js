// Final Wallet Diagnosis - Complete System Check
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Final Wallet Diagnosis - Complete System Check');
console.log('================================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function finalWalletDiagnosis() {
  try {
    console.log('1️⃣ CHECKING EXACT DATABASE STATE');
    console.log('─'.repeat(40));
    
    const targetEmail = '<EMAIL>';
    
    // Get the exact user record
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', targetEmail)
      .single();

    if (userError) {
      console.log('❌ User not found:', userError.message);
      return false;
    }

    console.log('✅ CURRENT DATABASE STATE:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Name: ${user.full_name}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Wallet Balance: ₹${user.wallet_balance || 0}`);
    console.log(`   Premium Referral Count: ${user.premium_referral_count || 0}`);
    console.log(`   Is Premium: ${user.is_premium}`);
    console.log(`   Referral Code: ${user.referral_code}`);
    console.log(`   Created: ${user.created_at}`);
    console.log(`   Updated: ${user.updated_at || 'Not set'}`);

    console.log('\n2️⃣ TESTING AUTHENTICATION');
    console.log('─'.repeat(40));
    
    // Check if we can authenticate as this user
    console.log('📝 Testing if user can login...');
    
    // Try common passwords
    const passwords = ['password', '123456', 'admin123', 'test123'];
    let loginSuccess = false;
    let correctPassword = null;
    
    for (const pwd of passwords) {
      try {
        const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
          email: targetEmail,
          password: pwd
        });

        if (authData?.user && !authError) {
          console.log(`✅ Login successful with password: ${pwd}`);
          console.log(`   Auth User ID: ${authData.user.id}`);
          console.log(`   Database User ID: ${user.id}`);
          console.log(`   IDs Match: ${authData.user.id === user.id ? '✅' : '❌'}`);
          
          loginSuccess = true;
          correctPassword = pwd;
          
          // Sign out immediately
          await supabase.auth.signOut();
          break;
        }
      } catch (e) {
        // Continue to next password
      }
    }

    if (!loginSuccess) {
      console.log('❌ Could not authenticate with common passwords');
      console.log('   This might be why the dashboard is not working');
    }

    console.log('\n3️⃣ CHECKING USER ID CONSISTENCY');
    console.log('─'.repeat(40));
    
    // Check if there are multiple users with same email
    const { data: allUsers, error: allError } = await supabase
      .from('users')
      .select('id, email, wallet_balance, created_at')
      .eq('email', targetEmail);

    if (allError) {
      console.log('❌ Error checking user consistency:', allError.message);
    } else {
      console.log(`✅ Found ${allUsers?.length || 0} users with email ${targetEmail}:`);
      allUsers?.forEach((u, index) => {
        console.log(`   ${index + 1}. ID: ${u.id}, Wallet: ₹${u.wallet_balance || 0}, Created: ${u.created_at}`);
      });
      
      if (allUsers && allUsers.length > 1) {
        console.log('⚠️ MULTIPLE USERS FOUND - This could be the issue!');
      }
    }

    console.log('\n4️⃣ FORCE WALLET UPDATE WITH VERIFICATION');
    console.log('─'.repeat(40));
    
    console.log('📝 Attempting multiple update methods...');
    
    // Method 1: Direct update
    const { error: update1 } = await supabase
      .from('users')
      .update({ 
        wallet_balance: 250.00,
        premium_referral_count: 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    console.log(`   Method 1 (Direct): ${update1 ? '❌ Failed' : '✅ Success'}`);
    if (update1) console.log(`      Error: ${update1.message}`);

    // Method 2: Upsert
    const { error: update2 } = await supabase
      .from('users')
      .upsert({
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        wallet_balance: 250.00,
        premium_referral_count: 1,
        is_premium: user.is_premium,
        is_admin: user.is_admin,
        referral_code: user.referral_code,
        referred_by: user.referred_by,
        updated_at: new Date().toISOString()
      });

    console.log(`   Method 2 (Upsert): ${update2 ? '❌ Failed' : '✅ Success'}`);
    if (update2) console.log(`      Error: ${update2.message}`);

    // Immediate verification
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('wallet_balance, premium_referral_count, updated_at')
      .eq('id', user.id)
      .single();

    if (verifyError) {
      console.log('❌ Verification failed:', verifyError.message);
    } else {
      console.log('✅ Post-update verification:');
      console.log(`   Wallet: ₹${verifyUser.wallet_balance}`);
      console.log(`   Referrals: ${verifyUser.premium_referral_count}`);
      console.log(`   Updated: ${verifyUser.updated_at}`);
    }

    console.log('\n5️⃣ CREATING TEST USER WITH WORKING WALLET');
    console.log('─'.repeat(40));
    
    console.log('📝 Creating a fresh test user with ₹250...');
    
    const testEmail = '<EMAIL>';
    const testPassword = 'test123';
    
    // Create auth user
    const { data: authUser, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test Wallet User'
        }
      }
    });

    if (authError && !authError.message.includes('already registered')) {
      console.log('❌ Test user creation failed:', authError.message);
    } else {
      console.log('✅ Test user auth created (or already exists)');
      
      // Create/update database record
      const { error: dbError } = await supabase
        .from('users')
        .upsert({
          email: testEmail,
          full_name: 'Test Wallet User',
          wallet_balance: 250.00,
          premium_referral_count: 1,
          is_premium: true,
          is_admin: false,
          referral_code: 'TESTREF123',
          created_at: new Date().toISOString()
        }, {
          onConflict: 'email'
        });

      if (dbError) {
        console.log('❌ Test user database record failed:', dbError.message);
      } else {
        console.log('✅ Test user database record created');
        console.log('');
        console.log('🎯 TEST USER CREDENTIALS:');
        console.log(`   Email: ${testEmail}`);
        console.log(`   Password: ${testPassword}`);
        console.log(`   Expected Wallet: ₹250`);
        console.log('');
        console.log('Try logging in with this test user to see if wallet works!');
      }
    }

    console.log('\n6️⃣ FRONTEND DEBUGGING GUIDE');
    console.log('─'.repeat(40));
    
    console.log('🔧 Debug the frontend with these steps:');
    console.log('');
    console.log('Step 1: Check Network Requests');
    console.log('   • Go to http://localhost:5173/dashboard');
    console.log('   • Press F12 → Network tab');
    console.log('   • Login with your credentials');
    console.log('   • Look for API calls to fetch user data');
    console.log('   • Check the response - does it show ₹250?');
    console.log('');
    console.log('Step 2: Check Console Errors');
    console.log('   • Press F12 → Console tab');
    console.log('   • Look for any JavaScript errors');
    console.log('   • Check for authentication errors');
    console.log('');
    console.log('Step 3: Check Local Storage');
    console.log('   • Press F12 → Application tab');
    console.log('   • Check Local Storage for cached user data');
    console.log('   • Clear all local storage');
    console.log('   • Refresh and login again');
    console.log('');
    console.log('Step 4: Test with Test User');
    console.log(`   • Login with: ${testEmail} / ${testPassword}`);
    console.log('   • See if ₹250 shows up for the test user');
    console.log('   • If yes, then original user has an issue');
    console.log('   • If no, then frontend has a bug');

    console.log('\n7️⃣ ADMIN PANEL VERIFICATION');
    console.log('─'.repeat(40));
    
    console.log('🔍 Verify in admin panel:');
    console.log('   1. Go to: http://localhost:5173/admin/dashboard/users');
    console.log(`   2. Search for: ${targetEmail}`);
    console.log('   3. Check wallet balance column');
    console.log(`   4. Also search for: ${testEmail}`);
    console.log('   5. Compare the wallet balances');
    console.log('');
    console.log('If admin panel shows ₹250 but user dashboard shows ₹0,');
    console.log('then it\'s definitely a frontend/authentication issue.');

    console.log('\n🎉 DIAGNOSIS COMPLETE');
    console.log('═'.repeat(50));
    console.log('📊 Summary:');
    console.log(`   Original user wallet: ₹${verifyUser?.wallet_balance || user.wallet_balance || 0}`);
    console.log(`   Login test: ${loginSuccess ? '✅ Success' : '❌ Failed'}`);
    console.log(`   Test user created: ✅ ${testEmail}`);
    console.log('');
    console.log('🔗 Next steps:');
    console.log('   1. Try logging in with test user credentials above');
    console.log('   2. Check admin panel for both users');
    console.log('   3. Use browser dev tools to debug frontend');
    console.log('   4. Report back what you find!');

    return {
      originalUserWallet: verifyUser?.wallet_balance || user.wallet_balance || 0,
      loginSuccess,
      correctPassword,
      testUserEmail: testEmail,
      testUserPassword: testPassword
    };

  } catch (error) {
    console.error('❌ Diagnosis failed:', error.message);
    return false;
  }
}

finalWalletDiagnosis().then(result => {
  if (result) {
    console.log('\n🚀 Diagnosis completed successfully!');
    console.log('💡 Try the test user login to isolate the issue!');
  } else {
    console.log('\n⚠️ Diagnosis failed. Check the errors above.');
  }
  process.exit(result ? 0 : 1);
});
