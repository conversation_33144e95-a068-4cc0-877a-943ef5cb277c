// Fix Admin Authentication Issues
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔐 Fixing Admin Authentication Issues');
console.log('====================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function fixAdminAuth() {
  try {
    console.log('1️⃣ CHECKING ADMIN USERS');
    console.log('─'.repeat(40));

    // Check for admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('id, email, full_name, is_admin')
      .eq('is_admin', true);

    if (adminError) {
      console.log('❌ Error checking admin users:', adminError.message);
      return false;
    }

    console.log(`✅ Found ${adminUsers?.length || 0} admin users`);
    
    if (adminUsers && adminUsers.length > 0) {
      adminUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
        console.log(`      Is Admin: ${user.is_admin ? '✅ Yes' : '❌ No'}`);
      });
    }

    console.log('\n2️⃣ CHECKING AUTH USERS TABLE');
    console.log('─'.repeat(40));

    // Check auth.users table for email confirmation status
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      console.log('❌ Error checking auth users:', authError.message);
      console.log('   This might require service role key for admin operations');
    } else {
      console.log(`✅ Found ${authUsers?.users?.length || 0} auth users`);
      
      authUsers?.users?.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email}`);
        console.log(`      Email confirmed: ${user.email_confirmed_at ? '✅ Yes' : '❌ No'}`);
        console.log(`      Created: ${user.created_at}`);
      });
    }

    console.log('\n3️⃣ CREATING/UPDATING ADMIN USER');
    console.log('─'.repeat(40));

    // Create a confirmed admin user
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456';
    const adminName = 'IIT Admin';

    console.log(`📝 Creating admin user: ${adminEmail}`);

    // Try to sign up the admin user
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          full_name: adminName,
          is_admin: true
        }
      }
    });

    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('ℹ️ Admin user already exists, checking confirmation status...');
        
        // Try to sign in to check status
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: adminEmail,
          password: adminPassword
        });

        if (signInError) {
          if (signInError.message.includes('Email not confirmed')) {
            console.log('❌ Admin email not confirmed');
            console.log('');
            console.log('🔧 MANUAL FIX REQUIRED:');
            console.log('1. Go to your Supabase dashboard');
            console.log('2. Navigate to Authentication > Users');
            console.log(`3. Find user: ${adminEmail}`);
            console.log('4. Click on the user');
            console.log('5. Toggle "Email Confirmed" to ON');
            console.log('6. Save changes');
            console.log('');
            console.log('🔗 Supabase Dashboard: https://supabase.com/dashboard');
          } else {
            console.log('❌ Sign in error:', signInError.message);
          }
        } else {
          console.log('✅ Admin user can sign in successfully');
          console.log(`   User ID: ${signInData.user?.id}`);
          console.log(`   Email confirmed: ${signInData.user?.email_confirmed_at ? 'Yes' : 'No'}`);
        }
      } else {
        console.log('❌ Sign up error:', signUpError.message);
      }
    } else {
      console.log('✅ Admin user created successfully');
      console.log(`   User ID: ${signUpData.user?.id}`);
      console.log(`   Email: ${signUpData.user?.email}`);
      console.log(`   Confirmation required: ${signUpData.user?.email_confirmed_at ? 'No' : 'Yes'}`);
    }

    console.log('\n4️⃣ UPDATING USER PROFILE');
    console.log('─'.repeat(40));

    // Ensure the user has admin privileges in the users table
    const { error: updateError } = await supabase
      .from('users')
      .upsert({
        email: adminEmail,
        full_name: adminName,
        is_admin: true,
        is_premium: true
      }, {
        onConflict: 'email'
      });

    if (updateError) {
      console.log('❌ Error updating user profile:', updateError.message);
    } else {
      console.log('✅ User profile updated with admin privileges');
    }

    console.log('\n5️⃣ TESTING ADMIN ACCESS');
    console.log('─'.repeat(40));

    // Test admin access
    const { data: testAdmin, error: testError } = await supabase
      .from('users')
      .select('id, email, full_name, is_admin')
      .eq('email', adminEmail)
      .single();

    if (testError) {
      console.log('❌ Error testing admin access:', testError.message);
    } else {
      console.log('✅ Admin user found in database');
      console.log(`   Name: ${testAdmin.full_name}`);
      console.log(`   Email: ${testAdmin.email}`);
      console.log(`   Is Admin: ${testAdmin.is_admin}`);
    }

    console.log('\n🎉 AUTHENTICATION FIX SUMMARY');
    console.log('═'.repeat(50));
    console.log('✅ Admin user exists in database');
    console.log('✅ User profile has admin privileges');
    console.log('');
    console.log('📋 ADMIN LOGIN CREDENTIALS:');
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: ${adminPassword}`);
    console.log('');
    console.log('🔗 LOGIN URLS:');
    console.log('   Admin Login: http://localhost:5173/admin');
    console.log('   User Login: http://localhost:5173/login');
    console.log('');
    console.log('⚠️ IF STILL GETTING "Email not confirmed" ERROR:');
    console.log('1. Go to Supabase Dashboard > Authentication > Users');
    console.log(`2. Find ${adminEmail} and confirm the email manually`);
    console.log('3. Or disable email confirmation in Auth settings');
    console.log('');
    console.log('🔧 ALTERNATIVE: Disable Email Confirmation');
    console.log('1. Supabase Dashboard > Authentication > Settings');
    console.log('2. Turn OFF "Enable email confirmations"');
    console.log('3. Save settings');

    return true;

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    return false;
  }
}

fixAdminAuth().then(success => {
  if (success) {
    console.log('\n🚀 Authentication fix completed!');
  } else {
    console.log('\n⚠️ Authentication fix failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
