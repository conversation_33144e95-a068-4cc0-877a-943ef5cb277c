import { createClient, SupabaseClient, Session } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Global singleton to prevent multiple instances across the entire app
declare global {
  var __supabase_client__: SupabaseClient | undefined;
  var __supabase_admin_client__: SupabaseClient | undefined;
  var __supabase_client_initialized__: boolean | undefined;
}

// Singleton instance holder
let supabaseInstance: SupabaseClient | null = null;

// Create singleton Supabase client with optimized configuration
export const supabase: SupabaseClient = (() => {
  // Return existing instance if available
  if (supabaseInstance) {
    return supabaseInstance;
  }

  // Check for existing global instance first (more robust checking)
  if (typeof window !== 'undefined') {
    if (window.__supabase_client__ && window.__supabase_client_initialized__) {
      supabaseInstance = window.__supabase_client__;
      return supabaseInstance;
    }
  }

  if (typeof global !== 'undefined') {
    if (global.__supabase_client__ && global.__supabase_client_initialized__) {
      supabaseInstance = global.__supabase_client__;
      return supabaseInstance;
    }
  }

  const client = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: 'pkce'
    },
    realtime: {
      params: {
        eventsPerSecond: 2 // Further reduced rate limiting
      }
    },
    global: {
      headers: {
        'apikey': supabaseAnonKey,
        'X-Client-Info': 'herbal-store@1.0.0'
      }
    },
    db: {
      schema: 'public'
    }
  });

  // Store in singleton and globally to prevent multiple instances
  supabaseInstance = client;

  if (typeof window !== 'undefined') {
    window.__supabase_client__ = client;
    window.__supabase_client_initialized__ = true;
  } else if (typeof global !== 'undefined') {
    global.__supabase_client__ = client;
    global.__supabase_client_initialized__ = true;
  }

  return client;
})();

// Single auth state listener to prevent multiple instances
let authListenerInitialized = false;

// Initialize auth listener only once
if (!authListenerInitialized && typeof window !== 'undefined') {
  supabase.auth.onAuthStateChange((event, session) => {
    // Handle auth state changes
    switch (event) {
      case 'SIGNED_IN':
        if (import.meta.env.DEV && session?.user?.email) {
          const lastLoggedUser = sessionStorage.getItem('lastLoggedUser');
          if (lastLoggedUser !== session.user.email) {
            console.log('✅ User authenticated:', session.user.email);
            sessionStorage.setItem('lastLoggedUser', session.user.email);
          }
        }
        break;
      case 'SIGNED_OUT':
        if (import.meta.env.DEV) {
          console.log('👋 User signed out');
        }
        sessionStorage.removeItem('lastLoggedUser');
        // Clear any cached data
        localStorage.removeItem('sb-zvsyozsltpczzuwonczs-auth-token');
        break;
      case 'TOKEN_REFRESHED':
        // Silent refresh, no logging needed
        break;
      case 'PASSWORD_RECOVERY':
        // Handle password recovery session
        if (import.meta.env.DEV) {
          console.log('🔐 Password recovery session started');
        }
        break;
    }
  });
  authListenerInitialized = true;
}

// Helper function to clear auth state
export const clearAuthState = async () => {
  try {
    await supabase.auth.signOut();
    localStorage.clear();
    sessionStorage.clear();
  } catch (error) {
    console.error('Error clearing auth state:', error);
  }
};

// Helper function to check if user is admin
export const isAdmin = async (): Promise<boolean> => {
  const { data: { user } } = await supabase.auth.getUser();
  return user?.email === '<EMAIL>' || 
         (user?.app_metadata as any)?.is_admin === true;
};

// Helper function to get current user
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

export default supabase;