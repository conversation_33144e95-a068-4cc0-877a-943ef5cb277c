import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CONFIG } from '../config/supabase';

if (!SUPABASE_CONFIG.url) {
  throw new Error('Missing Supabase URL configuration');
}

// Get service role key or fallback to anon key
const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || SUPABASE_CONFIG.anonKey;

if (!serviceRoleKey) {
  throw new Error('Missing Supabase service role key configuration');
}

// Create singleton admin Supabase client with service role for admin operations
// This client bypasses RLS policies and has full database access
export const adminSupabase = (() => {
  // Check if we already have a global admin client
  if (typeof window !== 'undefined' && window.__supabase_admin_client__) {
    return window.__supabase_admin_client__;
  }

  if (typeof global !== 'undefined' && global.__supabase_admin_client__) {
    return global.__supabase_admin_client__;
  }

  const adminClient = createClient(SUPABASE_CONFIG.url, serviceRoleKey, {
    auth: {
      persistSession: false, // Admin operations don't need session persistence
      autoRefreshToken: false,
      detectSessionInUrl: false,
      storageKey: `sb-admin-${SUPABASE_CONFIG.url.split('//')[1].split('.')[0]}-auth-token`, // Unique admin storage key
      debug: false
    },
    realtime: {
      params: {
        eventsPerSecond: 1 // Minimal realtime for admin operations
      }
    },
    global: {
      headers: {
        'apikey': serviceRoleKey,
        'X-Client-Info': 'herbal-store-admin@1.0.0'
      }
    }
  });

  // Store globally to prevent multiple instances
  if (typeof window !== 'undefined') {
    window.__supabase_admin_client__ = adminClient;
  } else if (typeof global !== 'undefined') {
    global.__supabase_admin_client__ = adminClient;
  }

  return adminClient;
})();

// Note: This client should only be used for admin operations
// It has elevated privileges and bypasses Row Level Security
// Separate storage key prevents conflicts with regular client

// Helper function to check if current user is admin
export const isAdminUser = async (email: string): Promise<boolean> => {
  return email === '<EMAIL>' || email === '<EMAIL>';
};
