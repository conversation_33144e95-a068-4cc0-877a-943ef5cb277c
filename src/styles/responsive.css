/* Mobile-first responsive utilities */

/* Touch targets - minimum 44px for accessibility */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Extra small screens */
@media (min-width: 475px) {
  .xs\:block {
    display: block;
  }
  
  .xs\:hidden {
    display: none;
  }
  
  .xs\:flex {
    display: flex;
  }
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
}

/* Smooth transitions for mobile interactions */
.mobile-transition {
  transition: all 0.3s ease-in-out;
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .responsive-text-lg {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .responsive-text-xl {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  .responsive-text-2xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  .responsive-text-3xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  .responsive-text-4xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  
  .responsive-text-6xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/* Mobile-friendly spacing */
@media (max-width: 640px) {
  .mobile-px {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mobile-py {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  
  .mobile-gap {
    gap: 0.75rem;
  }
}

/* Responsive grid utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Modal responsive utilities */
.responsive-modal {
  margin: 1rem;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

@media (min-width: 640px) {
  .responsive-modal {
    margin: 2rem;
    max-height: calc(100vh - 4rem);
  }
}

/* Safe area for mobile devices with notches */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Responsive container */
.responsive-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .responsive-container {
    max-width: 1536px;
  }
}
