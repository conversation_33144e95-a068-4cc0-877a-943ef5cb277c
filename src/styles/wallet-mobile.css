/* Mobile-specific styles for Wallet Page */

/* Touch targets for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Mobile-optimized animations */
@media (max-width: 640px) {
  .wallet-locked-overlay {
    animation: fadeInUp 0.5s ease-out;
  }
  
  .wallet-progress-bar {
    animation: progressFill 1s ease-out 0.3s both;
  }
  
  .wallet-step-item {
    transition: all 0.3s ease;
  }
  
  .wallet-step-item:hover {
    transform: translateX(4px);
  }
  
  .wallet-step-completed {
    animation: checkmarkBounce 0.6s ease-out;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressFill {
  from {
    width: 0%;
  }
}

@keyframes checkmarkBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* Mobile-specific blur effects */
@media (max-width: 640px) {
  .mobile-blur-light {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  
  .mobile-gradient-overlay {
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%);
  }
}

/* Mobile-optimized shadows */
@media (max-width: 640px) {
  .mobile-card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .mobile-card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

/* Mobile-specific spacing */
@media (max-width: 640px) {
  .mobile-safe-area {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Mobile-optimized text */
@media (max-width: 640px) {
  .mobile-text-balance {
    text-wrap: balance;
    line-height: 1.4;
  }
  
  .mobile-text-readable {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
  }
}

/* Mobile-specific button styles */
@media (max-width: 640px) {
  .mobile-button {
    min-height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 12px;
    transition: all 0.2s ease;
  }

  .mobile-button:active {
    transform: scale(0.98);
  }

  .mobile-button-primary {
    background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .mobile-button-primary:active {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  }
}

/* Dashboard hamburger menu fixes */
@media (max-width: 1024px) {
  .dashboard-hamburger {
    position: relative !important;
    z-index: 9999 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    background: white !important;
    border: 2px solid #d1d5db !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  }

  .dashboard-hamburger:hover {
    border-color: #10b981 !important;
    background: #f0fdf4 !important;
  }

  .dashboard-hamburger:active {
    transform: scale(0.95) !important;
  }

  /* Ensure hamburger is always on top */
  .dashboard-mobile-header {
    position: relative !important;
    z-index: 9998 !important;
  }

  /* Dashboard logo styling */
  .dashboard-logo {
    transition: all 0.2s ease;
  }

  .dashboard-logo:active {
    transform: scale(0.98);
  }

  .dashboard-logo:hover .logo-icon {
    transform: scale(1.1);
  }
}

/* Mobile-specific progress indicators */
@media (max-width: 640px) {
  .mobile-progress-container {
    position: relative;
    overflow: hidden;
  }
  
  .mobile-progress-bar {
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }
  
  .mobile-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Mobile-specific step indicators */
@media (max-width: 640px) {
  .mobile-step-indicator {
    position: relative;
  }
  
  .mobile-step-indicator::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 2px;
    height: 20px;
    background: #E5E7EB;
    transform: translateX(-50%);
  }
  
  .mobile-step-indicator:last-child::before {
    display: none;
  }
  
  .mobile-step-completed::before {
    background: #10B981;
  }
}

/* Mobile-specific loading states */
@media (max-width: 640px) {
  .mobile-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile-specific focus states */
@media (max-width: 640px) {
  .mobile-focus:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
    border-radius: 8px;
  }
  
  .mobile-focus:focus-visible {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
  }
}

/* Mobile-specific accessibility */
@media (max-width: 640px) {
  .mobile-high-contrast {
    filter: contrast(1.1);
  }
  
  .mobile-reduce-motion {
    animation: none !important;
    transition: none !important;
  }
}

/* Respect user preferences */
@media (prefers-reduced-motion: reduce) {
  .wallet-locked-overlay,
  .wallet-progress-bar,
  .wallet-step-item,
  .mobile-progress-bar,
  .mobile-skeleton {
    animation: none !important;
    transition: none !important;
  }
}

@media (prefers-contrast: high) {
  .mobile-blur-light {
    backdrop-filter: none;
    background: rgba(255, 255, 255, 0.95);
  }
}
