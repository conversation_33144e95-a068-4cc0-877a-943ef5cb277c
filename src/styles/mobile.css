/* Mobile-specific styles and utilities */

/* Touch targets for better mobile interaction */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Improved scrolling on mobile */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
}

/* Better table responsiveness */
.mobile-table {
  display: block;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
  .mobile-table table {
    min-width: 600px;
  }
}

/* Mobile-friendly form inputs */
.mobile-input {
  font-size: 16px; /* Prevents zoom on iOS */
  -webkit-appearance: none;
  border-radius: 8px;
}

/* Mobile navigation improvements */
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

/* Mobile modal improvements */
.mobile-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

@media (max-width: 640px) {
  .mobile-modal {
    align-items: flex-end;
  }
  
  .mobile-modal > div {
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 12px 12px 0 0;
  }
}

/* Mobile card improvements */
.mobile-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  background: white;
  overflow: hidden;
}

@media (max-width: 640px) {
  .mobile-card {
    margin: 0 -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}

/* Mobile button improvements */
.mobile-button {
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
  touch-action: manipulation;
}

.mobile-button:active {
  transform: scale(0.98);
}

/* Mobile text improvements */
@media (max-width: 640px) {
  .mobile-text-responsive {
    font-size: 14px;
    line-height: 1.5;
  }
  
  .mobile-heading-responsive {
    font-size: 20px;
    line-height: 1.3;
  }
}

/* Mobile spacing utilities */
.mobile-spacing {
  padding: 1rem;
}

@media (min-width: 640px) {
  .mobile-spacing {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-spacing {
    padding: 2rem;
  }
}

/* Mobile grid improvements */
.mobile-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Mobile safe areas for devices with notches */
.mobile-safe-top {
  padding-top: env(safe-area-inset-top);
}

.mobile-safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile-friendly animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mobile focus improvements */
@media (max-width: 768px) {
  *:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

/* Mobile sticky elements */
.mobile-sticky {
  position: sticky;
  top: 0;
  z-index: 40;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

/* Mobile overflow handling */
.mobile-overflow {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-overflow::-webkit-scrollbar {
  height: 4px;
}

.mobile-overflow::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.mobile-overflow::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

/* Mobile image optimization */
.mobile-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 8px;
}

/* Mobile loading states */
.mobile-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.mobile-loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile accessibility improvements */
@media (max-width: 768px) {
  .sr-only-mobile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

/* Mobile dark mode support */
@media (prefers-color-scheme: dark) {
  .mobile-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .mobile-nav {
    background: #1f2937;
    border-color: #374151;
  }
}

/* Mobile print styles */
@media print {
  .mobile-no-print {
    display: none !important;
  }
  
  .mobile-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
