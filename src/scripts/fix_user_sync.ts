import { AuthFixService } from '../services/authFixService';

async function fixUserSync() {
  try {
    // Replace with the email of the user having issues
    const email = '<EMAIL>';
    
    console.log('Starting user sync fix...');
    const result = await AuthFixService.fixUserSync(email);
    
    if (result.success) {
      console.log('✅ User sync fixed successfully:', result.user);
    } else {
      console.error('❌ Failed to fix user sync:', result.error);
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixUserSync(); 