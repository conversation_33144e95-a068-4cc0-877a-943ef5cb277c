import { supabase } from '../lib/supabase';
import { MLM_CONFIG } from '../services/mlmReferralService';

/**
 * Migration script to update the system from multi-level referral to MLM system
 */
export class MLMSystemMigration {
  
  async runMigration(): Promise<void> {
    console.log('🚀 Starting MLM System Migration...');
    
    try {
      // Step 1: Update admin settings for MLM system
      await this.updateAdminSettings();
      
      // Step 2: Update app configuration
      await this.updateAppConfiguration();
      
      // Step 3: Clean up old multi-level settings (optional)
      await this.cleanupOldSettings();
      
      console.log('✅ MLM System Migration completed successfully!');
      console.log('');
      console.log('🎯 New MLM System Features:');
      console.log('   • 1st & 2nd referrals: Bonus to direct referrer');
      console.log('   • 3rd+ referrals: Bonus to grandparent (referrer\'s referrer)');
      console.log(`   • Fixed bonus amount: ₹${MLM_CONFIG.BONUS_AMOUNT} per referral`);
      console.log('   • Simplified logic with clear rules');
      
    } catch (error) {
      console.error('❌ MLM System Migration failed:', error);
      throw error;
    }
  }
  
  private async updateAdminSettings(): Promise<void> {
    console.log('📝 Updating admin settings for MLM system...');
    
    const mlmSettings = [
      {
        setting_key: 'mlm_system_enabled',
        setting_value: 'true',
        description: 'Enable MLM referral system',
        category: 'mlm_system'
      },
      {
        setting_key: 'mlm_bonus_amount',
        setting_value: MLM_CONFIG.BONUS_AMOUNT.toString(),
        description: 'Fixed bonus amount for each MLM referral',
        category: 'mlm_system'
      },
      {
        setting_key: 'mlm_direct_referral_limit',
        setting_value: MLM_CONFIG.DIRECT_REFERRAL_LIMIT.toString(),
        description: 'Number of referrals that go to direct referrer (1st & 2nd)',
        category: 'mlm_system'
      },
      {
        setting_key: 'mlm_grandparent_referral_start',
        setting_value: MLM_CONFIG.GRANDPARENT_REFERRAL_START.toString(),
        description: 'Starting position for grandparent referrals (3rd+)',
        category: 'mlm_system'
      },
      {
        setting_key: 'system_type',
        setting_value: 'mlm',
        description: 'Current referral system type (mlm or multi_level)',
        category: 'system_config'
      }
    ];
    
    for (const setting of mlmSettings) {
      const { error } = await supabase
        .from('admin_settings')
        .upsert({
          ...setting,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      if (error) {
        console.error(`Failed to update setting ${setting.setting_key}:`, error);
      } else {
        console.log(`✅ Updated setting: ${setting.setting_key}`);
      }
    }
  }
  
  private async updateAppConfiguration(): Promise<void> {
    console.log('⚙️ Updating app configuration...');
    
    // Update any app-specific configurations
    const appSettings = [
      {
        setting_key: 'app_name',
        setting_value: 'IIT MLM System',
        description: 'Application name',
        category: 'app_config'
      },
      {
        setting_key: 'referral_system_version',
        setting_value: '2.0',
        description: 'Current referral system version',
        category: 'system_config'
      },
      {
        setting_key: 'migration_date',
        setting_value: new Date().toISOString(),
        description: 'Date when MLM system was migrated',
        category: 'system_config'
      }
    ];
    
    for (const setting of appSettings) {
      const { error } = await supabase
        .from('admin_settings')
        .upsert({
          ...setting,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      if (error) {
        console.error(`Failed to update app setting ${setting.setting_key}:`, error);
      } else {
        console.log(`✅ Updated app setting: ${setting.setting_key}`);
      }
    }
  }
  
  private async cleanupOldSettings(): Promise<void> {
    console.log('🧹 Cleaning up old multi-level referral settings...');
    
    // Disable old multi-level system
    const { error: disableError } = await supabase
      .from('admin_settings')
      .upsert({
        setting_key: 'multi_level_referral_enabled',
        setting_value: 'false',
        description: 'Multi-level referral system disabled (replaced by MLM)',
        category: 'referral_system',
        updated_at: new Date().toISOString()
      });
    
    if (disableError) {
      console.error('Failed to disable old multi-level system:', disableError);
    } else {
      console.log('✅ Disabled old multi-level referral system');
    }
  }
  
  // Test the MLM logic
  async testMLMLogic(): Promise<void> {
    console.log('🧪 Testing MLM logic...');
    
    const testCases = [
      { position: 1, expectedRecipient: 'direct_referrer', description: '1st referral' },
      { position: 2, expectedRecipient: 'direct_referrer', description: '2nd referral' },
      { position: 3, expectedRecipient: 'grandparent', description: '3rd referral' },
      { position: 4, expectedRecipient: 'grandparent', description: '4th referral' },
      { position: 5, expectedRecipient: 'grandparent', description: '5th referral' }
    ];
    
    console.log('Test Results:');
    testCases.forEach(testCase => {
      const actualRecipient = testCase.position <= MLM_CONFIG.DIRECT_REFERRAL_LIMIT 
        ? 'direct_referrer' 
        : 'grandparent';
      
      const isCorrect = actualRecipient === testCase.expectedRecipient;
      const status = isCorrect ? '✅' : '❌';
      
      console.log(`${status} ${testCase.description}: Bonus goes to ${actualRecipient}`);
    });
  }
  
  // Verify migration
  async verifyMigration(): Promise<boolean> {
    console.log('🔍 Verifying MLM migration...');
    
    try {
      // Check if MLM settings exist
      const { data: mlmSettings, error } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value')
        .eq('category', 'mlm_system');
      
      if (error) {
        console.error('Error verifying migration:', error);
        return false;
      }
      
      const requiredSettings = ['mlm_system_enabled', 'mlm_bonus_amount'];
      const existingSettings = mlmSettings?.map(s => s.setting_key) || [];
      
      const allSettingsExist = requiredSettings.every(setting => 
        existingSettings.includes(setting)
      );
      
      if (allSettingsExist) {
        console.log('✅ MLM migration verification successful');
        return true;
      } else {
        console.log('❌ MLM migration verification failed - missing settings');
        return false;
      }
      
    } catch (error) {
      console.error('Error during verification:', error);
      return false;
    }
  }
}

// Export function to run migration
export const runMLMMigration = async (): Promise<void> => {
  const migration = new MLMSystemMigration();
  
  console.log('🎯 IIT MLM System Migration');
  console.log('============================');
  
  await migration.runMigration();
  await migration.testMLMLogic();
  
  const isVerified = await migration.verifyMigration();
  
  if (isVerified) {
    console.log('');
    console.log('🎉 Migration completed successfully!');
    console.log('Your system is now using the new MLM referral logic.');
  } else {
    console.log('');
    console.log('⚠️ Migration completed but verification failed.');
    console.log('Please check the admin settings manually.');
  }
};
