import { create } from 'zustand';
import { Product, Category } from '../types';
import { supabase } from '../lib/supabase';

interface ProductState {
  products: Product[];
  categories: Category[];
  featuredProducts: Product[];
  isLoading: boolean;
  filters: {
    category: string;
    subcategory: string;
    priceRange: [number, number];
    rating: number;
    inStock: boolean;
    isPremiumOnly: boolean;
  };
  searchQuery: string;
  sortBy: 'name' | 'price-low' | 'price-high' | 'rating' | 'newest';
  fetchProducts: () => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchFeaturedProducts: () => Promise<void>;
  setFilters: (filters: Partial<ProductState['filters']>) => void;
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: ProductState['sortBy']) => void;
  getFilteredProducts: () => Product[];
  addProduct: (product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => Promise<{ success: boolean; error?: string }>;
}

export const useProductStore = create<ProductState>((set, get) => ({
  products: [],
  categories: [],
  featuredProducts: [],
  isLoading: false,
  filters: {
    category: '',
    subcategory: '',
    priceRange: [0, 1000],
    rating: 0,
    inStock: false,
    isPremiumOnly: false
  },
  searchQuery: '',
  sortBy: 'name',

  fetchProducts: async () => {
    set({ isLoading: true });
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      set({ products: data || [], isLoading: false });
    } catch (error) {
      console.error('Error fetching products:', error);
      set({ isLoading: false });
    }
  },

  fetchCategories: async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) throw error;
      set({ categories: data || [] });
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  },

  fetchFeaturedProducts: async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_featured', true)
        .order('created_at', { ascending: false })
        .limit(6);

      if (error) throw error;
      set({ featuredProducts: data || [] });
    } catch (error) {
      console.error('Error fetching featured products:', error);
    }
  },

  setFilters: (newFilters) => {
    set({ filters: { ...get().filters, ...newFilters } });
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },

  setSortBy: (sortBy) => {
    set({ sortBy });
  },

  getFilteredProducts: () => {
    const { products, filters, searchQuery, sortBy } = get();
    let filtered = [...products];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category);
    }

    // Apply price range filter
    filtered = filtered.filter(product =>
      product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]
    );

    // Apply rating filter
    if (filters.rating > 0) {
      filtered = filtered.filter(product => product.rating >= filters.rating);
    }

    // Apply stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product => product.stock_quantity > 0);
    }

    // Apply premium filter
    if (filters.isPremiumOnly) {
      filtered = filtered.filter(product => product.is_premium_only);
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      default:
        filtered.sort((a, b) => a.name.localeCompare(b.name));
    }

    return filtered;
  },

  addProduct: async (product) => {
    try {
      const { data, error } = await supabase
        .from('products')
        .insert([product])
        .select()
        .single();

      if (error) throw error;

      // Update the products list with the new product
      set(state => ({
        products: [data, ...state.products]
      }));

      return { success: true };
    } catch (error: any) {
      console.error('Error adding product:', error);
      return { success: false, error: error.message };
    }
  }
}));