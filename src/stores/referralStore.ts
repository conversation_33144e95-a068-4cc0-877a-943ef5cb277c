import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { useAuthStore } from './authStore';

interface Referral {
  id: string;
  referred_user?: {
    full_name: string;
  };
  created_at: string;
  bonus_amount: number;
  status: 'pending' | 'completed';
}

interface ReferralStats {
  totalReferrals: number;
  totalEarned: number;
}

interface ReferralState {
  referrals: Referral[];
  stats: ReferralStats;
  loading: boolean;
  error: string | null;
  fetchReferrals: () => Promise<void>;
  generateReferralCode: () => Promise<string | null>;
}

export const useReferralStore = create<ReferralState>((set) => ({
  referrals: [],
  stats: {
    totalReferrals: 0,
    totalEarned: 0,
  },
  loading: false,
  error: null,

  fetchReferrals: async () => {
    try {
      set({ loading: true, error: null });

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Fetch referrals for the current user
      const { data: referralsData, error: referralsError } = await supabase
        .from('referrals')
        .select(`
          id,
          created_at,
          bonus_amount,
          status,
          referred_user:referred_user_id (
            full_name
          )
        `)
        .eq('referrer_id', user.id)
        .order('created_at', { ascending: false });

      if (referralsError) throw referralsError;

      // Calculate stats
      const referrals = referralsData || [];
      const totalReferrals = referrals.length;
      const totalEarned = referrals
        .filter(ref => ref.status === 'completed')
        .reduce((sum, ref) => sum + ref.bonus_amount, 0);

      set({
        referrals,
        stats: { totalReferrals, totalEarned },
        loading: false,
      });
    } catch (error) {
      console.error('Error fetching referrals:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch referrals',
        loading: false,
      });
    }
  },

  generateReferralCode: async () => {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Generate a unique referral code
      const referralCode = `REF${user.id.slice(0, 8).toUpperCase()}${Date.now().toString().slice(-4)}`;

      // Update user's referral code in the database
      const { error } = await supabase
        .from('users')
        .update({ referral_code: referralCode })
        .eq('id', user.id);

      if (error) throw error;

      // Refresh auth store to get updated user data
      const authStore = useAuthStore.getState();
      if (authStore.checkUser) {
        await authStore.checkUser();
      }

      return referralCode;
    } catch (error) {
      console.error('Error generating referral code:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to generate referral code',
      });
      return null;
    }
  },
})); 