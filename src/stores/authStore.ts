import { create } from 'zustand';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';
import { safeSignIn, syncAuthWithDatabase } from '../utils/authSync';
import { errorHandler } from '../utils/errorHandler';

interface RegistrationData {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  referralCode?: string;
  mobile?: string;
  kycData?: {
    aadhaarFrontUrl: string;
    aadhaarBackUrl: string;
    panCardUrl: string;
    accountNumber: string;
    ifscCode: string;
    accountHolderName: string;
    bankName: string;
  };
}

interface AuthState {
  user: User | null;
  isAdmin: boolean;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean; // Add isAuthenticated property
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  register: (data: RegistrationData) => Promise<{ success: boolean; user?: User; error?: string }>;
  signOut: () => Promise<void>;
  checkUser: () => Promise<void>;
  refreshUser: () => Promise<void>; // Add refreshUser function
  resetLoadingState: () => void; // Add manual reset function
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAdmin: false,
  isLoading: false, // Start with false to prevent stuck loading state
  error: null,
  isAuthenticated: false, // Add isAuthenticated initial state

  signIn: async (email: string, password: string) => {
    // Prevent multiple simultaneous sign-in attempts
    if (get().isLoading) {
      console.log('⚠️ Sign in already in progress, ignoring duplicate request');
      return;
    }

    let timeoutId: NodeJS.Timeout | null = null;

    try {
      set({ isLoading: true, error: null });

      console.log('🔐 Starting sign in process...');

      // Set a timeout to prevent infinite loading (30 seconds)
      timeoutId = setTimeout(() => {
        console.error('⏰ Sign in timeout - resetting loading state');
        set({ isLoading: false, error: 'Sign in timed out. Please try again.' });
      }, 30000);

      // Use the safe sign in method that handles profile sync
      const result = await safeSignIn(email, password);

      // Clear timeout since we succeeded
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      // Merge profile data with user object
      const enhancedUser = {
        ...result.user,
        ...result.profile,
        user_metadata: {
          ...result.user.user_metadata,
          ...result.profile
        }
      };

      // Set auth state
      set({
        user: enhancedUser,
        isAdmin: result.user.email === '<EMAIL>' ||
                (result.user.app_metadata as any)?.is_admin === true,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      console.log('✅ Sign in completed successfully');

    } catch (error: any) {
      console.error('❌ Sign in failed:', error);

      // Clear timeout on error
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Use enhanced error handling
      const errorMessage = errorHandler.handleAuthError(error, {
        component: 'AuthStore',
        action: 'signIn',
        additionalData: { email }
      });

      // Always reset loading state on error
      set({
        error: errorMessage || 'Sign in failed. Please try again.',
        isLoading: false,
        user: null,
        isAdmin: false,
        isAuthenticated: false
      });
      throw error;
    }
  },

  signUp: async (email: string, password: string, fullName: string) => {
    try {
      set({ isLoading: true, error: null });
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) throw error;

      // Create user profile in public.users table
      if (data.user) {
        const { error: profileError } = await supabase
          .from('users')
          .insert([
            {
              id: data.user.id,
              email: email,
              full_name: fullName,
              username: email.split('@')[0],
              is_premium: false,
              wallet_balance: 0
            }
          ]);

        if (profileError) throw profileError;
      }

      set({
        user: data.user,
        isAdmin: data.user?.email === '<EMAIL>' ||
                (data.user?.app_metadata as any)?.is_admin === true,
        isAuthenticated: !!data.user,
        isLoading: false
      });
    } catch (error: any) {
      set({ error: error.message, isLoading: false, isAuthenticated: false });
      throw error;
    }
  },

  register: async (registrationData: RegistrationData) => {
    try {
      set({ isLoading: true, error: null });

      console.log('🔐 Starting registration process...');
      console.log('📝 Registration data:', registrationData);

      // Step 1: Check for existing users with same email or username
      const { data: existingUsers, error: checkError } = await supabase
        .from('users')
        .select('email, username')
        .or(`email.eq.${registrationData.email},username.eq.${registrationData.username}`);

      if (checkError) {
        console.error('❌ Error checking existing users:', checkError);
      } else if (existingUsers && existingUsers.length > 0) {
        const existingUser = existingUsers[0];
        let errorMsg = '';
        if (existingUser.email === registrationData.email) {
          errorMsg = 'An account with this email already exists';
        } else if (existingUser.username === registrationData.username) {
          errorMsg = 'This username is already taken';
        }
        set({ error: errorMsg, isLoading: false });
        return { success: false, error: errorMsg };
      }

      // Step 2: Generate a unique referral code
      let referralCode = '';
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        referralCode = 'REF' + Math.random().toString(36).substring(2, 10).toUpperCase();

        const { data: existingCode } = await supabase
          .from('users')
          .select('referral_code')
          .eq('referral_code', referralCode)
          .single();

        if (!existingCode) break; // Code is unique
        attempts++;
      }

      if (attempts >= maxAttempts) {
        const errorMsg = 'Unable to generate unique referral code. Please try again.';
        set({ error: errorMsg, isLoading: false });
        return { success: false, error: errorMsg };
      }

      console.log('✅ Generated unique referral code:', referralCode);

      // Step 3: Create auth user (trigger is now disabled)
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: registrationData.email,
        password: registrationData.password,
        options: {
          data: {
            full_name: `${registrationData.firstName} ${registrationData.lastName}`,
            username: registrationData.username
          }
        }
      });

      if (authError) {
        console.error('❌ Auth signup failed:', authError);
        set({ error: authError.message, isLoading: false });
        return { success: false, error: authError.message };
      }

      if (!authData.user) {
        const errorMsg = 'No user data returned from signup';
        set({ error: errorMsg, isLoading: false });
        return { success: false, error: errorMsg };
      }

      console.log('✅ Auth user created:', authData.user.id);

      // Step 4: Create user profile manually (in case trigger failed)
      const userProfile = {
        id: authData.user.id,
        email: registrationData.email,
        full_name: `${registrationData.firstName} ${registrationData.lastName}`,
        username: registrationData.username,
        mobile_number: registrationData.mobile || null,
        referral_code: referralCode,
        is_premium: false,
        wallet_balance: 0,
        ewallet_unlocked: false,
        // KYC data if provided
        ...(registrationData.kycData && {
          aadhaar_front_url: registrationData.kycData.aadhaarFrontUrl,
          aadhaar_back_url: registrationData.kycData.aadhaarBackUrl,
          pan_card_url: registrationData.kycData.panCardUrl,
          account_number_encrypted: registrationData.kycData.accountNumber,
          ifsc_code: registrationData.kycData.ifscCode,
          account_holder_name: registrationData.kycData.accountHolderName,
          bank_name: registrationData.kycData.bankName,
          kyc_status: 'under_review',
          kyc_submitted_at: new Date().toISOString()
        })
      };

      // Insert user profile (trigger is disabled, so we handle it manually)
      const { error: insertError } = await supabase
        .from('users')
        .insert([userProfile]);

      if (insertError) {
        console.error('❌ Profile creation failed:', insertError);
        set({ error: insertError.message, isLoading: false });
        return { success: false, error: insertError.message };
      }

      console.log('✅ User profile created successfully');

      // Step 5: Set auth state
      set({
        user: authData.user,
        isAdmin: authData.user.email === '<EMAIL>' ||
                (authData.user.app_metadata as any)?.is_admin === true,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      console.log('✅ Registration completed successfully');
      return { success: true, user: authData.user };

    } catch (error: any) {
      console.error('❌ Registration failed:', error);
      set({ error: error.message || 'Registration failed', isLoading: false, isAuthenticated: false });
      return { success: false, error: error.message || 'Registration failed' };
    }
  },

  signOut: async () => {
    try {
      set({ isLoading: true, error: null });
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      set({ user: null, isAdmin: false, isAuthenticated: false, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  checkUser: async () => {
    try {
      set({ isLoading: true, error: null });

      // Simple direct session check without token manager complications
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.log('Session error:', sessionError.message);
        set({ user: null, isAdmin: false, isAuthenticated: false, isLoading: false, error: null });
        return;
      }

      if (!session) {
        console.log('No session found');
        set({ user: null, isAdmin: false, isAuthenticated: false, isLoading: false, error: null });
        return;
      }

      // Get user from session
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.log('User error:', userError?.message || 'No user');
        set({ user: null, isAdmin: false, isAuthenticated: false, isLoading: false, error: null });
        return;
      }

      if (user) {
        console.log('✅ User session found, syncing profile...');

        // Sync user profile with database
        const profile = await syncAuthWithDatabase(user);

        // Merge profile data with user object
        const enhancedUser = {
          ...user,
          ...profile,
          user_metadata: {
            ...user.user_metadata,
            ...profile
          }
        };

        // Check admin status
        const isAdminUser = user.email === '<EMAIL>' ||
                           user.email === '<EMAIL>' ||
                           (user.app_metadata as any)?.is_admin === true ||
                           profile?.is_admin === true;

        console.log('Admin check:', { email: user.email, isAdmin: isAdminUser });

        set({
          user: enhancedUser,
          isAdmin: isAdminUser,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });
      } else {
        console.log('ℹ️ No user session found');
        set({ user: null, isAdmin: false, isAuthenticated: false, isLoading: false, error: null });
      }
    } catch (error: any) {
      // Don't log session missing errors as they're expected when not logged in
      if (!error.message?.includes('Auth session missing')) {
        console.error('❌ Check user failed:', error);
      }
      set({ error: null, isLoading: false, user: null, isAdmin: false, isAuthenticated: false });
      // Don't throw error here to prevent app from crashing on initial load
    }
  },

  refreshUser: async () => {
    try {
      const { user } = get();
      if (!user?.id) return;

      console.log('🔄 Refreshing user data...');

      // Get fresh user data from database with specific columns to avoid 406 errors
      const { data: userData, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          username,
          is_premium,
          wallet_balance,
          referral_code,
          referred_by,
          referred_by_id,
          mobile_number,
          mobile_verified,
          kyc_status,
          ewallet_unlocked,
          premium_lifetime_access,
          premium_purchased_at,
          created_at,
          updated_at
        `)
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('❌ Failed to refresh user data:', error);
        console.error('Error details:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });

        // If it's a 406 error, it might be a temporary issue, so don't fail completely
        if (error.code === 'PGRST116' || error.message?.includes('406')) {
          console.log('⚠️ Temporary access issue, continuing with existing user data');
          return;
        }
        return;
      }

      // Update the user object with fresh data
      const updatedUser = {
        ...user,
        ...userData,
        user_metadata: {
          ...user.user_metadata,
          ...userData
        }
      };

      set({ user: updatedUser });
      console.log('✅ User data refreshed successfully');
    } catch (error: any) {
      console.error('❌ Error refreshing user:', error);
    }
  },

  // Manual reset function to fix stuck loading states
  resetLoadingState: () => {
    console.log('🔄 Manually resetting loading state');
    set({ isLoading: false });
  }
}));
