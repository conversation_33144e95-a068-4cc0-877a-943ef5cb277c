import { create } from 'zustand';
import { WalletTransaction } from '../types';
import { supabase } from '../lib/supabase';
import { SupabaseDatabaseFunctions } from '../services/supabaseEdgeFunctions';
import toast from 'react-hot-toast';

interface WalletState {
  balance: number;
  transactions: WalletTransaction[];
  loading: boolean;
  error: string | null;
  fetchBalance: (userId: string) => Promise<void>;
  fetchTransactions: (userId: string) => Promise<void>;
  refreshData: (userId: string) => Promise<void>;
}

export const useWalletStore = create<WalletState>((set, get) => ({
  balance: 0,
  transactions: [],
  loading: false,
  error: null,

  fetchBalance: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      // Use Supabase database function for comprehensive wallet info
      const walletInfo = await SupabaseDatabaseFunctions.getUserWalletInfo(userId);

      set({
        balance: walletInfo?.current_balance || 0,
        loading: false
      });
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      toast.error('Failed to fetch wallet balance');
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch balance',
        loading: false
      });
    }
  },

  fetchTransactions: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      set({ transactions: data || [], loading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch transactions',
        loading: false
      });
    }
  },

  refreshData: async (userId: string) => {
    const { fetchBalance, fetchTransactions } = get();
    await Promise.all([
      fetchBalance(userId),
      fetchTransactions(userId)
    ]);
  },
}));