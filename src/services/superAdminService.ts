import { supabase } from '../lib/supabase';

export interface AdminPermissions {
  users?: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    manage_roles?: boolean;
  };
  products?: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    manage_inventory?: boolean;
  };
  orders?: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    refund?: boolean;
  };
  wallet?: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    transfer?: boolean;
    adjust_balance?: boolean;
  };
  referrals?: {
    create?: boolean;
    read?: boolean;
    update?: boolean;
    delete?: boolean;
    manage_bonuses?: boolean;
  };
  kyc?: {
    approve?: boolean;
    reject?: boolean;
    review?: boolean;
    delete?: boolean;
  };
  system?: {
    database_access?: boolean;
    backup?: boolean;
    restore?: boolean;
    settings?: boolean;
  };
  analytics?: {
    view_all?: boolean;
    export?: boolean;
    financial_reports?: boolean;
  };
  notifications?: {
    send?: boolean;
    broadcast?: boolean;
    manage_templates?: boolean;
  };
}

export interface AdminRole {
  id: string;
  role_name: string;
  role_description: string;
  permissions: AdminPermissions;
  is_active: boolean;
}

export interface UserRole {
  user_id: string;
  role_id: string;
  role_name: string;
  permissions: AdminPermissions;
  assigned_at: string;
  expires_at?: string;
  is_active: boolean;
}

class SuperAdminService {
  // Check if user has super admin access
  async isSuperAdmin(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          role_id,
          admin_roles!inner(role_name)
        `)
        .eq('user_id', userId)
        .eq('is_active', true)
        .eq('admin_roles.role_name', 'super_admin')
        .single();

      return !error && !!data;
    } catch (error) {
      console.error('Error checking super admin status:', error);
      return false;
    }
  }

  // Get user's admin permissions
  async getUserPermissions(userId: string): Promise<AdminPermissions> {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          admin_roles!inner(permissions)
        `)
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error || !data || data.length === 0) {
        return {};
      }

      // Merge all role permissions
      const mergedPermissions: AdminPermissions = {};
      data.forEach(userRole => {
        const rolePermissions = userRole.admin_roles.permissions as AdminPermissions;
        this.mergePermissions(mergedPermissions, rolePermissions);
      });

      return mergedPermissions;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return {};
    }
  }

  // Check specific permission
  async hasPermission(userId: string, module: string, action: string): Promise<boolean> {
    try {
      // First check if user is super admin (has all permissions)
      const isSuperAdmin = await this.isSuperAdmin(userId);
      if (isSuperAdmin) {
        return true;
      }

      const permissions = await this.getUserPermissions(userId);
      const modulePermissions = permissions[module as keyof AdminPermissions];
      
      if (!modulePermissions) {
        return false;
      }

      return modulePermissions[action as keyof typeof modulePermissions] === true;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  // Get all available roles
  async getAllRoles(): Promise<AdminRole[]> {
    try {
      const { data, error } = await supabase
        .from('admin_roles')
        .select('*')
        .eq('is_active', true)
        .order('role_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting roles:', error);
      return [];
    }
  }

  // Assign role to user
  async assignRole(userId: string, roleId: string, assignedBy: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role_id: roleId,
          assigned_by: assignedBy
        });

      return !error;
    } catch (error) {
      console.error('Error assigning role:', error);
      return false;
    }
  }

  // Remove role from user
  async removeRole(userId: string, roleId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_roles')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('role_id', roleId);

      return !error;
    } catch (error) {
      console.error('Error removing role:', error);
      return false;
    }
  }

  // Get user's roles
  async getUserRoles(userId: string): Promise<UserRole[]> {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          user_id,
          role_id,
          assigned_at,
          expires_at,
          is_active,
          admin_roles!inner(
            role_name,
            permissions
          )
        `)
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) throw error;

      return data?.map(item => ({
        user_id: item.user_id,
        role_id: item.role_id,
        role_name: item.admin_roles.role_name,
        permissions: item.admin_roles.permissions as AdminPermissions,
        assigned_at: item.assigned_at,
        expires_at: item.expires_at,
        is_active: item.is_active
      })) || [];
    } catch (error) {
      console.error('Error getting user roles:', error);
      return [];
    }
  }

  // Super admin functions - Direct database access
  async executeSuperAdminQuery(query: string): Promise<any> {
    try {
      const { data, error } = await supabase.rpc('execute_super_admin_query', {
        query_text: query
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error executing super admin query:', error);
      throw error;
    }
  }

  // Create new admin role
  async createRole(roleName: string, description: string, permissions: AdminPermissions): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('admin_roles')
        .insert({
          role_name: roleName,
          role_description: description,
          permissions: permissions
        });

      return !error;
    } catch (error) {
      console.error('Error creating role:', error);
      return false;
    }
  }

  // Update role permissions
  async updateRole(roleId: string, permissions: AdminPermissions): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('admin_roles')
        .update({
          permissions: permissions,
          updated_at: new Date().toISOString()
        })
        .eq('id', roleId);

      return !error;
    } catch (error) {
      console.error('Error updating role:', error);
      return false;
    }
  }

  // Helper function to merge permissions
  private mergePermissions(target: AdminPermissions, source: AdminPermissions): void {
    Object.keys(source).forEach(module => {
      if (!target[module as keyof AdminPermissions]) {
        target[module as keyof AdminPermissions] = {};
      }
      
      const sourceModule = source[module as keyof AdminPermissions];
      const targetModule = target[module as keyof AdminPermissions];
      
      if (sourceModule && targetModule) {
        Object.keys(sourceModule).forEach(action => {
          if (sourceModule[action as keyof typeof sourceModule]) {
            (targetModule as any)[action] = true;
          }
        });
      }
    });
  }

  // Get super admin dashboard stats
  async getSuperAdminStats(): Promise<any> {
    try {
      const { data, error } = await supabase.rpc('get_super_admin_stats');
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting super admin stats:', error);
      return null;
    }
  }
}

export const superAdminService = new SuperAdminService();
