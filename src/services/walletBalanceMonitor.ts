import { supabase } from '../lib/supabase';

export interface WalletBalanceCheck {
  user_email: string;
  old_balance: number;
  calculated_balance: number;
  difference: number;
  updated: boolean;
}

export interface WalletSyncResult {
  success: boolean;
  wallets_fixed: number;
  check_time: string;
  message: string;
  details?: WalletBalanceCheck[];
}

class WalletBalanceMonitor {
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  // Start automatic wallet balance monitoring
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('Wallet balance monitoring already active');
      return;
    }

    console.log('Starting automatic wallet balance monitoring...');
    this.isMonitoring = true;

    // Run wallet sync check every 10 minutes
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.runWalletBalanceCheck();
      } catch (error) {
        console.error('Wallet balance monitoring failed:', error);
      }
    }, 10 * 60 * 1000); // 10 minutes

    // Run initial check with delay to avoid startup conflicts
    setTimeout(() => {
      this.runWalletBalanceCheck();
    }, 5000);
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('Wallet balance monitoring stopped');
  }

  // Run wallet balance consistency check
  async runWalletBalanceCheck(): Promise<WalletSyncResult> {
    try {
      console.log('Running wallet balance consistency check...');

      // Call the database function to sync all wallet balances
      const { data, error } = await supabase.rpc('scheduled_wallet_balance_check');

      if (error) {
        throw error;
      }

      const result = data as WalletSyncResult;
      
      if (result.wallets_fixed > 0) {
        console.warn(`⚠️ Fixed ${result.wallets_fixed} wallet balance mismatches`);
        
        // Get detailed results
        const { data: details } = await supabase.rpc('sync_all_wallet_balances');
        result.details = details?.filter((d: WalletBalanceCheck) => d.updated) || [];
      } else {
        console.log('✅ All wallet balances are in sync');
      }

      return result;
    } catch (error) {
      console.error('Error running wallet balance check:', error);
      return {
        success: false,
        wallets_fixed: 0,
        check_time: new Date().toISOString(),
        message: `Error: ${error.message}`
      };
    }
  }

  // Manual wallet balance sync for specific user
  async syncUserWalletBalance(userId: string): Promise<{ success: boolean; old_balance: number; new_balance: number }> {
    try {
      // Get current balance
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      const oldBalance = user.wallet_balance;

      // Calculate correct balance from transactions
      const { data: transactions, error: transError } = await supabase
        .from('wallet_transactions')
        .select('type, amount')
        .eq('user_id', userId);

      if (transError) throw transError;

      const newBalance = transactions?.reduce((sum, t) => {
        return sum + (t.type === 'credit' ? t.amount : -t.amount);
      }, 0) || 0;

      // Update if different
      if (oldBalance !== newBalance) {
        const { error: updateError } = await supabase
          .from('users')
          .update({ 
            wallet_balance: newBalance,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (updateError) throw updateError;

        console.log(`Updated wallet balance for user ${userId}: ${oldBalance} → ${newBalance}`);
      }

      return {
        success: true,
        old_balance: oldBalance,
        new_balance: newBalance
      };
    } catch (error) {
      console.error('Error syncing user wallet balance:', error);
      return {
        success: false,
        old_balance: 0,
        new_balance: 0
      };
    }
  }

  // Get wallet balance audit log
  async getWalletBalanceAuditLog(limit: number = 50): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('wallet_balance_audit')
        .select(`
          *,
          user:users(email, full_name)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error && error.code !== '42P01') { // Ignore if table doesn't exist
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching wallet balance audit log:', error);
      return [];
    }
  }

  // Check if monitoring is active
  isActive(): boolean {
    return this.isMonitoring;
  }
}

// Create singleton instance
export const walletBalanceMonitor = new WalletBalanceMonitor();

// Auto-start monitoring when module is imported
if (typeof window !== 'undefined') {
  // Only start in browser environment
  setTimeout(() => {
    walletBalanceMonitor.startMonitoring();
  }, 2000);
}
