import { supabase } from '../lib/supabase';
import { sendPasswordResetEmail } from './emailService';
import toast from 'react-hot-toast';

interface PasswordResetToken {
  id: string;
  user_id: string;
  email: string;
  token: string;
  expires_at: string;
  used: boolean;
  used_at?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  updated_at: string;
}

// Generate a secure random token
const generateResetToken = (): string => {
  // Generate a secure random token without external dependencies
  const timestamp = Date.now().toString(36);
  const randomPart1 = Math.random().toString(36).substr(2, 9);
  const randomPart2 = Math.random().toString(36).substr(2, 9);
  const randomPart3 = Math.random().toString(36).substr(2, 9);
  return `${timestamp}-${randomPart1}-${randomPart2}-${randomPart3}`;
};

// Get user's IP address (simplified for demo)
const getUserIP = async (): Promise<string | null> => {
  try {
    // In production, you might use a service to get the real IP
    return null; // Will be stored as null for now
  } catch (error) {
    return null;
  }
};

// Request password reset using Supabase's built-in functionality
export const requestPasswordReset = async (email: string): Promise<{ success: boolean; message: string }> => {
  try {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { success: false, message: 'Please enter a valid email address.' };
    }

    // Check for recent reset requests (rate limiting) - Changed to 1 minute
    const oneMinuteAgo = new Date(Date.now() - 1 * 60 * 1000).toISOString();
    const { data: recentRequests, error: recentError } = await supabase
      .from('password_reset_tokens')
      .select('id')
      .eq('email', email.toLowerCase())
      .gte('created_at', oneMinuteAgo)
      .limit(1);

    if (recentError) {
      console.error('Error checking recent requests:', recentError);
    } else if (recentRequests && recentRequests.length > 0) {
      return {
        success: false,
        message: 'A password reset email was already sent recently. Please check your email or wait 1 minute before requesting again.'
      };
    }

    // Use Supabase's built-in password reset with proper redirect URL
    const redirectTo = `${window.location.origin}/reset-password`;
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectTo
    });

    console.log('Password reset email sent with redirect URL:', redirectTo);

    if (error) {
      console.error('Error sending password reset email:', error);
      return { success: false, message: 'Failed to send password reset email. Please try again.' };
    }

    // Log the reset request for tracking
    try {
      const { data: user } = await supabase
        .from('users')
        .select('id, full_name')
        .eq('email', email.toLowerCase())
        .maybeSingle();

      if (user) {
        const token = generateResetToken();
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
        const userAgent = navigator.userAgent;
        const ipAddress = await getUserIP();

        await supabase
          .from('password_reset_tokens')
          .insert({
            user_id: user.id,
            email: email.toLowerCase(),
            token: token,
            expires_at: expiresAt.toISOString(),
            used: false,
            ip_address: ipAddress,
            user_agent: userAgent
          });
      }
    } catch (logError) {
      console.error('Error logging reset request:', logError);
      // Don't fail the main request if logging fails
    }

    return {
      success: true,
      message: 'If an account with this email exists, you will receive a password reset link shortly.'
    };

  } catch (error) {
    console.error('Error in requestPasswordReset:', error);
    return { success: false, message: 'An unexpected error occurred. Please try again.' };
  }
};

// Validate reset session (check if user came from password reset email)
export const validateResetToken = async (token: string): Promise<{ valid: boolean; user?: any; message: string }> => {
  try {
    // Check if user has an active session from password reset
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return { valid: false, message: 'An error occurred while validating the reset link.' };
    }

    if (!session) {
      return {
        valid: false,
        message: 'Invalid or expired reset link. Please click the password reset link in your email again.'
      };
    }

    // Check if this is a password recovery session
    if (!session.user.email) {
      return { valid: false, message: 'Invalid session. Please request a new password reset.' };
    }

    // Get user details from our database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name')
      .eq('email', session.user.email)
      .maybeSingle();

    if (userError && userError.code !== 'PGRST116') {
      console.error('Error getting user:', userError);
      return { valid: false, message: 'An error occurred while validating the user.' };
    }

    if (!user) {
      return { valid: false, message: 'User not found. Please contact support.' };
    }

    return {
      valid: true,
      user: user,
      message: 'Reset session is valid.'
    };

  } catch (error) {
    console.error('Error in validateResetToken:', error);
    return { valid: false, message: 'An unexpected error occurred.' };
  }
};

// Reset password using Supabase session (called from reset page with URL params)
export const resetPassword = async (token: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
  try {
    // Validate password strength
    if (newPassword.length < 8) {
      return { success: false, message: 'Password must be at least 8 characters long.' };
    }

    // Check if user has an active session (from password reset link)
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return {
        success: false,
        message: 'Invalid session. Please click the password reset link in your email again.'
      };
    }

    // Update the user's password
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword
    });

    if (updateError) {
      console.error('Error updating password:', updateError);
      return {
        success: false,
        message: 'Failed to update password. Please try again or request a new reset link.'
      };
    }

    // Mark any existing tokens as used for this user
    try {
      await supabase
        .from('password_reset_tokens')
        .update({
          used: true,
          used_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', session.user.id)
        .eq('used', false);
    } catch (tokenError) {
      console.error('Error marking tokens as used:', tokenError);
      // Don't fail the password reset if this fails
    }

    // Sign out the user so they need to log in with new password
    await supabase.auth.signOut();

    return {
      success: true,
      message: 'Your password has been successfully reset. You can now log in with your new password.'
    };

  } catch (error) {
    console.error('Error in resetPassword:', error);
    return { success: false, message: 'An unexpected error occurred. Please try again.' };
  }
};

// Clean up expired tokens (utility function)
export const cleanupExpiredTokens = async (): Promise<void> => {
  try {
    const now = new Date().toISOString();
    await supabase
      .from('password_reset_tokens')
      .delete()
      .lt('expires_at', now);
  } catch (error) {
    console.error('Error cleaning up expired tokens:', error);
  }
};

// Get reset token history for admin (optional)
export const getResetTokenHistory = async (userId: string): Promise<PasswordResetToken[]> => {
  try {
    const { data, error } = await supabase
      .from('password_reset_tokens')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error getting reset token history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getResetTokenHistory:', error);
    return [];
  }
};
