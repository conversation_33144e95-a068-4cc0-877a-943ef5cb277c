import { supabase } from '../lib/supabase';

export interface AdminNotification {
  id: string;
  type: 'order' | 'user' | 'kyc' | 'product' | 'wallet' | 'alert' | 'referral';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface NotificationData {
  type: AdminNotification['type'];
  title: string;
  message: string;
  data?: any;
  priority?: AdminNotification['priority'];
}

class AdminNotificationService {
  private notifications: AdminNotification[] = [];
  private listeners: ((notifications: AdminNotification[]) => void)[] = [];

  constructor() {
    this.initializeRealTimeSubscriptions();
  }

  // Initialize real-time subscriptions for various events
  private initializeRealTimeSubscriptions() {
    // Subscribe to new orders
    supabase
      .channel('admin-orders')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'orders' },
        (payload) => this.handleNewOrder(payload.new)
      )
      .subscribe();

    // Subscribe to new users
    supabase
      .channel('admin-users')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'users' },
        (payload) => this.handleNewUser(payload.new)
      )
      .subscribe();

    // Subscribe to KYC submissions
    supabase
      .channel('admin-kyc')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'users', filter: 'kyc_status=eq.under_review' },
        (payload) => this.handleKYCSubmission(payload.new)
      )
      .subscribe();

    // Subscribe to wallet transactions
    supabase
      .channel('admin-wallet')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'wallet_transactions' },
        (payload) => this.handleWalletTransaction(payload.new)
      )
      .subscribe();

    // Subscribe to product stock changes
    supabase
      .channel('admin-products')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'products', filter: 'stock_quantity=lte.5' },
        (payload) => this.handleLowStock(payload.new)
      )
      .subscribe();

    // Subscribe to referrals
    supabase
      .channel('admin-referrals')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'referrals' },
        (payload) => this.handleNewReferral(payload.new)
      )
      .subscribe();
  }

  // Event handlers
  private handleNewOrder(order: any) {
    this.addNotification({
      type: 'order',
      title: 'New Order Received',
      message: `Order #${order.id} for ₹${order.total_amount} from ${order.customer_email}`,
      data: { orderId: order.id, amount: order.total_amount },
      priority: 'medium'
    });
  }

  private handleNewUser(user: any) {
    this.addNotification({
      type: 'user',
      title: 'New User Registration',
      message: `${user.full_name || user.email} just registered`,
      data: { userId: user.id, email: user.email },
      priority: 'low'
    });
  }

  private handleKYCSubmission(user: any) {
    this.addNotification({
      type: 'kyc',
      title: 'KYC Submission',
      message: `${user.full_name || user.email} submitted KYC documents for review`,
      data: { userId: user.id, email: user.email },
      priority: 'high'
    });
  }

  private handleWalletTransaction(transaction: any) {
    if (transaction.amount > 1000) { // Only notify for large transactions
      this.addNotification({
        type: 'wallet',
        title: 'Large Wallet Transaction',
        message: `₹${transaction.amount} ${transaction.type} transaction`,
        data: { transactionId: transaction.id, amount: transaction.amount },
        priority: 'medium'
      });
    }
  }

  private handleLowStock(product: any) {
    this.addNotification({
      type: 'alert',
      title: 'Low Stock Alert',
      message: `${product.name} is running low (${product.stock_quantity} left)`,
      data: { productId: product.id, stock: product.stock_quantity },
      priority: 'urgent'
    });
  }

  private handleNewReferral(referral: any) {
    this.addNotification({
      type: 'referral',
      title: 'New Referral',
      message: `New referral registered in the system`,
      data: { referralId: referral.id },
      priority: 'low'
    });
  }

  // Add notification
  private addNotification(data: NotificationData) {
    const notification: AdminNotification = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: data.type,
      title: data.title,
      message: data.message,
      data: data.data,
      read: false,
      created_at: new Date().toISOString(),
      priority: data.priority || 'medium'
    };

    this.notifications.unshift(notification);
    
    // Keep only last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }

    this.notifyListeners();
  }

  // Public methods
  public getNotifications(): AdminNotification[] {
    return this.notifications;
  }

  public getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  public markAsRead(notificationId: string) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.notifyListeners();
    }
  }

  public markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
    this.notifyListeners();
  }

  public clearNotification(notificationId: string) {
    this.notifications = this.notifications.filter(n => n.id !== notificationId);
    this.notifyListeners();
  }

  public clearAllNotifications() {
    this.notifications = [];
    this.notifyListeners();
  }

  // Subscribe to notification changes
  public subscribe(callback: (notifications: AdminNotification[]) => void) {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.notifications));
  }

  // Manual notification creation (for testing or custom events)
  public createNotification(data: NotificationData) {
    this.addNotification(data);
  }

  // Get notifications by type
  public getNotificationsByType(type: AdminNotification['type']): AdminNotification[] {
    return this.notifications.filter(n => n.type === type);
  }

  // Get notifications by priority
  public getNotificationsByPriority(priority: AdminNotification['priority']): AdminNotification[] {
    return this.notifications.filter(n => n.priority === priority);
  }

  // Initialize with some sample notifications for demo
  public initializeSampleNotifications() {
    const sampleNotifications: NotificationData[] = [
      {
        type: 'order',
        title: 'New Order Received',
        message: 'Order #1001 for ₹2,500 from <EMAIL>',
        priority: 'medium',
        data: { orderId: '1001', amount: 2500 }
      },
      {
        type: 'user',
        title: 'New User Registration',
        message: 'John Doe just registered',
        priority: 'low',
        data: { userId: 'user123', email: '<EMAIL>' }
      },
      {
        type: 'kyc',
        title: 'KYC Submission',
        message: 'Sarah Smith submitted KYC documents for review',
        priority: 'high',
        data: { userId: 'user456', email: '<EMAIL>' }
      },
      {
        type: 'alert',
        title: 'Low Stock Alert',
        message: 'Green Tea Extract is running low (3 left)',
        priority: 'urgent',
        data: { productId: 'prod789', stock: 3 }
      },
      {
        type: 'wallet',
        title: 'Large Wallet Transaction',
        message: '₹5,000 credit transaction',
        priority: 'medium',
        data: { transactionId: 'tx123', amount: 5000 }
      }
    ];

    sampleNotifications.forEach(notification => {
      this.addNotification(notification);
    });
  }
}

// Create singleton instance
export const adminNotificationService = new AdminNotificationService();

// Initialize sample notifications for demo
if (import.meta.env.DEV) {
  adminNotificationService.initializeSampleNotifications();
}
