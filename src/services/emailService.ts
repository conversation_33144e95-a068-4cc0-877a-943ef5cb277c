// Email service for sending OTP emails
// This is a simple implementation using EmailJS for demo purposes
// In production, you would use a proper email service like SendGrid, AWS SES, etc.

interface EmailConfig {
  serviceId: string;
  templateId: string;
  publicKey: string;
}

// Email configuration (replace with your EmailJS credentials)
const EMAIL_CONFIG: EmailConfig = {
  serviceId: 'service_startjuicce', // Replace with your EmailJS service ID
  templateId: 'template_otp', // Replace with your EmailJS template ID
  publicKey: 'your_public_key' // Replace with your EmailJS public key
};

// OTP email template
const OTP_EMAIL_TEMPLATE = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Juicce - OTP Verification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10B981, #059669); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .otp-box { background: white; border: 2px solid #10B981; border-radius: 10px; padding: 20px; text-align: center; margin: 20px 0; }
        .otp-code { font-size: 32px; font-weight: bold; color: #10B981; letter-spacing: 5px; margin: 10px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .warning { background: #FEF3C7; border: 1px solid #F59E0B; border-radius: 5px; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌿 Start Juicce</h1>
            <p>Verify Your Account</p>
        </div>
        <div class="content">
            <h2>Hello!</h2>
            <p>Thank you for joining Start Juicce! To complete your registration, please verify your email address using the OTP code below:</p>
            
            <div class="otp-box">
                <p>Your OTP Code:</p>
                <div class="otp-code">{{OTP_CODE}}</div>
                <p><small>Valid for 10 minutes</small></p>
            </div>
            
            <div class="warning">
                <strong>⚠️ Security Notice:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Never share this OTP with anyone</li>
                    <li>Start Juicce will never ask for your OTP via phone or email</li>
                    <li>This OTP expires in 10 minutes</li>
                </ul>
            </div>
            
            <p>If you didn't request this verification, please ignore this email or contact our support team.</p>
            
            <p>Welcome to the Start Juicce family!</p>
            <p><strong>The Start Juicce Team</strong></p>
        </div>
        <div class="footer">
            <p>© 2024 Start Juicce. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
`;

// Password reset email template
const PASSWORD_RESET_EMAIL_TEMPLATE = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Juicce - Password Reset</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #DC2626, #B91C1C); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .reset-button { background: #DC2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; margin: 20px 0; }
        .reset-button:hover { background: #B91C1C; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .warning { background: #FEE2E2; border: 1px solid #EF4444; border-radius: 5px; padding: 15px; margin: 20px 0; }
        .link-box { background: #F8F9FA; border: 1px solid #DEE2E6; border-radius: 4px; padding: 15px; word-break: break-all; font-family: monospace; font-size: 12px; color: #495057; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Start Juicce</h1>
            <p>Password Reset Request</p>
        </div>
        <div class="content">
            <h2>Hello {{USER_NAME}}!</h2>
            <p>We received a request to reset your password for your Start Juicce account. If you made this request, please click the button below to reset your password:</p>

            <div style="text-align: center;">
                <a href="{{RESET_LINK}}" class="reset-button">Reset My Password</a>
            </div>

            <p>Or copy and paste this link into your browser:</p>
            <div class="link-box">{{RESET_LINK}}</div>

            <div class="warning">
                <strong>🔒 Security Information:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>This link will expire in <strong>1 hour</strong></li>
                    <li>This link can only be used once</li>
                    <li>If you didn't request this reset, please ignore this email</li>
                    <li>Your current password remains unchanged until you complete the reset</li>
                </ul>
            </div>

            <p>If you're having trouble with the button above, copy and paste the URL into your web browser.</p>

            <p>If you didn't request this password reset, please ignore this email or contact our support team if you have concerns.</p>

            <p><strong>The Start Juicce Team</strong></p>
        </div>
        <div class="footer">
            <p>© 2024 Start Juicce. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
`;

// Send OTP email using a simple fetch request (for demo)
export const sendOTPEmail = async (email: string, otp: string): Promise<boolean> => {
  try {
    // For demo purposes, we'll simulate sending an email
    // In production, you would integrate with a real email service
    
    console.log('Sending OTP email to:', email);
    console.log('OTP Code:', otp);
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real implementation, you would do something like:
    /*
    const response = await fetch('https://api.emailservice.com/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
      },
      body: JSON.stringify({
        to: email,
        subject: 'Start Juicce - OTP Verification',
        html: OTP_EMAIL_TEMPLATE.replace('{{OTP_CODE}}', otp),
        from: '<EMAIL>'
      })
    });
    
    return response.ok;
    */
    
    // For demo, always return true
    return true;
  } catch (error) {
    console.error('Error sending OTP email:', error);
    return false;
  }
};

// Send password reset email
export const sendPasswordResetEmail = async (email: string, resetLink: string, userName: string): Promise<boolean> => {
  try {
    console.log('Sending password reset email to:', email);
    console.log('Reset link:', resetLink);
    console.log('User name:', userName);

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In a real implementation, you would do something like:
    /*
    const emailContent = PASSWORD_RESET_EMAIL_TEMPLATE
      .replace(/{{RESET_LINK}}/g, resetLink)
      .replace('{{USER_NAME}}', userName);

    const response = await fetch('https://api.emailservice.com/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
      },
      body: JSON.stringify({
        to: email,
        subject: '🔐 Start Juicce - Password Reset Request',
        html: emailContent,
        from: '<EMAIL>'
      })
    });

    return response.ok;
    */

    // For demo, always return true
    console.log('✅ Password reset email sent successfully (demo mode)');
    return true;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return false;
  }
};

// Alternative: Using EmailJS (client-side email service)
export const sendOTPEmailJS = async (email: string, otp: string): Promise<boolean> => {
  try {
    // This would require EmailJS library to be installed
    // npm install @emailjs/browser
    
    /*
    import emailjs from '@emailjs/browser';
    
    const templateParams = {
      to_email: email,
      otp_code: otp,
      to_name: email.split('@')[0],
    };
    
    const response = await emailjs.send(
      EMAIL_CONFIG.serviceId,
      EMAIL_CONFIG.templateId,
      templateParams,
      EMAIL_CONFIG.publicKey
    );
    
    return response.status === 200;
    */
    
    console.log('EmailJS would send OTP to:', email, 'with code:', otp);
    return true;
  } catch (error) {
    console.error('Error sending email via EmailJS:', error);
    return false;
  }
};

// Send OTP via Supabase Edge Functions (recommended for production)
export const sendOTPSupabase = async (email: string, otp: string): Promise<boolean> => {
  try {
    // This would call a Supabase Edge Function that handles email sending
    /*
    const { data, error } = await supabase.functions.invoke('send-otp-email', {
      body: {
        email,
        otp,
        template: 'verification'
      }
    });
    
    return !error && data?.success;
    */
    
    console.log('Supabase Edge Function would send OTP to:', email, 'with code:', otp);
    return true;
  } catch (error) {
    console.error('Error sending email via Supabase:', error);
    return false;
  }
};

// Email validation utility
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Generate email-friendly OTP (avoiding confusing characters)
export const generateEmailOTP = (): string => {
  // Use only digits for simplicity
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Email rate limiting (simple in-memory implementation)
const emailRateLimit = new Map<string, { count: number; resetTime: number }>();

export const checkEmailRateLimit = (email: string, maxAttempts: number = 3, windowMs: number = 300000): boolean => {
  const now = Date.now();
  const key = email.toLowerCase();
  
  const current = emailRateLimit.get(key);
  
  if (!current || now > current.resetTime) {
    emailRateLimit.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxAttempts) {
    return false;
  }
  
  current.count++;
  return true;
};

// Clean up expired rate limit entries
export const cleanupEmailRateLimit = (): void => {
  const now = Date.now();
  for (const [key, value] of emailRateLimit.entries()) {
    if (now > value.resetTime) {
      emailRateLimit.delete(key);
    }
  }
};

// Setup for production email service integration
export const setupEmailService = () => {
  // Initialize your email service here
  console.log('Email service initialized for Start Juicce');
  
  // Clean up rate limit entries every 5 minutes
  setInterval(cleanupEmailRateLimit, 5 * 60 * 1000);
};
