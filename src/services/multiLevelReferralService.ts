import { supabase } from '../lib/supabase';

// Multi-level referral bonus structure (INR amounts)
export const REFERRAL_LEVEL_BONUSES = {
  1: 250.00,  // Direct referrer
  2: 100.00,
  3: 50.00,
  4: 25.00,
  5: 10.00,
  6: 5.00,
  7: 2.00,
  8: 1.00,
  9: 0.50,
  10: 0.25
};

export const MAX_REFERRAL_LEVELS = 10;
export const TOTAL_POSSIBLE_DISTRIBUTION = 443.75;

export interface ReferralChainUser {
  id: string;
  email: string;
  full_name: string;
  referral_code: string;
  referred_by: string | null;
  is_premium: boolean;
  wallet_balance: number;
  level: number;
}

export interface ReferralProcessingResult {
  success: boolean;
  totalBonusDistributed: number;
  levelsProcessed: number;
  chainUsers: ReferralChainUser[];
  errors: string[];
  warnings: string[];
  referralId?: string;
  processingTime: number;
  auditLogId?: string;
}

export interface BonusDistribution {
  userId: string;
  level: number;
  amount: number;
  userName: string;
  userEmail: string;
}

export class MultiLevelReferralService {
  
  /**
   * Process multi-level referral bonuses when a user becomes premium
   * This is the main entry point for the multi-level referral system
   */
  async processMultiLevelReferral(
    newPremiumUserId: string,
    newPremiumUserEmail: string,
    referralCode: string
  ): Promise<ReferralProcessingResult> {
    const startTime = Date.now();
    const result: ReferralProcessingResult = {
      success: false,
      totalBonusDistributed: 0,
      levelsProcessed: 0,
      chainUsers: [],
      errors: [],
      warnings: [],
      processingTime: 0
    };

    // Check if user is authenticated before processing
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      result.errors.push('User not authenticated');
      result.processingTime = Date.now() - startTime;
      return result;
    }

    // Start audit logging
    const auditLogId = await this.startAuditLog(newPremiumUserId, newPremiumUserEmail, referralCode);
    result.auditLogId = auditLogId;

    try {
      // Input validation
      if (!newPremiumUserId || !newPremiumUserEmail || !referralCode) {
        result.errors.push('Missing required parameters');
        await this.logAuditError(auditLogId, 'VALIDATION_ERROR', 'Missing required parameters');
        return result;
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newPremiumUserEmail)) {
        result.errors.push('Invalid email format');
        await this.logAuditError(auditLogId, 'VALIDATION_ERROR', 'Invalid email format');
        return result;
      }

      // Referral code format validation
      if (referralCode.length < 6 || referralCode.length > 20) {
        result.errors.push('Invalid referral code format');
        await this.logAuditError(auditLogId, 'VALIDATION_ERROR', 'Invalid referral code format');
        return result;
      }

      // 1. Validate that the new user is premium
      const { data: newUser, error: newUserError } = await supabase
        .from('users')
        .select('id, email, full_name, is_premium, referred_by')
        .eq('id', newPremiumUserId)
        .single();

      if (newUserError || !newUser) {
        result.errors.push('New premium user not found');
        await this.logAuditError(auditLogId, 'USER_NOT_FOUND', `User ${newPremiumUserId} not found: ${newUserError?.message}`);
        return result;
      }

      if (!newUser.is_premium) {
        result.errors.push('User is not premium - no bonuses will be distributed');
        await this.logAuditError(auditLogId, 'PREMIUM_VALIDATION_FAILED', `User ${newPremiumUserId} is not premium`);
        return result;
      }

      // Check for duplicate processing
      const { data: existingReferral } = await supabase
        .from('referrals')
        .select('id, status')
        .eq('referred_user_id', newPremiumUserId)
        .single();

      if (existingReferral && existingReferral.status === 'completed') {
        result.errors.push('Referral already processed for this user');
        result.warnings.push('Duplicate processing attempt detected');
        await this.logAuditError(auditLogId, 'DUPLICATE_PROCESSING', `Referral already exists for user ${newPremiumUserId}`);
        return result;
      }

      // 2. Validate referral code and find direct referrer
      const { data: directReferrer, error: referrerError } = await supabase
        .from('users')
        .select('id, email, full_name, referral_code, referred_by, is_premium, wallet_balance')
        .eq('referral_code', referralCode)
        .single();

      if (referrerError || !directReferrer) {
        result.errors.push('Invalid referral code - direct referrer not found');
        await this.logAuditError(auditLogId, 'REFERRER_NOT_FOUND', `Referrer with code ${referralCode} not found: ${referrerError?.message}`);
        return result;
      }

      if (!directReferrer.is_premium) {
        result.errors.push('Direct referrer is not premium - no bonuses will be distributed');
        await this.logAuditError(auditLogId, 'REFERRER_NOT_PREMIUM', `Referrer ${directReferrer.id} is not premium`);
        return result;
      }

      // Check for self-referral
      if (directReferrer.id === newPremiumUserId) {
        result.errors.push('Self-referral is not allowed');
        await this.logAuditError(auditLogId, 'SELF_REFERRAL', `User ${newPremiumUserId} attempted self-referral`);
        return result;
      }

      // 3. Build the referral chain
      const chain = await this.buildReferralChain(directReferrer);
      result.chainUsers = chain;

      // 4. Get configurable bonus amounts
      const bonusAmounts = await this.getConfigurableBonusAmounts();

      // 5. Distribute bonuses to each level with enhanced error handling
      const distributions = await this.distributeBonusesWithErrorHandling(chain, bonusAmounts, newPremiumUserId, newPremiumUserEmail, auditLogId);
      
      // 6. Create referral record
      const referralId = await this.createReferralRecord(
        directReferrer.id,
        newPremiumUserId,
        bonusAmounts[1] || REFERRAL_LEVEL_BONUSES[1]
      );
      result.referralId = referralId;

      // 7. Log the complete transaction
      await this.logMultiLevelReferralActivity(newPremiumUserId, distributions, chain);

      result.success = true;
      result.totalBonusDistributed = distributions.reduce((sum, d) => sum + d.amount, 0);
      result.levelsProcessed = distributions.length;
      result.processingTime = Date.now() - startTime;

      // Log successful completion
      await this.logAuditSuccess(auditLogId, {
        totalBonusDistributed: result.totalBonusDistributed,
        levelsProcessed: result.levelsProcessed,
        processingTime: result.processingTime,
        chainLength: chain.length,
        distributions: distributions
      });

      return result;

    } catch (error) {
      console.error('Multi-level referral processing error:', error);
      result.errors.push(`Processing failed: ${error.message}`);
      result.processingTime = Date.now() - startTime;

      // Log critical error
      await this.logAuditError(auditLogId, 'CRITICAL_ERROR', `Processing failed: ${error.message}`, {
        stack: error.stack,
        processingTime: result.processingTime
      });

      return result;
    }
  }

  /**
   * Build the referral chain by traversing up to 10 levels
   * Implements circular reference detection and premium validation
   */
  private async buildReferralChain(startUser: any): Promise<ReferralChainUser[]> {
    const chain: ReferralChainUser[] = [];
    const visitedUserIds = new Set<string>();
    let currentUser = startUser;
    let level = 1;

    while (currentUser && level <= MAX_REFERRAL_LEVELS) {
      // Circular reference detection
      if (visitedUserIds.has(currentUser.id)) {
        console.warn(`Circular reference detected at level ${level}, stopping chain traversal`);
        break;
      }

      visitedUserIds.add(currentUser.id);

      // Add current user to chain if premium
      if (currentUser.is_premium) {
        chain.push({
          id: currentUser.id,
          email: currentUser.email,
          full_name: currentUser.full_name,
          referral_code: currentUser.referral_code,
          referred_by: currentUser.referred_by,
          is_premium: currentUser.is_premium,
          wallet_balance: currentUser.wallet_balance,
          level: level
        });
      } else {
        console.log(`User at level ${level} is not premium, stopping chain traversal`);
        break;
      }

      // Move to next level if user has a referrer
      if (!currentUser.referred_by) {
        break;
      }

      // Find the next user in the chain
      const { data: nextUser, error } = await supabase
        .from('users')
        .select('id, email, full_name, referral_code, referred_by, is_premium, wallet_balance')
        .eq('referral_code', currentUser.referred_by)
        .single();

      if (error || !nextUser) {
        console.log(`No referrer found for level ${level + 1}, stopping chain traversal`);
        break;
      }

      currentUser = nextUser;
      level++;
    }

    return chain;
  }

  /**
   * Get configurable bonus amounts from admin settings
   */
  private async getConfigurableBonusAmounts(): Promise<{ [level: number]: number }> {
    try {
      const { data: settings, error } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value')
        .like('setting_key', 'referral_level_%_bonus');

      if (error) {
        console.warn('Failed to load configurable bonus amounts, using defaults:', error);
        return REFERRAL_LEVEL_BONUSES;
      }

      const configurableBonuses: { [level: number]: number } = {};

      settings?.forEach(setting => {
        const match = setting.setting_key.match(/referral_level_(\d+)_bonus/);
        if (match) {
          const level = parseInt(match[1]);
          const amount = parseFloat(setting.setting_value);
          if (!isNaN(level) && !isNaN(amount) && level >= 1 && level <= MAX_REFERRAL_LEVELS) {
            configurableBonuses[level] = amount;
          }
        }
      });

      // Fill in missing levels with defaults
      for (let level = 1; level <= MAX_REFERRAL_LEVELS; level++) {
        if (!configurableBonuses[level]) {
          configurableBonuses[level] = REFERRAL_LEVEL_BONUSES[level];
        }
      }

      return configurableBonuses;
    } catch (error) {
      console.warn('Error loading configurable bonus amounts, using defaults:', error);
      return REFERRAL_LEVEL_BONUSES;
    }
  }

  /**
   * Distribute bonuses to all users in the referral chain
   */
  private async distributeBonuses(
    chain: ReferralChainUser[],
    bonusAmounts: { [level: number]: number },
    newPremiumUserId: string,
    newPremiumUserEmail: string
  ): Promise<BonusDistribution[]> {
    const distributions: BonusDistribution[] = [];

    for (const user of chain) {
      const bonusAmount = bonusAmounts[user.level];

      if (!bonusAmount || bonusAmount <= 0) {
        continue;
      }

      try {
        // Update user's wallet balance
        const { error: updateError } = await supabase
          .from('users')
          .update({
            wallet_balance: supabase.sql`wallet_balance + ${bonusAmount}`,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        if (updateError) {
          console.error(`Failed to update wallet for user ${user.id} at level ${user.level}:`, updateError);
          continue;
        }

        // Create wallet transaction record
        const { error: transactionError } = await supabase
          .from('wallet_transactions')
          .insert({
            user_id: user.id,
            type: 'credit',
            amount: bonusAmount,
            description: `Level ${user.level} referral bonus for ${newPremiumUserEmail} becoming premium`,
            reference_type: 'multi_level_referral',
            reference_id: newPremiumUserId,
            metadata: {
              referral_level: user.level,
              referred_user_id: newPremiumUserId,
              referred_user_email: newPremiumUserEmail,
              bonus_type: 'multi_level_referral'
            }
          });

        if (transactionError) {
          console.error(`Failed to create transaction for user ${user.id} at level ${user.level}:`, transactionError);
          // Continue processing other levels even if one fails
        }

        distributions.push({
          userId: user.id,
          level: user.level,
          amount: bonusAmount,
          userName: user.full_name,
          userEmail: user.email
        });

        console.log(`Level ${user.level} bonus of ₹${bonusAmount} credited to ${user.email}`);

      } catch (error) {
        console.error(`Error distributing bonus to user ${user.id} at level ${user.level}:`, error);
        // Continue with other levels
      }
    }

    return distributions;
  }

  /**
   * Create a referral record for the direct referral relationship
   */
  private async createReferralRecord(
    referrerId: string,
    referredUserId: string,
    bonusAmount: number
  ): Promise<string | null> {
    try {
      const { data: referral, error } = await supabase
        .from('referrals')
        .insert({
          referrer_id: referrerId,
          referred_user_id: referredUserId,
          bonus_amount: bonusAmount,
          status: 'completed',
          created_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        console.error('Failed to create referral record:', error);
        return null;
      }

      return referral.id;
    } catch (error) {
      console.error('Error creating referral record:', error);
      return null;
    }
  }

  /**
   * Log multi-level referral activity for audit trail
   */
  private async logMultiLevelReferralActivity(
    newPremiumUserId: string,
    distributions: BonusDistribution[],
    chain: ReferralChainUser[]
  ): Promise<void> {
    try {
      const activityData = {
        new_premium_user_id: newPremiumUserId,
        total_levels_processed: distributions.length,
        total_bonus_distributed: distributions.reduce((sum, d) => sum + d.amount, 0),
        distributions: distributions,
        referral_chain: chain.map(user => ({
          user_id: user.id,
          level: user.level,
          email: user.email,
          full_name: user.full_name
        }))
      };

      await supabase
        .from('referral_audit_log')
        .insert({
          user_id: newPremiumUserId,
          action: 'multi_level_referral_processed',
          details: activityData,
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('Error logging multi-level referral activity:', error);
      // Don't throw error as this is just logging
    }
  }

  /**
   * Check if multi-level referral system is enabled
   */
  async isMultiLevelReferralEnabled(): Promise<boolean> {
    try {
      const { data: setting, error } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'multi_level_referral_enabled')
        .single();

      if (error || !setting) {
        return true; // Default to enabled
      }

      return setting.setting_value === 'true';
    } catch (error) {
      console.warn('Error checking multi-level referral status, defaulting to enabled:', error);
      return true;
    }
  }

  /**
   * Get maximum referral levels from configuration
   */
  async getMaxReferralLevels(): Promise<number> {
    try {
      const { data: setting, error } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'max_referral_levels')
        .single();

      if (error || !setting) {
        return MAX_REFERRAL_LEVELS; // Default to 10
      }

      const maxLevels = parseInt(setting.setting_value);
      return isNaN(maxLevels) ? MAX_REFERRAL_LEVELS : Math.min(maxLevels, 20); // Cap at 20 for safety
    } catch (error) {
      console.warn('Error getting max referral levels, using default:', error);
      return MAX_REFERRAL_LEVELS;
    }
  }

  /**
   * Start audit logging for referral processing
   */
  private async startAuditLog(
    newPremiumUserId: string,
    newPremiumUserEmail: string,
    referralCode: string
  ): Promise<string | null> {
    try {
      const { data: auditLog, error } = await supabase
        .from('referral_audit_log')
        .insert({
          user_id: newPremiumUserId,
          action: 'multi_level_referral_started',
          details: {
            new_premium_user_id: newPremiumUserId,
            new_premium_user_email: newPremiumUserEmail,
            referral_code: referralCode,
            started_at: new Date().toISOString(),
            status: 'processing'
          },
          created_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        console.error('Failed to start audit log:', error);
        return null;
      }

      return auditLog.id;
    } catch (error) {
      console.error('Error starting audit log:', error);
      return null;
    }
  }

  /**
   * Log audit error
   */
  private async logAuditError(
    auditLogId: string | null,
    errorType: string,
    errorMessage: string,
    additionalData?: any
  ): Promise<void> {
    if (!auditLogId) return;

    try {
      await supabase
        .from('referral_audit_log')
        .insert({
          user_id: null, // System error
          action: 'multi_level_referral_error',
          details: {
            audit_log_id: auditLogId,
            error_type: errorType,
            error_message: errorMessage,
            additional_data: additionalData,
            occurred_at: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to log audit error:', error);
    }
  }

  /**
   * Log successful completion
   */
  private async logAuditSuccess(
    auditLogId: string | null,
    successData: any
  ): Promise<void> {
    if (!auditLogId) return;

    try {
      await supabase
        .from('referral_audit_log')
        .update({
          details: supabase.sql`details || ${JSON.stringify({
            ...successData,
            completed_at: new Date().toISOString(),
            status: 'completed'
          })}::jsonb`,
          updated_at: new Date().toISOString()
        })
        .eq('id', auditLogId);
    } catch (error) {
      console.error('Failed to log audit success:', error);
    }
  }

  /**
   * Enhanced error handling for bonus distribution using batch processing
   */
  private async distributeBonusesWithErrorHandling(
    chain: ReferralChainUser[],
    bonusAmounts: { [level: number]: number },
    newPremiumUserId: string,
    newPremiumUserEmail: string,
    auditLogId: string | null
  ): Promise<BonusDistribution[]> {
    const distributions: BonusDistribution[] = [];

    try {
      // Prepare batch updates
      const batchUpdates = chain
        .filter(user => bonusAmounts[user.level] && bonusAmounts[user.level] > 0)
        .map(user => ({
          user_id: user.id,
          amount: bonusAmounts[user.level],
          description: `Level ${user.level} referral bonus for ${newPremiumUserEmail} becoming premium`,
          reference_type: 'multi_level_referral',
          reference_id: newPremiumUserId,
          metadata: {
            referral_level: user.level,
            referred_user_id: newPremiumUserId,
            referred_user_email: newPremiumUserEmail,
            bonus_type: 'multi_level_referral',
            audit_log_id: auditLogId
          }
        }));

      if (batchUpdates.length === 0) {
        console.warn('No valid bonus distributions to process');
        return distributions;
      }

      // Process batch updates using database function
      const { data: batchResult, error: batchError } = await supabase.rpc('batch_wallet_updates', {
        p_updates: batchUpdates
      });

      if (batchError) {
        throw new Error(`Batch processing failed: ${batchError.message}`);
      }

      // Process results
      if (batchResult?.results) {
        for (const result of batchResult.results) {
          const userId = result.user_id;
          const user = chain.find(u => u.id === userId);

          if (user && result.result?.success) {
            distributions.push({
              userId: user.id,
              level: user.level,
              amount: bonusAmounts[user.level],
              userName: user.full_name,
              userEmail: user.email
            });

            console.log(`Level ${user.level} bonus of ₹${bonusAmounts[user.level]} credited to ${user.email}`);
          } else if (user && !result.result?.success) {
            // Log individual failure
            await this.logAuditError(auditLogId, 'DISTRIBUTION_ERROR',
              `Failed to distribute bonus to user ${user.id} at level ${user.level}: ${result.result?.error}`,
              { user_id: user.id, level: user.level, amount: bonusAmounts[user.level] }
            );
          }
        }
      }

      // Log batch processing summary
      await this.logAuditSuccess(auditLogId, {
        batch_processing: {
          total_attempted: batchUpdates.length,
          successful: batchResult?.successful_updates || 0,
          failed: batchResult?.failed_updates || 0,
          total_amount_distributed: batchResult?.total_amount_distributed || 0
        }
      });

      return distributions;

    } catch (error) {
      console.error('Error in batch bonus distribution:', error);

      // Fallback to individual processing
      console.log('Falling back to individual processing...');
      return await this.distributeBonusesIndividually(chain, bonusAmounts, newPremiumUserId, newPremiumUserEmail, auditLogId);
    }
  }

  /**
   * Fallback individual bonus distribution
   */
  private async distributeBonusesIndividually(
    chain: ReferralChainUser[],
    bonusAmounts: { [level: number]: number },
    newPremiumUserId: string,
    newPremiumUserEmail: string,
    auditLogId: string | null
  ): Promise<BonusDistribution[]> {
    const distributions: BonusDistribution[] = [];
    const failedDistributions: Array<{ user: ReferralChainUser; error: string }> = [];

    for (const user of chain) {
      const bonusAmount = bonusAmounts[user.level];

      if (!bonusAmount || bonusAmount <= 0) {
        continue;
      }

      try {
        // Use safe wallet update function
        const { data: updateResult, error: updateError } = await supabase.rpc('safe_wallet_update', {
          p_user_id: user.id,
          p_amount: bonusAmount,
          p_description: `Level ${user.level} referral bonus for ${newPremiumUserEmail} becoming premium`,
          p_reference_type: 'multi_level_referral',
          p_reference_id: newPremiumUserId,
          p_metadata: {
            referral_level: user.level,
            referred_user_id: newPremiumUserId,
            referred_user_email: newPremiumUserEmail,
            bonus_type: 'multi_level_referral',
            audit_log_id: auditLogId
          }
        });

        if (updateError || !updateResult?.success) {
          throw new Error(updateResult?.error || updateError?.message || 'Unknown error');
        }

        distributions.push({
          userId: user.id,
          level: user.level,
          amount: bonusAmount,
          userName: user.full_name,
          userEmail: user.email
        });

        console.log(`Level ${user.level} bonus of ₹${bonusAmount} credited to ${user.email}`);

      } catch (error) {
        console.error(`Error distributing bonus to user ${user.id} at level ${user.level}:`, error);

        failedDistributions.push({
          user: user,
          error: error.message
        });

        // Log individual distribution error
        await this.logAuditError(auditLogId, 'DISTRIBUTION_ERROR',
          `Failed to distribute bonus to user ${user.id} at level ${user.level}: ${error.message}`,
          { user_id: user.id, level: user.level, amount: bonusAmount }
        );
      }
    }

    // Log failed distributions summary
    if (failedDistributions.length > 0) {
      await this.logAuditError(auditLogId, 'PARTIAL_DISTRIBUTION_FAILURE',
        `${failedDistributions.length} distributions failed out of ${chain.length}`,
        { failed_distributions: failedDistributions }
      );
    }

    return distributions;
  }
}

// Export singleton instance
export const multiLevelReferralService = new MultiLevelReferralService();
