import { supabase } from '../lib/supabase';
import { adminSupabase } from '../lib/adminSupabase';

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

interface AuthFixResult {
  success: boolean;
  message?: string;
  error?: string;
  results?: {
    total: number;
    fixed: number;
    failed: number;
    errors: string[];
  };
  user?: any;
}

export class AuthFixService {
  /**
   * Fix users who exist in database but not in Supabase Auth
   */
  static async fixMissingAuthUsers(): Promise<AuthFixResult> {
    try {
      console.log('🔧 Starting auth user fix process...');
      
      // Get all users from database
      const { data: dbUsers, error: dbError } = await supabase
        .from('users')
        .select('id, email, full_name, phone, username')
        .order('created_at', { ascending: true });
        
      if (dbError) {
        console.error('❌ Failed to fetch database users:', dbError);
        return { success: false, error: dbError.message };
      }
      
      if (!dbUsers || dbUsers.length === 0) {
        console.log('ℹ️ No users found in database');
        return { success: true, message: 'No users to fix' };
      }
      
      console.log(`📊 Found ${dbUsers.length} users in database`);
      
      // Get all auth users with retries
      let authUsers: { users: any[] } | null = null;
      let authError: Error | null = null;
      
      for (let i = 0; i < MAX_RETRIES; i++) {
        try {
          const result = await adminSupabase.auth.admin.listUsers();
          if (result.data) {
            authUsers = result.data;
            break;
          }
          authError = result.error || new Error('No data returned');
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        } catch (e: any) {
          authError = e;
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
      }
      
      if (!authUsers || authError) {
        console.error('❌ Failed to fetch auth users:', authError);
        return { success: false, error: authError?.message || 'Failed to fetch auth users' };
      }
      
      const authEmails = new Set(authUsers.users.map(user => user.email));
      console.log(`📊 Found ${authUsers.users.length} users in auth`);
      
      // Find users missing from auth
      const missingAuthUsers = dbUsers.filter(user => !authEmails.has(user.email));
      
      if (missingAuthUsers.length === 0) {
        console.log('✅ All users have auth accounts');
        return { success: true, message: 'All users are properly synchronized' };
      }
      
      console.log(`🔧 Found ${missingAuthUsers.length} users missing auth accounts`);
      
      const results = {
        total: missingAuthUsers.length,
        fixed: 0,
        failed: 0,
        errors: [] as string[]
      };
      
      // Fix each missing user with retries
      for (const user of missingAuthUsers) {
        let success = false;
        let lastError: Error | null = null;
        
        for (let i = 0; i < MAX_RETRIES && !success; i++) {
          try {
            console.log(`🔧 Creating auth user for: ${user.email} (attempt ${i + 1})`);
            
            // Create auth user with default password
            const defaultPassword = 'TempPass123!'; // User will need to reset
            
            const { data: newAuthUser, error: createError } = await adminSupabase.auth.admin.createUser({
              email: user.email,
              password: defaultPassword,
              email_confirm: true,
              user_metadata: {
                full_name: user.full_name,
                phone: user.phone,
                username: user.username,
                user_id: user.id,
                auth_fixed: true,
                temp_password: true
              }
            });
            
            if (createError) {
              lastError = createError;
              console.error(`❌ Failed to create auth user for ${user.email} (attempt ${i + 1}):`, createError);
              await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
              continue;
            }
            
            if (!newAuthUser?.user) {
              lastError = new Error('No user data returned');
              console.error(`❌ No user data returned for ${user.email} (attempt ${i + 1})`);
              await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
              continue;
            }
            
            // Update database user with new auth ID
            const { error: updateError } = await supabase
              .from('users')
              .update({ 
                id: newAuthUser.user.id,
                updated_at: new Date().toISOString()
              })
              .eq('email', user.email);
              
            if (updateError) {
              lastError = updateError;
              console.error(`❌ Failed to update database for ${user.email} (attempt ${i + 1}):`, updateError);
              await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
              continue;
            }
            
            console.log(`✅ Successfully fixed auth for: ${user.email}`);
            success = true;
            results.fixed++;
            
          } catch (error: any) {
            lastError = error;
            console.error(`❌ Unexpected error fixing ${user.email} (attempt ${i + 1}):`, error);
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          }
        }
        
        if (!success) {
          results.failed++;
          results.errors.push(`${user.email}: ${lastError?.message || 'Unknown error'}`);
        }
      }
      
      console.log(`🎉 Auth fix completed: ${results.fixed} fixed, ${results.failed} failed`);
      
      return {
        success: true,
        message: `Fixed ${results.fixed} users, ${results.failed} failed`,
        results
      };
      
    } catch (error: any) {
      console.error('❌ Auth fix service failed:', error);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * Fix a specific user's auth account
   */
  static async fixUserAuth(email: string, password: string): Promise<AuthFixResult> {
    try {
      console.log(`🔧 Fixing auth for user: ${email}`);
      
      // Check if user exists in database
      const { data: dbUser, error: dbError } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single();
        
      if (dbError || !dbUser) {
        console.error('❌ User not found in database:', dbError);
        return { success: false, error: 'User not found in database' };
      }

      // Check if auth user already exists with retries
      let authUsers: { users: any[] } | null = null;
      let authError: Error | null = null;
      
      for (let i = 0; i < MAX_RETRIES; i++) {
        try {
          const result = await adminSupabase.auth.admin.listUsers();
          if (result.data) {
            authUsers = result.data;
            break;
          }
          authError = result.error || new Error('No data returned');
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        } catch (e: any) {
          authError = e;
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
      }
      
      if (!authUsers || authError) {
        console.error('❌ Failed to check existing auth users:', authError);
        return { success: false, error: 'Failed to verify auth status' };
      }

      const existingAuthUser = authUsers.users.find(u => u.email === email);
      if (existingAuthUser) {
        console.log('⚠️ Auth user already exists, attempting to fix sync...');
        
        // Update database user with auth ID if needed
        if (dbUser.id !== existingAuthUser.id) {
          const { error: updateError } = await supabase
            .from('users')
            .update({ 
              id: existingAuthUser.id,
              updated_at: new Date().toISOString()
            })
            .eq('email', email);

          if (updateError) {
            console.error('❌ Failed to sync user ID:', updateError);
            return { success: false, error: 'Failed to synchronize user IDs' };
          }

          console.log('✅ User ID synchronized successfully');
        }

        return { success: true, message: 'User synchronized successfully' };
      }

      // Create new auth user with retries
      let newAuthUser: { user: any } | null = null;
      let createError: Error | null = null;
      
      for (let i = 0; i < MAX_RETRIES; i++) {
        try {
          console.log('📝 Creating new auth user...');
          const result = await adminSupabase.auth.admin.createUser({
            email: email,
            password: password,
            email_confirm: true,
            user_metadata: {
              full_name: dbUser.full_name,
              phone: dbUser.mobile_number,
              username: dbUser.username,
              auth_fixed: true
            }
          });
          
          if (result.data?.user) {
            newAuthUser = result.data;
            break;
          }
          createError = result.error || new Error('No user data returned');
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        } catch (e: any) {
          createError = e;
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
      }

      if (!newAuthUser?.user || createError) {
        console.error('❌ Failed to create auth user:', createError);
        return { success: false, error: createError?.message || 'Failed to create auth user' };
      }

      // Update database user with new auth ID
      const { error: updateError } = await supabase
        .from('users')
        .update({ 
          id: newAuthUser.user.id,
          updated_at: new Date().toISOString()
        })
        .eq('email', email);

      if (updateError) {
        console.error('❌ Failed to update database:', updateError);
        
        // Try to clean up auth user if database update fails
        try {
          await adminSupabase.auth.admin.deleteUser(newAuthUser.user.id);
        } catch (deleteError) {
          console.error('⚠️ Failed to clean up auth user:', deleteError);
        }
        
        return { success: false, error: 'Failed to synchronize user data' };
      }

      console.log('✅ Auth user created and synchronized successfully');
      return { 
        success: true, 
        message: 'Auth account created successfully',
        user: newAuthUser.user
      };

    } catch (error: any) {
      console.error('❌ Auth fix failed:', error);
      return { 
        success: false, 
        error: error.message || 'An unexpected error occurred'
      };
    }
  }

  static async fixUserSync(email: string): Promise<AuthFixResult> {
    try {
      console.log('🔄 Starting user sync fix for:', email);
      
      // Get auth user using listUsers with retries
      let users: { users: any[] } | null = null;
      let authError: Error | null = null;
      
      for (let i = 0; i < MAX_RETRIES; i++) {
        try {
          const result = await adminSupabase.auth.admin.listUsers();
          if (result.data) {
            users = result.data;
            break;
          }
          authError = result.error || new Error('No data returned');
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        } catch (e: any) {
          authError = e;
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
      }
      
      if (!users || authError) {
        console.error('❌ Failed to list users:', authError);
        return { success: false, error: 'Failed to list users' };
      }

      const authUser = users.users.find(u => u.email === email);
      
      if (!authUser) {
        console.error('❌ Auth user not found');
        return { success: false, error: 'Auth user not found' };
      }

      // Check database user
      const { data: dbUser, error: dbError } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (dbError && dbError.code !== 'PGRST116') {
        console.error('❌ Database error:', dbError);
        return { success: false, error: 'Database error' };
      }

      // If no database user, create one
      if (!dbUser) {
        console.log('📝 Creating database user');
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert({
            id: authUser.id,
            email: email,
            full_name: authUser.user_metadata?.full_name || email,
            username: email.split('@')[0],
            referral_code: 'REF' + Math.random().toString(36).substring(2, 8).toUpperCase(),
            is_premium: false,
            wallet_balance: 0,
            ewallet_unlocked: false
          })
          .select()
          .single();

        if (createError) {
          console.error('❌ Failed to create database user:', createError);
          return { success: false, error: 'Failed to create database user' };
        }

        console.log('✅ Database user created successfully');
        return {
          success: true,
          message: 'Database user created successfully',
          user: newUser
        };
      }

      // If IDs don't match, update database user
      if (dbUser.id !== authUser.id) {
        console.log('🔄 Syncing user IDs');
        const { error: updateError } = await supabase
          .from('users')
          .update({
            id: authUser.id,
            updated_at: new Date().toISOString()
          })
          .eq('email', email);

        if (updateError) {
          console.error('❌ Failed to sync user IDs:', updateError);
          return { success: false, error: 'Failed to sync user IDs' };
        }

        console.log('✅ User IDs synchronized successfully');
      }

      return {
        success: true,
        message: 'User synchronized successfully',
        user: dbUser
      };

    } catch (error: any) {
      console.error('❌ User sync failed:', error);
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      };
    }
  }
}
