import { supabase } from '../lib/supabase';
import { sendOTPEmail, checkEmailRateLimit } from './emailService';
import { sendOTPSMS, checkSMSRateLimit } from './smsService';
import toast from 'react-hot-toast';

interface OTPRecord {
  id: string;
  contact: string;
  contact_type: 'mobile' | 'email';
  otp_code: string;
  expires_at: string;
  verified: boolean;
  attempts: number;
  created_at: string;
}

// Generate a 6-digit OTP
const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP via email
const sendEmailOTP = async (email: string, otp: string): Promise<boolean> => {
  try {
    // Check rate limiting
    if (!checkEmailRateLimit(email)) {
      toast.error('Too many OTP requests. Please try again later.');
      return false;
    }

    // Try to send email using the email service
    const emailSent = await sendOTPEmail(email, otp);

    if (emailSent) {
      // For demo purposes, also show <PERSON><PERSON> in toast (remove in production)
      toast.success(`Demo OTP sent to ${email}: ${otp}`, {
        duration: 15000,
        style: {
          background: '#10B981',
          color: 'white',
        },
      });
      return true;
    } else {
      // Fallback: show OTP in toast for demo
      console.log(`OTP for ${email}: ${otp}`);
      toast.success(`Demo OTP for ${email}: ${otp}`, {
        duration: 15000,
        style: {
          background: '#10B981',
          color: 'white',
        },
      });
      return true;
    }
  } catch (error) {
    console.error('Error sending email OTP:', error);
    return false;
  }
};

// Send OTP via SMS
const sendSMSOTPInternal = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    // Check rate limiting for mobile
    if (!checkSMSRateLimit(mobile)) {
      toast.error('Too many SMS OTP requests. Please try again later.');
      return false;
    }

    // Try to send SMS using the SMS service
    const smsSent = await sendOTPSMS(mobile, otp);

    if (smsSent) {
      // For demo purposes, also show OTP in toast (remove in production)
      toast.success(`Demo SMS OTP sent to +91${mobile}: ${otp}`, {
        duration: 15000,
        style: {
          background: '#10B981',
          color: 'white',
        },
      });
      return true;
    } else {
      // Fallback: show OTP in toast for demo
      console.log(`SMS OTP for +91${mobile}: ${otp}`);
      toast.success(`Demo SMS OTP for +91${mobile}: ${otp}`, {
        duration: 15000,
        style: {
          background: '#10B981',
          color: 'white',
        },
      });
      return true;
    }
  } catch (error) {
    console.error('Error sending SMS OTP:', error);
    return false;
  }
};

// Send OTP
export const sendOTP = async (contact: string, contactType: 'mobile' | 'email'): Promise<boolean> => {
  try {
    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now

    // Clean up any existing OTPs for this contact
    await supabase
      .from('otp_verifications')
      .delete()
      .eq('contact', contact)
      .eq('contact_type', contactType);

    // Store OTP in database
    const { error: dbError } = await supabase
      .from('otp_verifications')
      .insert({
        contact,
        contact_type: contactType,
        otp_code: otp,
        expires_at: expiresAt.toISOString(),
        verified: false,
        attempts: 0
      });

    if (dbError) {
      console.error('Error storing OTP:', dbError);
      return false;
    }

    // Send OTP based on contact type
    let sent = false;
    if (contactType === 'email') {
      sent = await sendEmailOTP(contact, otp);
    } else if (contactType === 'mobile') {
      sent = await sendSMSOTPInternal(contact, otp);
    }

    if (sent) {
      toast.success(`OTP sent to your ${contactType}`);
      return true;
    } else {
      toast.error('Failed to send OTP. Please try again.');
      return false;
    }
  } catch (error) {
    console.error('Error in sendOTP:', error);
    toast.error('Failed to send OTP. Please try again.');
    return false;
  }
};

// Verify OTP
export const verifyOTP = async (contact: string, contactType: 'mobile' | 'email', otp: string): Promise<boolean> => {
  try {
    // Get the latest OTP record for this contact
    const { data: otpRecord, error: fetchError } = await supabase
      .from('otp_verifications')
      .select('*')
      .eq('contact', contact)
      .eq('contact_type', contactType)
      .eq('verified', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle(); // Use maybeSingle to handle no results gracefully

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching OTP record:', fetchError);
      toast.error('Database error. Please try again.');
      return false;
    }

    if (!otpRecord) {
      toast.error('No valid OTP found. Please request a new one.');
      return false;
    }

    // Check if OTP has expired
    const now = new Date();
    const expiresAt = new Date(otpRecord.expires_at);
    if (now > expiresAt) {
      toast.error('OTP has expired. Please request a new one.');
      return false;
    }

    // Check if too many attempts
    if (otpRecord.attempts >= 3) {
      toast.error('Too many failed attempts. Please request a new OTP.');
      return false;
    }

    // Verify OTP
    if (otpRecord.otp_code === otp) {
      // Mark as verified
      const { error: updateError } = await supabase
        .from('otp_verifications')
        .update({ 
          verified: true,
          attempts: otpRecord.attempts + 1
        })
        .eq('id', otpRecord.id);

      if (updateError) {
        console.error('Error updating OTP record:', updateError);
        return false;
      }

      toast.success('OTP verified successfully!');
      return true;
    } else {
      // Increment attempts
      await supabase
        .from('otp_verifications')
        .update({ 
          attempts: otpRecord.attempts + 1
        })
        .eq('id', otpRecord.id);

      const remainingAttempts = 3 - (otpRecord.attempts + 1);
      if (remainingAttempts > 0) {
        toast.error(`Invalid OTP. ${remainingAttempts} attempts remaining.`);
      } else {
        toast.error('Invalid OTP. Too many failed attempts. Please request a new OTP.');
      }
      return false;
    }
  } catch (error) {
    console.error('Error in verifyOTP:', error);
    toast.error('Failed to verify OTP. Please try again.');
    return false;
  }
};

// Clean up expired OTPs (utility function)
export const cleanupExpiredOTPs = async (): Promise<void> => {
  try {
    const now = new Date();
    await supabase
      .from('otp_verifications')
      .delete()
      .lt('expires_at', now.toISOString());
  } catch (error) {
    console.error('Error cleaning up expired OTPs:', error);
  }
};

// Check if contact is already verified
export const isContactVerified = async (contact: string, contactType: 'mobile' | 'email'): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('otp_verifications')
      .select('verified')
      .eq('contact', contact)
      .eq('contact_type', contactType)
      .eq('verified', true)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle(); // Use maybeSingle to handle no results gracefully

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking contact verification:', error);
      return false;
    }

    return data?.verified === true;
  } catch (error) {
    console.error('Error in isContactVerified:', error);
    return false;
  }
};
