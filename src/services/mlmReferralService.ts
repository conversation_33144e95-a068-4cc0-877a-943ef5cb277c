import { supabase } from '../lib/supabase';

export interface MLMUser {
  id: string;
  name: string;
  email: string;
  level: number;
  referrer_id: string | null;
  referrer_name?: string;
  direct_referrals: MLMUser[];
  referral_count: number;
  is_premium: boolean;
  wallet_balance: number;
}

export interface BonusDistribution {
  referral_id: string;
  referral_name: string;
  referral_email: string;
  bonus_recipient_id: string;
  bonus_recipient_name: string;
  bonus_recipient_email: string;
  bonus_amount: number;
  referral_position: number; // 1st, 2nd, 3rd, etc.
  reason: string;
}

export interface MLMReferralResult {
  success: boolean;
  bonus_distributions: BonusDistribution[];
  total_bonus_distributed: number;
  error?: string;
}

// MLM Bonus Configuration
export const MLM_CONFIG = {
  BONUS_AMOUNT: 250.00, // Fixed bonus amount for each referral
  DIRECT_REFERRAL_LIMIT: 2, // 1st and 2nd referrals go to direct referrer
  GRANDPARENT_REFERRAL_POSITION: 3 // ONLY 3rd referral goes to grandparent
};

export class MLMReferralService {
  
  /**
   * Get user's referral tree structure
   */
  async getUserReferralTree(userId: string): Promise<MLMUser | null> {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          id,
          full_name,
          email,
          referred_by,
          is_premium,
          wallet_balance
        `)
        .eq('id', userId)
        .single();

      if (error || !user) {
        console.error('User not found:', error);
        return null;
      }

      // Get direct referrals
      const { data: referrals, error: referralsError } = await supabase
        .from('users')
        .select(`
          id,
          full_name,
          email,
          referred_by,
          is_premium,
          wallet_balance
        `)
        .eq('referred_by', user.id)
        .order('created_at', { ascending: true });

      if (referralsError) {
        console.error('Error fetching referrals:', referralsError);
        return null;
      }

      // Get referrer info
      let referrerName = '';
      if (user.referred_by) {
        const { data: referrer } = await supabase
          .from('users')
          .select('full_name')
          .eq('id', user.referred_by)
          .single();
        referrerName = referrer?.full_name || '';
      }

      const mlmUser: MLMUser = {
        id: user.id,
        name: user.full_name,
        email: user.email,
        level: 1, // Will be calculated based on tree depth
        referrer_id: user.referred_by,
        referrer_name: referrerName,
        direct_referrals: referrals?.map(ref => ({
          id: ref.id,
          name: ref.full_name,
          email: ref.email,
          level: 2,
          referrer_id: ref.referred_by,
          direct_referrals: [],
          referral_count: 0,
          is_premium: ref.is_premium,
          wallet_balance: ref.wallet_balance
        })) || [],
        referral_count: referrals?.length || 0,
        is_premium: user.is_premium,
        wallet_balance: user.wallet_balance
      };

      return mlmUser;
    } catch (error) {
      console.error('Error building referral tree:', error);
      return null;
    }
  }

  /**
   * Calculate who gets the bonus for a new referral
   */
  calculateBonusRecipient(referrer: MLMUser, newReferralPosition: number): {
    recipient_id: string;
    recipient_name: string;
    recipient_email: string;
    reason: string;
  } | null {

    // Rule 1 & 2: 1st and 2nd referrals go to direct referrer
    if (newReferralPosition <= MLM_CONFIG.DIRECT_REFERRAL_LIMIT) {
      return {
        recipient_id: referrer.id,
        recipient_name: referrer.name,
        recipient_email: referrer.email,
        reason: `Direct referral #${newReferralPosition} - bonus to direct referrer`
      };
    }

    // Rule 3: ONLY 3rd referral goes to referrer's referrer (grandparent)
    if (newReferralPosition === MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION && referrer.referrer_id) {
      return {
        recipient_id: referrer.referrer_id,
        recipient_name: referrer.referrer_name || 'Unknown',
        recipient_email: '', // Will be fetched separately
        reason: `3rd referral - bonus to grandparent (${referrer.referrer_name})`
      };
    }

    // Rule 4: 4th+ referrals go back to direct referrer
    if (newReferralPosition > MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION) {
      return {
        recipient_id: referrer.id,
        recipient_name: referrer.name,
        recipient_email: referrer.email,
        reason: `${newReferralPosition}th referral - bonus back to direct referrer`
      };
    }

    return null;
  }

  /**
   * Process MLM referral bonus when a new user is referred
   */
  async processMLMReferral(
    newUserId: string,
    newUserEmail: string,
    referralCode: string
  ): Promise<MLMReferralResult> {
    try {
      console.log('🎯 Processing MLM referral:', { newUserId, newUserEmail, referralCode });

      // 1. Find the direct referrer
      const { data: directReferrer, error: referrerError } = await supabase
        .from('users')
        .select(`
          id,
          full_name,
          email,
          referred_by,
          is_premium,
          wallet_balance
        `)
        .eq('referral_code', referralCode)
        .single();

      if (referrerError || !directReferrer) {
        return {
          success: false,
          bonus_distributions: [],
          total_bonus_distributed: 0,
          error: 'Invalid referral code - referrer not found'
        };
      }

      // 2. Check if new user is premium (required for bonus distribution)
      const { data: newUser, error: newUserError } = await supabase
        .from('users')
        .select('id, full_name, email, is_premium')
        .eq('id', newUserId)
        .single();

      if (newUserError || !newUser || !newUser.is_premium) {
        return {
          success: false,
          bonus_distributions: [],
          total_bonus_distributed: 0,
          error: 'New user must be premium to trigger bonus distribution'
        };
      }

      // 3. Count existing referrals for the direct referrer
      const { data: existingReferrals, error: countError } = await supabase
        .from('users')
        .select('id')
        .eq('referred_by', directReferrer.id);

      if (countError) {
        return {
          success: false,
          bonus_distributions: [],
          total_bonus_distributed: 0,
          error: 'Error counting existing referrals'
        };
      }

      const currentReferralCount = existingReferrals?.length || 0;
      const newReferralPosition = currentReferralCount + 1;

      console.log(`📊 Referral position: ${newReferralPosition} for referrer: ${directReferrer.full_name}`);

      // 4. Build referrer object
      const referrerMLM: MLMUser = {
        id: directReferrer.id,
        name: directReferrer.full_name,
        email: directReferrer.email,
        level: 1,
        referrer_id: directReferrer.referred_by,
        referrer_name: '',
        direct_referrals: [],
        referral_count: currentReferralCount,
        is_premium: directReferrer.is_premium,
        wallet_balance: directReferrer.wallet_balance
      };

      // Get referrer's referrer name if exists
      if (directReferrer.referred_by) {
        const { data: grandparent } = await supabase
          .from('users')
          .select('full_name, email')
          .eq('id', directReferrer.referred_by)
          .single();
        
        if (grandparent) {
          referrerMLM.referrer_name = grandparent.full_name;
        }
      }

      // 5. Calculate bonus recipient
      const bonusRecipient = this.calculateBonusRecipient(referrerMLM, newReferralPosition);

      if (!bonusRecipient) {
        return {
          success: false,
          bonus_distributions: [],
          total_bonus_distributed: 0,
          error: 'No valid bonus recipient found'
        };
      }

      // 6. Get recipient email if it's the grandparent
      if (bonusRecipient.recipient_id !== directReferrer.id) {
        const { data: recipient } = await supabase
          .from('users')
          .select('email')
          .eq('id', bonusRecipient.recipient_id)
          .single();
        
        if (recipient) {
          bonusRecipient.recipient_email = recipient.email;
        }
      }

      // 7. Get current bonus amount from admin settings
      const currentBonusAmount = await this.getMLMBonusAmount();

      // 8. Distribute the bonus
      const success = await this.distributeBonusToRecipient(
        bonusRecipient.recipient_id,
        currentBonusAmount,
        newUser.full_name,
        newUser.email,
        newReferralPosition
      );

      if (!success) {
        return {
          success: false,
          bonus_distributions: [],
          total_bonus_distributed: 0,
          error: 'Failed to distribute bonus'
        };
      }

      // 8. Create bonus distribution record
      const bonusDistribution: BonusDistribution = {
        referral_id: newUser.id,
        referral_name: newUser.full_name,
        referral_email: newUser.email,
        bonus_recipient_id: bonusRecipient.recipient_id,
        bonus_recipient_name: bonusRecipient.recipient_name,
        bonus_recipient_email: bonusRecipient.recipient_email,
        bonus_amount: currentBonusAmount,
        referral_position: newReferralPosition,
        reason: bonusRecipient.reason
      };

      console.log('✅ MLM Referral processed successfully:', bonusDistribution);

      return {
        success: true,
        bonus_distributions: [bonusDistribution],
        total_bonus_distributed: currentBonusAmount,
      };

    } catch (error) {
      console.error('❌ Error processing MLM referral:', error);
      return {
        success: false,
        bonus_distributions: [],
        total_bonus_distributed: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Distribute bonus to the calculated recipient
   */
  private async distributeBonusToRecipient(
    recipientId: string,
    bonusAmount: number,
    referralName: string,
    referralEmail: string,
    referralPosition: number
  ): Promise<boolean> {
    try {
      // 1. Update recipient's wallet balance
      const { error: walletError } = await supabase
        .from('users')
        .update({
          wallet_balance: supabase.raw(`wallet_balance + ${bonusAmount}`)
        })
        .eq('id', recipientId);

      if (walletError) {
        console.error('Error updating wallet balance:', walletError);
        return false;
      }

      // 2. Create wallet transaction record
      const { error: transactionError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: recipientId,
          type: 'credit',
          amount: bonusAmount,
          description: `MLM Referral bonus (Position #${referralPosition}) for ${referralName} (${referralEmail})`,
          reference_type: 'referral',
          reference_id: recipientId
        });

      if (transactionError) {
        console.error('Error creating transaction record:', transactionError);
        return false;
      }

      console.log(`💰 Bonus ₹${bonusAmount} distributed to recipient ${recipientId}`);
      return true;

    } catch (error) {
      console.error('Error distributing bonus:', error);
      return false;
    }
  }

  /**
   * Get MLM bonus amount from admin settings
   */
  async getMLMBonusAmount(): Promise<number> {
    try {
      const { data: setting, error } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'mlm_bonus_amount')
        .single();

      if (error || !setting) {
        console.warn('Failed to load MLM bonus amount from settings, using default:', MLM_CONFIG.BONUS_AMOUNT);
        return MLM_CONFIG.BONUS_AMOUNT;
      }

      const amount = parseFloat(setting.setting_value);
      return isNaN(amount) ? MLM_CONFIG.BONUS_AMOUNT : amount;
    } catch (error) {
      console.warn('Error loading MLM bonus amount, using default:', error);
      return MLM_CONFIG.BONUS_AMOUNT;
    }
  }

  /**
   * Check if MLM system is enabled
   */
  async isMLMSystemEnabled(): Promise<boolean> {
    try {
      const { data: setting, error } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'mlm_system_enabled')
        .single();

      if (error || !setting) {
        console.warn('MLM system setting not found, defaulting to enabled');
        return true;
      }

      return setting.setting_value === 'true';
    } catch (error) {
      console.warn('Error checking MLM system status, defaulting to enabled:', error);
      return true;
    }
  }
}

// Export singleton instance
export const mlmReferralService = new MLMReferralService();
