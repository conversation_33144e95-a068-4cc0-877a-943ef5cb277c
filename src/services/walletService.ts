import { supabase } from '../lib/supabase';

export interface WalletTransaction {
  id: string;
  user_id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_type: string;
  reference_id?: string;
  referral_id?: string;
  bonus_level?: number;
  created_at: string;
}

export interface WalletBalance {
  balance: number;
  locked_balance: number;
  available_balance: number;
}

class WalletService {
  // E-wallet is always unlocked for premium users - no lock functionality
  async isEwalletUnlocked(userId: string): Promise<boolean> {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('is_premium')
        .eq('id', userId)
        .single();

      // Always return true for premium users - no wallet lock
      return user?.is_premium || false;
    } catch (error) {
      console.error('Error checking premium status:', error);
      return false;
    }
  }

  // Get user's wallet balance
  async getBalance(userId: string): Promise<WalletBalance> {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', userId)
        .single();

      const balance = user?.wallet_balance || 0;

      // For now, no locked balance implementation
      return {
        balance,
        locked_balance: 0,
        available_balance: balance
      };
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      return {
        balance: 0,
        locked_balance: 0,
        available_balance: 0
      };
    }
  }

  // Add funds to user's wallet
  async addFunds(
    userId: string,
    amount: number,
    description: string,
    referenceType: string = 'admin_credit',
    referenceId?: string,
    referralId?: string,
    bonusLevel: number = 1
  ): Promise<boolean> {
    try {
      // Start transaction
      const { data: currentUser } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', userId)
        .single();

      if (!currentUser) {
        throw new Error('User not found');
      }

      const newBalance = (currentUser.wallet_balance || 0) + amount;

      // Update user balance
      const { error: updateError } = await supabase
        .from('users')
        .update({ wallet_balance: newBalance })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: userId,
          type: 'credit',
          amount,
          description,
          reference_type: referenceType,
          reference_id: referenceId,
          referral_id: referralId,
          bonus_level: bonusLevel
        });

      if (transactionError) throw transactionError;

      return true;
    } catch (error) {
      console.error('Error adding funds:', error);
      return false;
    }
  }

  // Deduct funds from user's wallet
  async deductFunds(
    userId: string,
    amount: number,
    description: string,
    referenceType: string = 'purchase',
    referenceId?: string
  ): Promise<boolean> {
    try {
      // Premium users have full wallet access - no lock checks needed
      const { data: user } = await supabase
        .from('users')
        .select('is_premium')
        .eq('id', userId)
        .single();

      if (!user?.is_premium) {
        throw new Error('Wallet access requires premium membership.');
      }

      const { data: currentUser } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', userId)
        .single();

      if (!currentUser) {
        throw new Error('User not found');
      }

      const currentBalance = currentUser.wallet_balance || 0;
      if (currentBalance < amount) {
        throw new Error('Insufficient balance');
      }

      const newBalance = currentBalance - amount;

      // Update user balance
      const { error: updateError } = await supabase
        .from('users')
        .update({ wallet_balance: newBalance })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: userId,
          type: 'debit',
          amount,
          description,
          reference_type: referenceType,
          reference_id: referenceId
        });

      if (transactionError) throw transactionError;

      return true;
    } catch (error) {
      console.error('Error deducting funds:', error);
      return false;
    }
  }

  // Withdraw funds from wallet
  async withdrawFunds(userId: string, amount: number): Promise<boolean> {
    try {
      // Premium users have full wallet access - no lock checks needed
      const { data: user } = await supabase
        .from('users')
        .select('is_premium')
        .eq('id', userId)
        .single();

      if (!user?.is_premium) {
        throw new Error('Wallet access requires premium membership.');
      }

      // Get minimum withdrawal amount from settings
      const { data: minWithdrawalSettings } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'minimum_withdrawal_amount')
        .single();

      const minAmount = parseFloat(minWithdrawalSettings?.setting_value || '1');

      if (amount < minAmount) {
        throw new Error(`Minimum withdrawal amount is ₹${minAmount}`);
      }

      // Get withdrawal fee from settings
      const { data: feeSettings } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'withdrawal_fee_percentage')
        .single();

      const feePercentage = parseFloat(feeSettings?.setting_value || '0');
      const fee = (amount * feePercentage) / 100;
      const totalDeduction = amount + fee;

      const { data: currentUser } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', userId)
        .single();

      if (!currentUser) {
        throw new Error('User not found');
      }

      const currentBalance = currentUser.wallet_balance || 0;
      if (currentBalance < totalDeduction) {
        throw new Error('Insufficient balance for withdrawal including fees');
      }

      const newBalance = currentBalance - totalDeduction;

      // Update user balance
      const { error: updateError } = await supabase
        .from('users')
        .update({ wallet_balance: newBalance })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Create withdrawal transaction
      const { error: withdrawalError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: userId,
          type: 'debit',
          amount: amount,
          description: `Withdrawal of ₹${amount}`,
          reference_type: 'withdrawal'
        });

      if (withdrawalError) throw withdrawalError;

      // Create fee transaction if applicable
      if (fee > 0) {
        await supabase
          .from('wallet_transactions')
          .insert({
            user_id: userId,
            type: 'debit',
            amount: fee,
            description: `Withdrawal fee (${feePercentage}%)`,
            reference_type: 'withdrawal_fee'
          });
      }

      return true;
    } catch (error) {
      console.error('Error withdrawing funds:', error);
      return false;
    }
  }

  // Get user's transaction history
  async getTransactionHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<WalletTransaction[]> {
    try {
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting transaction history:', error);
      return [];
    }
  }

  // Get referral-specific transactions
  async getReferralTransactions(userId: string): Promise<WalletTransaction[]> {
    try {
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', userId)
        .in('reference_type', ['referral', 'referral_special'])
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting referral transactions:', error);
      return [];
    }
  }

  // No unlock progress needed - wallet is always available for premium users
  async getUnlockProgress(userId: string): Promise<{
    current: number;
    required: number;
    unlocked: boolean;
    progress: number;
  }> {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('is_premium')
        .eq('id', userId)
        .single();

      // Wallet is always unlocked for premium users - no progress tracking needed
      const unlocked = user?.is_premium || false;

      return {
        current: 1,
        required: 1,
        unlocked,
        progress: 100 // Always 100% for premium users
      };
    } catch (error) {
      console.error('Error checking premium status:', error);
      return {
        current: 1,
        required: 1,
        unlocked: false,
        progress: 0
      };
    }
  }
}

export const walletService = new WalletService();
