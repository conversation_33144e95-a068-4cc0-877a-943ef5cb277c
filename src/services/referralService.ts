import { supabase } from '../lib/supabase';
import { mlmReferralService } from './mlmReferralService';

export interface ReferralData {
  id: string;
  referrer_id: string;
  referred_user_id: string;
  bonus_amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  referral_level: number;
  is_special_third: boolean;
  created_at: string;
  completed_at?: string;
  referred_user?: {
    full_name: string;
    email: string;
    phone?: string;
  };
}

export interface ReferralStats {
  totalReferrals: number;
  completedReferrals: number;
  pendingReferrals: number;
  totalEarnings: number;
  premiumReferrals: number;
  ewalletUnlocked: boolean;
  specialStatus: boolean;
}

export interface ReferralBonusReceived {
  id: string;
  amount: number;
  description: string;
  created_at: string;
  referral_level: number;
  referrer_name: string;
  referrer_email: string;
  referred_user_name?: string;
  referred_user_email?: string;
}

class ReferralService {
  // Generate unique referral code for user
  async generateReferralCode(userId: string): Promise<string | null> {
    try {
      // Check if user already has a referral code
      const { data: existingUser } = await supabase
        .from('users')
        .select('referral_code')
        .eq('id', userId)
        .single();

      if (existingUser?.referral_code) {
        return existingUser.referral_code;
      }

      // Generate new code using database function
      const { data, error } = await supabase.rpc('generate_referral_code');
      
      if (error) throw error;

      const referralCode = data;

      // Update user with new referral code
      const { error: updateError } = await supabase
        .from('users')
        .update({ referral_code: referralCode })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Log the action
      await this.logReferralAction(userId, 'code_generated', { code: referralCode });

      return referralCode;
    } catch (error) {
      console.error('Error generating referral code:', error);
      return null;
    }
  }

  // Process MLM referral when user becomes premium
  async createReferral(referralCode: string, newUserId: string, newUserEmail: string): Promise<boolean> {
    try {
      console.log('🎯 MLM referral processing started:', { referralCode, newUserId, newUserEmail });

      // Process the MLM referral using new logic
      const result = await mlmReferralService.processMLMReferral(
        newUserId,
        newUserEmail,
        referralCode
      );

      if (result.success) {
        console.log(`✅ MLM referral processed successfully:`, {
          totalBonusDistributed: result.total_bonus_distributed,
          bonusDistributions: result.bonus_distributions.length
        });

        // Log the action for audit trail
        await this.logReferralAction(newUserId, 'mlm_referral_completed', {
          referral_code: referralCode,
          total_bonus_distributed: result.total_bonus_distributed,
          bonus_distributions: result.bonus_distributions
        });

        return true;
      } else {
        console.error('❌ MLM referral processing failed:', result.error);
        return false;
      }

    } catch (error) {
      console.error('❌ Error in MLM referral processing:', error);
      return false;
    }
  }

  // Complete referral when referred user becomes premium - triggers multi-level processing
  async completeReferral(referredUserId: string): Promise<void> {
    try {
      console.log('Referral completion triggered for premium user:', referredUserId);

      // Get user data including referral information
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, email, full_name, is_premium, referred_by')
        .eq('id', referredUserId)
        .single();

      if (userError || !user) {
        console.error('User not found for referral completion:', userError);
        return;
      }

      if (!user.is_premium) {
        console.log('User is not premium - referral completion skipped');
        return;
      }

      if (!user.referred_by) {
        console.log('User has no referral code - referral completion skipped');
        return;
      }

      // Check if referral has already been processed
      const { data: existingReferral, error: referralError } = await supabase
        .from('referrals')
        .select('id, status')
        .eq('referred_user_id', referredUserId)
        .single();

      if (!referralError && existingReferral && existingReferral.status === 'completed') {
        console.log('Referral already processed for user:', referredUserId);
        return;
      }

      // Process MLM referral using the user's referral code
      const success = await this.createReferral(user.referred_by, referredUserId, user.email);

      if (success) {
        console.log('✅ MLM referral completion successful for user:', referredUserId);
      } else {
        console.error('❌ MLM referral completion failed for user:', referredUserId);
      }

    } catch (error) {
      console.error('Error completing referral:', error);
    }
  }

  // Add funds to user's wallet
  private async addWalletFunds(
    userId: string,
    amount: number,
    description: string,
    referenceType: string = 'referral',
    referenceId?: string
  ): Promise<void> {
    try {
      // Get current balance
      const { data: user } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', userId)
        .single();

      const currentBalance = user?.wallet_balance || 0;
      const newBalance = currentBalance + amount;

      // Update user balance
      await supabase
        .from('users')
        .update({ wallet_balance: newBalance })
        .eq('id', userId);

      // Create transaction record
      await supabase
        .from('wallet_transactions')
        .insert({
          user_id: userId,
          type: 'credit',
          amount,
          description,
          reference_type: referenceType,
          reference_id: referenceId,
          referral_id: referenceId
        });
    } catch (error) {
      console.error('Error adding wallet funds:', error);
    }
  }

  // E-wallet is now automatically unlocked for all premium users
  // This function is deprecated and no longer needed
  private async checkEwalletUnlock(userId: string): Promise<void> {
    // E-wallet unlock is now automatic for premium users
    // No referral count requirement
    return;
  }

  // Get user's referral statistics
  async getReferralStats(userId: string): Promise<ReferralStats> {
    try {
      // Get user data
      const { data: user } = await supabase
        .from('users')
        .select('premium_referral_count, ewallet_unlocked, referral_special_status')
        .eq('id', userId)
        .single();

      // Get referral data separately to avoid JOIN issues
      const { data: referrals } = await supabase
        .from('referrals')
        .select('status, bonus_amount')
        .eq('referrer_id', userId);

      // Get wallet earnings separately to avoid JOIN issues
      const { data: earnings } = await supabase
        .from('wallet_transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('reference_type', 'referral');

      const totalReferrals = referrals?.length || 0;
      const completedReferrals = referrals?.filter(r => r.status === 'completed').length || 0;
      const pendingReferrals = referrals?.filter(r => r.status === 'pending').length || 0;
      const totalEarnings = earnings?.reduce((sum, t) => sum + t.amount, 0) || 0;

      console.log('Referral Stats Debug:', {
        userId,
        totalReferrals,
        completedReferrals,
        pendingReferrals,
        premiumReferrals: user?.premium_referral_count || 0,
        totalEarnings,
        ewalletUnlocked: user?.ewallet_unlocked || false
      });

      return {
        totalReferrals,
        completedReferrals,
        pendingReferrals,
        totalEarnings,
        premiumReferrals: user?.premium_referral_count || 0,
        ewalletUnlocked: user?.ewallet_unlocked || false,
        specialStatus: user?.referral_special_status || false
      };
    } catch (error) {
      console.error('Error getting referral stats:', error);
      return {
        totalReferrals: 0,
        completedReferrals: 0,
        pendingReferrals: 0,
        totalEarnings: 0,
        premiumReferrals: 0,
        ewalletUnlocked: false,
        specialStatus: false
      };
    }
  }

  // Get user's referral network
  async getReferralNetwork(userId: string): Promise<ReferralData[]> {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .select(`
          *,
          referred_user:users!referrals_referred_user_id_fkey(full_name, email, phone)
        `)
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting referral network:', error);
      return [];
    }
  }

  // Get referral bonuses received by the user
  async getReferralBonusesReceived(userId: string): Promise<ReferralBonusReceived[]> {
    try {
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select(`
          id,
          amount,
          description,
          created_at,
          bonus_level,
          metadata,
          referral_id
        `)
        .eq('user_id', userId)
        .eq('type', 'credit')
        .eq('reference_type', 'referral')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // For each transaction, try to extract referrer info from description or metadata
      return (data || []).map(transaction => {
        let referrerName = 'System';
        let referrerEmail = '<EMAIL>';
        let referredUserName = '';
        let referredUserEmail = '';

        // Try to extract email from description (e.g., "Level 1 referral <NAME_EMAIL>")
        const emailMatch = transaction.description.match(/for\s+([^\s]+@[^\s]+)/);
        if (emailMatch) {
          referredUserEmail = emailMatch[1];
          referredUserName = emailMatch[1].split('@')[0];
        }

        // If we have metadata, try to get more info
        if (transaction.metadata && typeof transaction.metadata === 'object') {
          const metadata = transaction.metadata as any;
          if (metadata.referrer_name) referrerName = metadata.referrer_name;
          if (metadata.referrer_email) referrerEmail = metadata.referrer_email;
          if (metadata.referred_user_name) referredUserName = metadata.referred_user_name;
          if (metadata.referred_user_email) referredUserEmail = metadata.referred_user_email;
        }

        return {
          id: transaction.id,
          amount: transaction.amount,
          description: transaction.description,
          created_at: transaction.created_at,
          referral_level: transaction.bonus_level || 1,
          referrer_name: referrerName,
          referrer_email: referrerEmail,
          referred_user_name: referredUserName || undefined,
          referred_user_email: referredUserEmail || undefined
        };
      });
    } catch (error) {
      console.error('Error fetching referral bonuses received:', error);
      return [];
    }
  }

  // Get detailed referral statistics with multi-level information
  async getReferralStatisticsDetailed(userId: string): Promise<any> {
    try {
      console.log('Getting detailed multi-level referral statistics for user:', userId);

      // Get basic user info
      const { data: user } = await supabase
        .from('users')
        .select('id, email, full_name, is_premium, referral_code, ewallet_unlocked')
        .eq('id', userId)
        .single();

      if (!user) {
        return null;
      }

      // Get referral statistics
      const stats = await this.getReferralStats(userId);

      // Get multi-level earnings breakdown
      const { data: multiLevelEarnings } = await supabase
        .from('wallet_transactions')
        .select('amount, description, metadata, created_at')
        .eq('user_id', userId)
        .eq('reference_type', 'multi_level_referral')
        .order('created_at', { ascending: false });

      // Group earnings by level
      const earningsByLevel: { [level: number]: number } = {};
      let totalMultiLevelEarnings = 0;

      multiLevelEarnings?.forEach(transaction => {
        const level = transaction.metadata?.referral_level;
        if (level) {
          earningsByLevel[level] = (earningsByLevel[level] || 0) + transaction.amount;
          totalMultiLevelEarnings += transaction.amount;
        }
      });

      // Get referral network depth
      const networkDepth = await this.calculateNetworkDepth(userId);

      return {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          is_premium: user.is_premium,
          referral_code: user.referral_code,
          ewallet_unlocked: user.ewallet_unlocked
        },
        statistics: stats,
        multiLevelEarnings: {
          total: totalMultiLevelEarnings,
          byLevel: earningsByLevel,
          transactions: multiLevelEarnings || []
        },
        networkInfo: {
          depth: networkDepth,
          canRefer: user.is_premium
        }
      };
    } catch (error) {
      console.error('Error getting detailed referral statistics:', error);
      return null;
    }
  }

  // Helper method to calculate network depth
  private async calculateNetworkDepth(userId: string): Promise<number> {
    try {
      // This is a simplified calculation - in a real scenario you might want to traverse the entire network
      const { count } = await supabase
        .from('referrals')
        .select('*', { count: 'exact', head: true })
        .eq('referrer_id', userId);

      return count || 0;
    } catch (error) {
      console.error('Error calculating network depth:', error);
      return 0;
    }
  }

  // Simulate next referral bonus showing multi-level distribution
  async simulateNextReferralBonus(userId: string): Promise<any> {
    try {
      console.log('Multi-level referral bonus simulation for user:', userId);

      // Get user's referral code
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('referral_code, is_premium')
        .eq('id', userId)
        .single();

      if (userError || !user || !user.is_premium) {
        return {
          canRefer: false,
          reason: 'User is not premium or not found'
        };
      }

      // Get configurable bonus amounts
      const bonusAmounts = await multiLevelReferralService.getConfigurableBonusAmounts();

      // Simulate what would happen if someone used this user's referral code
      const simulation = {
        canRefer: true,
        userReferralCode: user.referral_code,
        directBonus: bonusAmounts[1] || 250,
        multiLevelDistribution: Object.entries(bonusAmounts)
          .slice(0, 10) // Show up to 10 levels
          .map(([level, amount]) => ({
            level: parseInt(level),
            amount: amount,
            description: `Level ${level} referrer receives ₹${amount}`
          })),
        totalPossibleDistribution: Object.values(bonusAmounts)
          .slice(0, 10)
          .reduce((sum, amount) => sum + amount, 0),
        note: 'Bonuses are only distributed when the referred user becomes premium'
      };

      return simulation;
    } catch (error) {
      console.error('Error simulating referral bonus:', error);
      return null;
    }
  }

  // Log referral actions for audit trail
  private async logReferralAction(userId: string, action: string, details: any): Promise<void> {
    try {
      await supabase
        .from('referral_audit_log')
        .insert({
          user_id: userId,
          action,
          details,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Error logging referral action:', error);
    }
  }
}

export const referralService = new ReferralService();
