import { supabase } from '../lib/supabase';
import { referralService } from './referralService';
import { referralCodeGenerator } from './referralCodeGenerator';

export interface PremiumSubscription {
  id: string;
  user_id: string;
  plan_type: 'monthly' | 'yearly';
  amount: number;
  start_date: string;
  end_date: string;
  status: 'active' | 'expired' | 'cancelled';
  payment_method: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
}

export interface PremiumPlan {
  id: string;
  name: string;
  price: number;
  duration: number; // in months
  features: string[];
  popular?: boolean;
}

class PremiumService {
  // Get available premium plans
  async getPremiumPlans(): Promise<PremiumPlan[]> {
    try {
      // Get pricing from admin settings
      const { data: monthlyPrice } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'premium_monthly_price')
        .single();

      const monthlyAmount = parseFloat(monthlyPrice?.setting_value || '299');
      const yearlyAmount = monthlyAmount * 12 * 0.8; // 20% discount for yearly

      return [
        {
          id: 'monthly',
          name: 'Monthly Premium',
          price: monthlyAmount,
          duration: 1,
          features: [
            'Generate referral codes',
            'Earn ₹100 per referral',
            'Access to premium products',
            'Priority customer support',
            'Special discounts',
            'Basic wallet access'
          ]
        },
        {
          id: 'yearly',
          name: 'Yearly Premium',
          price: yearlyAmount,
          duration: 12,
          features: [
            'All monthly features',
            '20% discount (2 months free)',
            'Extended referral bonuses',
            'VIP customer support',
            'Exclusive product access',
            'Advanced analytics'
          ],
          popular: true
        }
      ];
    } catch (error) {
      console.error('Error getting premium plans:', error);
      return [];
    }
  }

  // Check if user has active premium subscription (lifetime model)
  async isPremiumActive(userId: string): Promise<boolean> {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('is_premium, premium_lifetime_access')
        .eq('id', userId)
        .single();

      if (!user?.is_premium) return false;

      // For lifetime premium model, check lifetime access flag
      return user.premium_lifetime_access === true;
    } catch (error) {
      console.error('Error checking premium status:', error);
      return false;
    }
  }

  // Subscribe user to premium with NO REFUND policy (Phase 1)
  async subscribeToPremium(
    userId: string,
    planType: 'monthly' | 'yearly',
    paymentMethod: string,
    paymentId: string,
    transactionId?: string
  ): Promise<boolean> {
    try {
      const plans = await this.getPremiumPlans();
      const selectedPlan = plans.find(p => p.id === planType);

      if (!selectedPlan) {
        throw new Error('Invalid plan selected');
      }

      // Use the new Phase 1 premium subscription function with NO REFUND policy
      const { data: result, error } = await supabase.rpc('purchase_premium_subscription', {
        p_user_id: userId,
        p_amount: selectedPlan.price,
        p_payment_method: paymentMethod,
        p_payment_id: paymentId,
        p_transaction_id: transactionId
      });

      if (error) {
        console.error('Premium subscription error:', error);
        throw new Error(error.message);
      }

      if (!result?.success) {
        throw new Error(result?.message || 'Failed to activate premium subscription');
      }

      console.log('Phase 1 Premium Subscription:', {
        subscriptionId: result.subscription_id,
        expiresAt: result.expires_at,
        amountPaid: result.amount_paid,
        refundEligible: result.refund_eligible, // Always false
        policy: 'NO_REFUNDS_ALLOWED'
      });

      // Check if this user was referred and complete the referral
      await referralService.completeReferral(userId);

      // Generate referral code for the new premium user
      await this.ensureReferralCodeForPremiumUser(userId);

      return true;
    } catch (error) {
      console.error('Error subscribing to premium:', error);
      return false;
    }
  }

  // Expire premium subscription (for lifetime model, this is admin-only)
  async expirePremium(userId: string): Promise<void> {
    try {
      // Update user premium status (lifetime model)
      await supabase
        .from('users')
        .update({
          is_premium: false,
          premium_lifetime_access: false
        })
        .eq('id', userId);

      // Update active subscriptions to expired
      await supabase
        .from('premium_subscriptions')
        .update({ payment_status: 'cancelled' })
        .eq('user_id', userId)
        .eq('payment_status', 'completed');

    } catch (error) {
      console.error('Error expiring premium:', error);
    }
  }

  // Ensure premium user has a referral code
  async ensureReferralCodeForPremiumUser(userId: string): Promise<void> {
    try {
      // Get user details
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, email, referral_code, is_premium')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        console.error('Error fetching user for referral code generation:', userError);
        return;
      }

      // Only generate for premium users without referral codes
      if (!user.is_premium) {
        console.log('User is not premium, skipping referral code generation');
        return;
      }

      if (user.referral_code) {
        console.log('User already has referral code:', user.referral_code);
        return;
      }

      // Generate referral code
      const result = await referralCodeGenerator.generateAndAssignForPremiumUser(
        user.id,
        user.email
      );

      if (result.success) {
        console.log('✅ Referral code generated for premium user:', {
          userId: user.id,
          email: user.email,
          referralCode: result.referralCode
        });
      } else {
        console.error('❌ Failed to generate referral code:', result.error);
      }

    } catch (error) {
      console.error('Error in ensureReferralCodeForPremiumUser:', error);
    }
  }

  // Get user's premium subscription details
  async getUserSubscription(userId: string): Promise<PremiumSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('premium_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) return null;
      return data;
    } catch (error) {
      console.error('Error getting user subscription:', error);
      return null;
    }
  }

  // Get premium subscription history
  async getSubscriptionHistory(userId: string): Promise<PremiumSubscription[]> {
    try {
      const { data, error } = await supabase
        .from('premium_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting subscription history:', error);
      return [];
    }
  }

  // Cancel premium subscription (lifetime model - admin only)
  async cancelSubscription(userId: string): Promise<boolean> {
    try {
      // Note: Premium subscriptions are non-refundable and non-cancellable
      // This method is for admin use only or for future features

      const { error } = await supabase
        .from('premium_subscriptions')
        .update({ payment_status: 'cancelled' })
        .eq('user_id', userId)
        .eq('payment_status', 'completed');

      if (error) throw error;

      // Update user status (lifetime model)
      await supabase
        .from('users')
        .update({
          is_premium: false,
          premium_lifetime_access: false,
          ewallet_unlocked: false,
          wallet_unlocked: false
        })
        .eq('id', userId);

      return true;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return false;
    }
  }

  // Get premium statistics for admin
  async getPremiumStats(): Promise<{
    totalSubscribers: number;
    activeSubscribers: number;
    monthlyRevenue: number;
    yearlyRevenue: number;
    conversionRate: number;
  }> {
    try {
      // Get total subscribers
      const { data: totalSubs } = await supabase
        .from('premium_subscriptions')
        .select('id', { count: 'exact' });

      // Get active subscribers
      const { data: activeSubs } = await supabase
        .from('premium_subscriptions')
        .select('id', { count: 'exact' })
        .eq('status', 'active');

      // Get monthly revenue (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: monthlyRevData } = await supabase
        .from('premium_subscriptions')
        .select('amount')
        .gte('created_at', thirtyDaysAgo.toISOString());

      // Get yearly revenue (last 365 days)
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      const { data: yearlyRevData } = await supabase
        .from('premium_subscriptions')
        .select('amount')
        .gte('created_at', oneYearAgo.toISOString());

      // Get total users for conversion rate
      const { data: totalUsers } = await supabase
        .from('users')
        .select('id', { count: 'exact' });

      const monthlyRevenue = monthlyRevData?.reduce((sum, sub) => sum + sub.amount, 0) || 0;
      const yearlyRevenue = yearlyRevData?.reduce((sum, sub) => sum + sub.amount, 0) || 0;
      const conversionRate = totalUsers?.length ? (activeSubs?.length || 0) / totalUsers.length * 100 : 0;

      return {
        totalSubscribers: totalSubs?.length || 0,
        activeSubscribers: activeSubs?.length || 0,
        monthlyRevenue,
        yearlyRevenue,
        conversionRate
      };
    } catch (error) {
      console.error('Error getting premium stats:', error);
      return {
        totalSubscribers: 0,
        activeSubscribers: 0,
        monthlyRevenue: 0,
        yearlyRevenue: 0,
        conversionRate: 0
      };
    }
  }

  // Process premium upgrade with payment gateway integration
  async processPremiumUpgrade(
    userId: string,
    planType: 'monthly' | 'yearly',
    paymentData: {
      method: string;
      gateway: string;
      amount: number;
      currency: string;
    }
  ): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      // In a real implementation, this would integrate with payment gateways
      // like Razorpay, Stripe, PayU, etc.
      
      // Simulate payment processing
      const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // For demo purposes, assume payment is successful
      const paymentSuccess = true;
      
      if (!paymentSuccess) {
        return { success: false, error: 'Payment failed' };
      }

      // Subscribe user to premium
      const subscriptionSuccess = await this.subscribeToPremium(
        userId,
        planType,
        paymentData.gateway,
        transactionId
      );

      if (!subscriptionSuccess) {
        return { success: false, error: 'Failed to activate premium subscription' };
      }

      return { success: true, transactionId };
    } catch (error) {
      console.error('Error processing premium upgrade:', error);
      return { success: false, error: 'Payment processing failed' };
    }
  }
}

export const premiumService = new PremiumService();
