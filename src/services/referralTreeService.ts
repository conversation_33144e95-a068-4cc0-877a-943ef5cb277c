import { supabase } from '../lib/supabase';

export interface ReferralTreeNode {
  id: string;
  email: string;
  full_name: string;
  referral_code: string;
  is_premium: boolean;
  wallet_balance: number;
  premium_referral_count: number;
  ewallet_unlocked: boolean;
  created_at: string;
  premium_purchased_at?: string;
  level: number;
  children: ReferralTreeNode[];
  parent_id?: string;
  referral_order?: number;
  total_earnings: number;
  direct_referrals: number;
  total_network_size: number;
}

export interface ReferralTreeStats {
  total_users: number;
  total_premium_users: number;
  total_earnings: number;
  max_depth: number;
  total_network_value: number;
}

export class ReferralTreeService {
  // Get complete referral tree for a user
  async getReferralTree(userId: string): Promise<{
    tree: ReferralTreeNode;
    stats: ReferralTreeStats;
  }> {
    try {
      // Get the root user data
      const { data: rootUser, error: rootError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        `)
        .eq('id', userId)
        .single();

      if (rootError || !rootUser) {
        throw new Error('User not found');
      }

      // Build the tree recursively
      const tree = await this.buildTreeNode(rootUser, 0);
      
      // Calculate stats
      const stats = this.calculateTreeStats(tree);

      return { tree, stats };
    } catch (error) {
      console.error('Error getting referral tree:', error);
      throw error;
    }
  }

  // Build tree node recursively
  private async buildTreeNode(user: any, level: number): Promise<ReferralTreeNode> {
    // Get user's total earnings
    const { data: earnings } = await supabase
      .from('wallet_transactions')
      .select('amount')
      .eq('user_id', user.id)
      .eq('reference_type', 'referral');

    const totalEarnings = earnings?.reduce((sum, t) => sum + parseFloat(t.amount), 0) || 0;

    // Get direct referrals
    const { data: directReferrals } = await supabase
      .from('referrals')
      .select(`
        id,
        referral_order,
        referred_user:users!referrals_referred_user_id_fkey(
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        )
      `)
      .eq('referrer_id', user.id)
      .order('referral_order');

    const children: ReferralTreeNode[] = [];

    if (directReferrals) {
      for (const referral of directReferrals) {
        if (referral.referred_user) {
          const childNode = await this.buildTreeNode(referral.referred_user, level + 1);
          childNode.parent_id = user.id;
          childNode.referral_order = referral.referral_order;
          children.push(childNode);
        }
      }
    }

    // Calculate network size (including all descendants)
    const totalNetworkSize = children.reduce((sum, child) => sum + child.total_network_size + 1, 0);

    return {
      id: user.id,
      email: user.email,
      full_name: user.full_name,
      referral_code: user.referral_code,
      is_premium: user.is_premium,
      wallet_balance: parseFloat(user.wallet_balance || '0'),
      premium_referral_count: user.premium_referral_count || 0,
      ewallet_unlocked: user.ewallet_unlocked || false,
      created_at: user.created_at,
      premium_purchased_at: user.premium_purchased_at,
      level,
      children,
      parent_id: user.parent_id,
      referral_order: user.referral_order,
      total_earnings: totalEarnings,
      direct_referrals: children.length,
      total_network_size: totalNetworkSize
    };
  }

  // Calculate tree statistics
  private calculateTreeStats(tree: ReferralTreeNode): ReferralTreeStats {
    let totalUsers = 1;
    let totalPremiumUsers = tree.is_premium ? 1 : 0;
    let totalEarnings = tree.total_earnings;
    let maxDepth = tree.level;
    let totalNetworkValue = tree.wallet_balance;

    const traverse = (node: ReferralTreeNode) => {
      for (const child of node.children) {
        totalUsers++;
        if (child.is_premium) totalPremiumUsers++;
        totalEarnings += child.total_earnings;
        totalNetworkValue += child.wallet_balance;
        maxDepth = Math.max(maxDepth, child.level);
        traverse(child);
      }
    };

    traverse(tree);

    return {
      total_users: totalUsers,
      total_premium_users: totalPremiumUsers,
      total_earnings: totalEarnings,
      max_depth: maxDepth + 1,
      total_network_value: totalNetworkValue
    };
  }

  // Get all root users (users with no referrer)
  async getRootUsers(): Promise<ReferralTreeNode[]> {
    try {
      const { data: rootUsers, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        `)
        .or('referred_by.is.null,referred_by.eq.')
        .order('created_at');

      if (error) throw error;

      const trees: ReferralTreeNode[] = [];
      
      for (const user of rootUsers || []) {
        const tree = await this.buildTreeNode(user, 0);
        trees.push(tree);
      }

      return trees;
    } catch (error) {
      console.error('Error getting root users:', error);
      throw error;
    }
  }

  // Get network overview for admin dashboard
  async getNetworkOverview(): Promise<{
    total_networks: number;
    total_users: number;
    total_premium_users: number;
    total_earnings: number;
    average_network_size: number;
    largest_network: number;
  }> {
    try {
      console.log('Getting network overview...');

      // Get basic user stats first
      const { data: userStats, error: userStatsError } = await supabase
        .from('users')
        .select('id, is_premium, wallet_balance');

      if (userStatsError) {
        console.error('Error getting user stats:', userStatsError);
        throw userStatsError;
      }

      const totalUsers = userStats?.length || 0;
      const totalPremiumUsers = userStats?.filter(u => u.is_premium).length || 0;

      // Get total earnings from wallet transactions
      const { data: earnings, error: earningsError } = await supabase
        .from('wallet_transactions')
        .select('amount')
        .eq('reference_type', 'referral');

      const totalEarnings = earnings?.reduce((sum, t) => sum + parseFloat(t.amount), 0) || 0;

      // Get root users count
      const { data: rootUsers, error: rootError } = await supabase
        .from('users')
        .select('id')
        .or('referred_by.is.null,referred_by.eq.');

      const totalNetworks = rootUsers?.length || 0;

      console.log('Network overview calculated:', {
        total_networks: totalNetworks,
        total_users: totalUsers,
        total_premium_users: totalPremiumUsers,
        total_earnings: totalEarnings
      });

      return {
        total_networks: totalNetworks,
        total_users: totalUsers,
        total_premium_users: totalPremiumUsers,
        total_earnings: totalEarnings,
        average_network_size: totalNetworks > 0 ? totalUsers / totalNetworks : 0,
        largest_network: Math.max(totalUsers, 0)
      };
    } catch (error) {
      console.error('Error getting network overview:', error);
      throw error;
    }
  }

  // Search users in referral network
  async searchInNetwork(query: string): Promise<ReferralTreeNode[]> {
    try {
      console.log('Searching for users with query:', query);

      const { data: users, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          full_name,
          referral_code,
          is_premium,
          wallet_balance,
          premium_referral_count,
          ewallet_unlocked,
          created_at,
          premium_purchased_at
        `)
        .or(`email.ilike.%${query}%,full_name.ilike.%${query}%,referral_code.ilike.%${query}%`)
        .limit(20);

      if (error) {
        console.error('Search query error:', error);
        throw error;
      }

      console.log(`Found ${users?.length || 0} users matching search`);

      const results: ReferralTreeNode[] = [];

      for (const user of users || []) {
        // Get basic user info without building full tree for search results
        const { data: earnings } = await supabase
          .from('wallet_transactions')
          .select('amount')
          .eq('user_id', user.id)
          .eq('reference_type', 'referral');

        const totalEarnings = earnings?.reduce((sum, t) => sum + parseFloat(t.amount), 0) || 0;

        // Get direct referrals count
        const { data: directReferrals } = await supabase
          .from('referrals')
          .select('id')
          .eq('referrer_id', user.id);

        const directReferralsCount = directReferrals?.length || 0;

        results.push({
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          referral_code: user.referral_code,
          is_premium: user.is_premium,
          wallet_balance: parseFloat(user.wallet_balance || '0'),
          premium_referral_count: user.premium_referral_count || 0,
          ewallet_unlocked: user.ewallet_unlocked || false,
          created_at: user.created_at,
          premium_purchased_at: user.premium_purchased_at,
          level: 0,
          children: [],
          total_earnings: totalEarnings,
          direct_referrals: directReferralsCount,
          total_network_size: directReferralsCount
        });
      }

      console.log('Search results processed:', results.length);
      return results;
    } catch (error) {
      console.error('Error searching network:', error);
      throw error;
    }
  }
}

export const referralTreeService = new ReferralTreeService();
