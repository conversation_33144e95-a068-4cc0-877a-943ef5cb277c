// SMS service for sending OTP via SMS
// This includes integrations for popular SMS providers in India

interface SMSConfig {
  provider: 'twilio' | 'aws-sns' | 'textlocal' | 'msg91' | 'fast2sms';
  apiKey: string;
  apiSecret?: string;
  senderId?: string;
}

// SMS configuration (replace with your credentials)
const SMS_CONFIG: SMSConfig = {
  provider: 'msg91', // Popular in India
  apiKey: import.meta.env.VITE_MSG91_API_KEY || 'demo_key', // Use environment variable
  senderId: 'STARTJC' // 6-character sender ID
};

// OTP SMS templates
const OTP_SMS_TEMPLATES = {
  english: `🌿 Start Juicce OTP: {{OTP_CODE}}

Use this code to verify your mobile number. Valid for 10 minutes.

Never share this OTP with anyone.

- Start Juicce Team`,

  hindi: `🌿 Start Juicce OTP: {{OTP_CODE}}

अपना मोबाइल नंबर सत्यापित करने के लिए इस कोड का उपयोग करें। 10 मिनट के लिए वैध।

इस OTP को किसी के साथ साझा न करें।

- Start Juicce Team`
};

// Send OTP via Twilio
export const sendOTPTwilio = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    // This would require Twilio SDK
    /*
    const twilio = require('twilio');
    const client = twilio(SMS_CONFIG.apiKey, SMS_CONFIG.apiSecret);
    
    const message = await client.messages.create({
      body: OTP_SMS_TEMPLATES.english.replace('{{OTP_CODE}}', otp),
      from: '+1234567890', // Your Twilio phone number
      to: `+91${mobile}`
    });
    
    return message.sid ? true : false;
    */
    
    console.log('Twilio would send SMS to:', `+91${mobile}`, 'with OTP:', otp);
    return true;
  } catch (error) {
    console.error('Error sending SMS via Twilio:', error);
    return false;
  }
};

// Send OTP via MSG91 (popular in India) - Updated to handle CORS issues
export const sendOTPMSG91 = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    // Check if API key is configured
    if (!SMS_CONFIG.apiKey || SMS_CONFIG.apiKey === 'demo_key') {
      console.log('MSG91 API key not configured, using demo mode');
      return false; // Will fallback to demo mode
    }

    const message = OTP_SMS_TEMPLATES.english.replace('{{OTP_CODE}}', otp);

    // Note: Direct API calls from browser will fail due to CORS
    // In production, implement this through your backend API
    console.log('MSG91 SMS would be sent to:', mobile, 'with OTP:', otp);
    console.log('Note: MSG91 API disabled due to CORS. Use server-side proxy in production.');

    // For production, use your backend endpoint:
    // const response = await fetch('/api/send-sms', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({
    //     mobile: `91${mobile}`,
    //     otp: otp,
    //     provider: 'msg91'
    //   })
    // });

    return false; // Return false to trigger demo mode
  } catch (error) {
    console.error('Error sending SMS via MSG91:', error);
    return false;
  }
};

// Send OTP via TextLocal (Indian SMS provider)
export const sendOTPTextLocal = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    const message = OTP_SMS_TEMPLATES.english.replace('{{OTP_CODE}}', otp);
    
    const params = new URLSearchParams({
      apikey: SMS_CONFIG.apiKey,
      numbers: `91${mobile}`,
      message: message,
      sender: SMS_CONFIG.senderId || 'TXTLCL'
    });
    
    const response = await fetch('https://api.textlocal.in/send/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    });
    
    const result = await response.json();
    return result.status === 'success';
  } catch (error) {
    console.error('Error sending SMS via TextLocal:', error);
    return false;
  }
};

// Send OTP via Fast2SMS (Indian SMS provider)
export const sendOTPFast2SMS = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    const message = OTP_SMS_TEMPLATES.english.replace('{{OTP_CODE}}', otp);
    
    const response = await fetch('https://www.fast2sms.com/dev/bulkV2', {
      method: 'POST',
      headers: {
        'authorization': SMS_CONFIG.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        route: 'v3',
        sender_id: SMS_CONFIG.senderId,
        message: message,
        language: 'english',
        flash: 0,
        numbers: mobile
      })
    });
    
    const result = await response.json();
    return result.return === true;
  } catch (error) {
    console.error('Error sending SMS via Fast2SMS:', error);
    return false;
  }
};

// Send OTP via AWS SNS
export const sendOTPAWSSNS = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    // This would require AWS SDK
    /*
    const AWS = require('aws-sdk');
    const sns = new AWS.SNS({
      accessKeyId: 'your_access_key',
      secretAccessKey: 'your_secret_key',
      region: 'ap-south-1' // Mumbai region for India
    });
    
    const message = OTP_SMS_TEMPLATES.english.replace('{{OTP_CODE}}', otp);
    
    const params = {
      Message: message,
      PhoneNumber: `+91${mobile}`,
      MessageAttributes: {
        'AWS.SNS.SMS.SenderID': {
          DataType: 'String',
          StringValue: SMS_CONFIG.senderId
        },
        'AWS.SNS.SMS.SMSType': {
          DataType: 'String',
          StringValue: 'Transactional'
        }
      }
    };
    
    const result = await sns.publish(params).promise();
    return result.MessageId ? true : false;
    */
    
    console.log('AWS SNS would send SMS to:', `+91${mobile}`, 'with OTP:', otp);
    return true;
  } catch (error) {
    console.error('Error sending SMS via AWS SNS:', error);
    return false;
  }
};

// Main SMS sending function
export const sendOTPSMS = async (mobile: string, otp: string): Promise<boolean> => {
  try {
    // Validate mobile number format
    if (!isValidIndianMobile(mobile)) {
      throw new Error('Invalid mobile number format');
    }

    // Choose SMS provider based on configuration
    switch (SMS_CONFIG.provider) {
      case 'twilio':
        return await sendOTPTwilio(mobile, otp);
      case 'msg91':
        return await sendOTPMSG91(mobile, otp);
      case 'textlocal':
        return await sendOTPTextLocal(mobile, otp);
      case 'fast2sms':
        return await sendOTPFast2SMS(mobile, otp);
      case 'aws-sns':
        return await sendOTPAWSSNS(mobile, otp);
      default:
        throw new Error('No SMS provider configured');
    }
  } catch (error) {
    console.error('Error in sendOTPSMS:', error);
    return false;
  }
};

// Validate Indian mobile number
export const isValidIndianMobile = (mobile: string): boolean => {
  // Indian mobile numbers: 10 digits starting with 6, 7, 8, or 9
  const mobileRegex = /^[6-9]\d{9}$/;
  return mobileRegex.test(mobile);
};

// Format mobile number for display
export const formatMobileNumber = (mobile: string): string => {
  if (mobile.length === 10) {
    return `+91 ${mobile.slice(0, 5)} ${mobile.slice(5)}`;
  }
  return mobile;
};

// SMS rate limiting (simple in-memory implementation)
const smsRateLimit = new Map<string, { count: number; resetTime: number }>();

export const checkSMSRateLimit = (mobile: string, maxAttempts: number = 3, windowMs: number = 300000): boolean => {
  const now = Date.now();
  const key = mobile;
  
  const current = smsRateLimit.get(key);
  
  if (!current || now > current.resetTime) {
    smsRateLimit.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxAttempts) {
    return false;
  }
  
  current.count++;
  return true;
};

// Clean up expired rate limit entries
export const cleanupSMSRateLimit = (): void => {
  const now = Date.now();
  for (const [key, value] of smsRateLimit.entries()) {
    if (now > value.resetTime) {
      smsRateLimit.delete(key);
    }
  }
};

// Generate SMS-friendly OTP (avoiding confusing characters)
export const generateSMSOTP = (): string => {
  // Use only digits for SMS
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Setup SMS service
export const setupSMSService = () => {
  console.log(`SMS service initialized with provider: ${SMS_CONFIG.provider}`);
  
  // Clean up rate limit entries every 5 minutes
  setInterval(cleanupSMSRateLimit, 5 * 60 * 1000);
};

// Get SMS provider status
export const getSMSProviderStatus = async (): Promise<{ provider: string; status: 'active' | 'inactive'; balance?: number }> => {
  try {
    // This would check the actual provider status
    return {
      provider: SMS_CONFIG.provider,
      status: 'active',
      balance: 1000 // Mock balance
    };
  } catch (error) {
    return {
      provider: SMS_CONFIG.provider,
      status: 'inactive'
    };
  }
};
