import { supabase } from '../lib/supabase';
import { multiLevelReferralService } from './multiLevelReferralService';

export class AutomaticReferralMonitor {
  private static instance: AutomaticReferralMonitor;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  static getInstance(): AutomaticReferralMonitor {
    if (!AutomaticReferralMonitor.instance) {
      AutomaticReferralMonitor.instance = new AutomaticReferralMonitor();
    }
    return AutomaticReferralMonitor.instance;
  }

  // Start automatic monitoring
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('Referral monitoring already active');
      return;
    }

    console.log('Starting automatic referral monitoring...');
    this.isMonitoring = true;

    // Run consistency check every 5 minutes
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.runConsistencyCheck();
      } catch (error) {
        console.error('Monitoring consistency check failed:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Run initial check with delay to avoid startup conflicts
    setTimeout(() => {
      this.runConsistencyCheck();
    }, 3000);
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('Stopped automatic referral monitoring');
  }

  // Run consistency check
  private async runConsistencyCheck(): Promise<void> {
    try {
      // Check if user is authenticated before running checks
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // Skip monitoring when no user is authenticated
        return;
      }

      // Simple consistency check without RPC functions
      console.log('Running referral consistency check...');

      // Check for missing referrals and fix them
      const missingReferrals = await this.checkMissingReferrals();

      if (missingReferrals.length > 0) {
        console.log(`Found ${missingReferrals.length} missing referrals, processing...`);
        // Process a few at a time to avoid overwhelming the system
        const batch = missingReferrals.slice(0, 5);

        for (const user of batch) {
          try {
            await this.processUserReferrals(user.id, user.email);
          } catch (error) {
            console.error(`Failed to process referral for user ${user.email}:`, error);
          }
        }

        // Notify components to refresh data
        this.notifyDataUpdate();
      }

      // Update last check timestamp
      localStorage.setItem('last_referral_check', new Date().toISOString());

    } catch (error) {
      console.error('Consistency check failed:', error);
    }
  }

  // Manually trigger consistency check
  async triggerConsistencyCheck(): Promise<any> {
    try {
      console.log('Starting manual consistency check...');

      // Fix all missing referrals
      const result = await this.fixAllMissingReferrals();

      console.log('Manual consistency check completed:', result);
      this.notifyDataUpdate();

      return result;
    } catch (error) {
      console.error('Manual consistency check failed:', error);
      throw error;
    }
  }

  // Process specific user's referrals using multi-level system
  async processUserReferrals(userId: string, userEmail: string): Promise<any> {
    try {
      console.log('Multi-level referral processing for user:', { userId, userEmail });

      // Check if multi-level referral system is enabled
      const isEnabled = await multiLevelReferralService.isMultiLevelReferralEnabled();
      if (!isEnabled) {
        return { success: false, message: 'Multi-level referral system is disabled' };
      }

      // Get user's referral information
      const { data: userData } = await supabase
        .from('users')
        .select('id, email, full_name, is_premium, referred_by')
        .eq('id', userId)
        .single();

      if (!userData) {
        return { success: false, message: 'User not found' };
      }

      if (!userData.is_premium) {
        return { success: false, message: 'User is not premium - no bonuses will be processed' };
      }

      if (!userData.referred_by) {
        return { success: false, message: 'No referral code found for user' };
      }

      // Check if referral has already been processed
      const { data: existingReferral } = await supabase
        .from('referrals')
        .select('id, status')
        .eq('referred_user_id', userId)
        .single();

      if (existingReferral && existingReferral.status === 'completed') {
        return { success: false, message: 'Referral already processed for this user' };
      }

      // Process the multi-level referral
      const result = await multiLevelReferralService.processMultiLevelReferral(
        userId,
        userEmail,
        userData.referred_by
      );

      if (result.success) {
        console.log('Multi-level referral processed successfully:', {
          userId,
          totalBonusDistributed: result.totalBonusDistributed,
          levelsProcessed: result.levelsProcessed
        });

        this.notifyDataUpdate();

        return {
          success: true,
          message: `Multi-level referral processed successfully`,
          totalBonusDistributed: result.totalBonusDistributed,
          levelsProcessed: result.levelsProcessed,
          referralId: result.referralId
        };
      } else {
        console.error('Multi-level referral processing failed:', result.errors);
        return {
          success: false,
          message: `Referral processing failed: ${result.errors.join(', ')}`,
          errors: result.errors
        };
      }

    } catch (error) {
      console.error('User referral processing failed:', error);
      return {
        success: false,
        message: `Processing failed: ${error.message}`,
        error: error.message
      };
    }
  }

  // Check for missing referrals
  async checkMissingReferrals(): Promise<any[]> {
    try {
      // Get users who have a referral code but no referral record
      const { data: usersWithReferralCode, error: usersError } = await supabase
        .from('users')
        .select('id, email, full_name, referred_by, created_at, is_premium')
        .not('referred_by', 'is', null)
        .neq('referred_by', '')
        .eq('is_premium', true); // Only check premium users

      if (usersError) {
        throw usersError;
      }

      if (!usersWithReferralCode || usersWithReferralCode.length === 0) {
        return [];
      }

      // Check which ones don't have referral records
      const missingReferrals = [];
      for (const user of usersWithReferralCode) {
        const { data: existingReferral } = await supabase
          .from('referrals')
          .select('id')
          .eq('referred_user_id', user.id)
          .single();

        if (!existingReferral) {
          missingReferrals.push(user);
        }
      }

      return missingReferrals;
    } catch (error) {
      console.error('Failed to check missing referrals:', error);
      return [];
    }
  }

  // Fix all missing referrals
  async fixAllMissingReferrals(): Promise<any> {
    try {
      const missingReferrals = await this.checkMissingReferrals();
      
      if (missingReferrals.length === 0) {
        return { success: true, message: 'No missing referrals found', fixed: 0 };
      }

      let fixedCount = 0;
      const errors: any[] = [];

      for (const user of missingReferrals) {
        try {
          await this.processUserReferrals(user.id, user.email);
          fixedCount++;
        } catch (error) {
          errors.push({ userId: user.id, email: user.email, error: error.message });
        }
      }

      this.notifyDataUpdate();

      return {
        success: true,
        message: `Fixed ${fixedCount} missing referrals`,
        fixed: fixedCount,
        total: missingReferrals.length,
        errors
      };
    } catch (error) {
      console.error('Failed to fix missing referrals:', error);
      throw error;
    }
  }

  // Notify components about data updates
  private notifyDataUpdate(): void {
    // Use localStorage to notify other tabs/components
    localStorage.setItem('referral_data_updated', Date.now().toString());
    
    // Dispatch custom event for same-tab components
    window.dispatchEvent(new CustomEvent('referralDataUpdated', {
      detail: { timestamp: Date.now() }
    }));
  }

  // Get monitoring status
  getStatus(): { isMonitoring: boolean; lastCheck?: string } {
    return {
      isMonitoring: this.isMonitoring,
      lastCheck: localStorage.getItem('last_referral_check') || undefined
    };
  }
}

// Export singleton instance
export const automaticReferralMonitor = AutomaticReferralMonitor.getInstance();

// Auto-start monitoring when module loads
if (typeof window !== 'undefined') {
  automaticReferralMonitor.startMonitoring();
}
