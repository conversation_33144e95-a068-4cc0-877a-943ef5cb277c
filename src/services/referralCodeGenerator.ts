import { supabase } from '../lib/supabase';

interface ReferralCodeResult {
  success: boolean;
  referralCode?: string;
  error?: string;
}

interface CodeGenerationOptions {
  prefix?: string;
  length?: number;
  includeUserInfo?: boolean;
  customPattern?: string;
}

class ReferralCodeGenerator {
  private readonly DEFAULT_PREFIX = 'HERB';
  private readonly DEFAULT_LENGTH = 8;
  private readonly MAX_ATTEMPTS = 10;

  // Generate unique referral code with multiple strategies
  async generateUniqueReferralCode(
    userId: string, 
    userEmail: string, 
    options: CodeGenerationOptions = {}
  ): Promise<ReferralCodeResult> {
    try {
      const {
        prefix = this.DEFAULT_PREFIX,
        length = this.DEFAULT_LENGTH,
        includeUserInfo = true,
        customPattern
      } = options;

      let attempts = 0;
      let referralCode: string;

      do {
        attempts++;
        
        if (customPattern) {
          referralCode = this.generateCustomPatternCode(customPattern, userId, userEmail);
        } else if (includeUserInfo) {
          referralCode = this.generatePersonalizedCode(prefix, userId, userEmail, length);
        } else {
          referralCode = this.generateRandomCode(prefix, length);
        }

        // Check if code is unique
        const isUnique = await this.isCodeUnique(referralCode);
        if (isUnique) {
          return {
            success: true,
            referralCode: referralCode
          };
        }

        if (attempts >= this.MAX_ATTEMPTS) {
          throw new Error('Failed to generate unique referral code after maximum attempts');
        }
      } while (attempts < this.MAX_ATTEMPTS);

      return {
        success: false,
        error: 'Failed to generate unique referral code'
      };

    } catch (error) {
      console.error('Error generating referral code:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Generate personalized code based on user info
  private generatePersonalizedCode(prefix: string, userId: string, userEmail: string, length: number): string {
    const emailPart = userEmail.split('@')[0].toUpperCase().replace(/[^A-Z0-9]/g, '');
    const userIdPart = userId.replace(/-/g, '').toUpperCase();
    const timestamp = Date.now().toString(36).toUpperCase();
    
    // Create a mix of email, user ID, and timestamp
    const combined = emailPart + userIdPart + timestamp;
    const randomPart = combined.substring(0, length - prefix.length);
    
    return `${prefix}${randomPart}`;
  }

  // Generate random code
  private generateRandomCode(prefix: string, length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    
    for (let i = prefix.length; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  // Generate code with custom pattern
  private generateCustomPatternCode(pattern: string, userId: string, userEmail: string): string {
    const replacements = {
      '{USER_ID}': userId.substring(0, 8).toUpperCase(),
      '{EMAIL}': userEmail.split('@')[0].toUpperCase().substring(0, 6),
      '{TIMESTAMP}': Date.now().toString(36).toUpperCase(),
      '{RANDOM}': Math.random().toString(36).substring(2, 8).toUpperCase()
    };

    let code = pattern;
    Object.entries(replacements).forEach(([placeholder, value]) => {
      code = code.replace(new RegExp(placeholder, 'g'), value);
    });

    return code;
  }

  // Check if referral code is unique
  private async isCodeUnique(code: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('referral_code', code)
        .limit(1);

      if (error) {
        console.error('Error checking code uniqueness:', error);
        return false;
      }

      return !data || data.length === 0;
    } catch (error) {
      console.error('Error in uniqueness check:', error);
      return false;
    }
  }

  // Assign referral code to user
  async assignReferralCodeToUser(userId: string, referralCode: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          referral_code: referralCode,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('Error assigning referral code:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in assignReferralCodeToUser:', error);
      return false;
    }
  }

  // Generate and assign referral code for premium user
  async generateAndAssignForPremiumUser(userId: string, userEmail: string): Promise<ReferralCodeResult> {
    try {
      // Check if user already has a referral code
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('referral_code, is_premium')
        .eq('id', userId)
        .single();

      if (checkError) {
        return {
          success: false,
          error: 'Failed to check existing user data'
        };
      }

      if (existingUser.referral_code) {
        return {
          success: true,
          referralCode: existingUser.referral_code
        };
      }

      if (!existingUser.is_premium) {
        return {
          success: false,
          error: 'User is not premium'
        };
      }

      // Generate new referral code
      const codeResult = await this.generateUniqueReferralCode(userId, userEmail, {
        prefix: 'HERB',
        length: 10,
        includeUserInfo: true
      });

      if (!codeResult.success || !codeResult.referralCode) {
        return codeResult;
      }

      // Assign code to user
      const assigned = await this.assignReferralCodeToUser(userId, codeResult.referralCode);
      if (!assigned) {
        return {
          success: false,
          error: 'Failed to assign referral code to user'
        };
      }

      // Log the generation
      await this.logReferralCodeGeneration(userId, codeResult.referralCode);

      return {
        success: true,
        referralCode: codeResult.referralCode
      };

    } catch (error) {
      console.error('Error in generateAndAssignForPremiumUser:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Log referral code generation for audit
  private async logReferralCodeGeneration(userId: string, referralCode: string): Promise<void> {
    try {
      await supabase.from('referral_logs').insert({
        user_id: userId,
        action: 'code_generated',
        details: {
          referral_code: referralCode,
          generated_at: new Date().toISOString(),
          generation_method: 'automatic_premium_upgrade'
        },
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error logging referral code generation:', error);
      // Don't fail the main operation if logging fails
    }
  }

  // Bulk generate codes for existing premium users without codes
  async generateCodesForExistingPremiumUsers(): Promise<{ processed: number; successful: number; errors: string[] }> {
    try {
      // Get all premium users without referral codes
      const { data: premiumUsers, error } = await supabase
        .from('users')
        .select('id, email, full_name')
        .eq('is_premium', true)
        .is('referral_code', null);

      if (error) {
        throw new Error('Failed to fetch premium users');
      }

      if (!premiumUsers || premiumUsers.length === 0) {
        return { processed: 0, successful: 0, errors: [] };
      }

      let successful = 0;
      const errors: string[] = [];

      for (const user of premiumUsers) {
        try {
          const result = await this.generateAndAssignForPremiumUser(user.id, user.email);
          if (result.success) {
            successful++;
          } else {
            errors.push(`User ${user.email}: ${result.error}`);
          }
        } catch (error) {
          errors.push(`User ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return {
        processed: premiumUsers.length,
        successful,
        errors
      };

    } catch (error) {
      console.error('Error in bulk generation:', error);
      return {
        processed: 0,
        successful: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  // Validate referral code format
  static validateReferralCodeFormat(code: string): boolean {
    // Must be 6-12 characters, alphanumeric, uppercase
    const pattern = /^[A-Z0-9]{6,12}$/;
    return pattern.test(code);
  }

  // Get referral code statistics
  async getReferralCodeStats(): Promise<any> {
    try {
      const { data: stats, error } = await supabase
        .from('users')
        .select('referral_code, is_premium')
        .not('referral_code', 'is', null);

      if (error) throw error;

      const totalCodes = stats?.length || 0;
      const premiumCodes = stats?.filter(u => u.is_premium).length || 0;
      const nonPremiumCodes = totalCodes - premiumCodes;

      return {
        total_codes: totalCodes,
        premium_user_codes: premiumCodes,
        non_premium_user_codes: nonPremiumCodes,
        code_coverage: totalCodes > 0 ? (premiumCodes / totalCodes * 100).toFixed(2) : 0
      };
    } catch (error) {
      console.error('Error getting referral code stats:', error);
      return null;
    }
  }
}

// Export singleton instance
export const referralCodeGenerator = new ReferralCodeGenerator();
export default referralCodeGenerator;
