import { supabase } from '../lib/supabase';
import { multiLevelReferralService } from './multiLevelReferralService';

// Service for calling Supabase Edge Functions
export class SupabaseEdgeFunctions {
  
  // Process referral using Edge Function
  static async processReferral(referralCode: string, newUserId: string, newUserEmail: string) {
    try {
      const { data, error } = await supabase.functions.invoke('process-referral', {
        body: {
          referralCode,
          newUserId,
          newUserEmail
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error processing referral:', error);
      throw error;
    }
  }

  // Send OTP using Edge Function
  static async sendOTP(contact: string, contactType: 'email' | 'mobile') {
    try {
      const { data, error } = await supabase.functions.invoke('send-otp', {
        body: {
          contact,
          contactType,
          template: 'verification'
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error sending OTP:', error);
      throw error;
    }
  }

  // Process payment using Edge Function
  static async processPayment(
    userId: string,
    amount: number,
    paymentMethod: string,
    orderId?: string,
    paymentGateway = 'razorpay'
  ) {
    try {
      const { data, error } = await supabase.functions.invoke('process-payment', {
        body: {
          userId,
          amount,
          paymentMethod,
          orderId,
          paymentGateway,
          description: `Payment of ₹${amount} via ${paymentMethod}`
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }

  // Send notification email using Edge Function
  static async sendNotificationEmail(
    email: string,
    subject: string,
    message: string,
    template = 'notification'
  ) {
    try {
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          email,
          subject,
          message,
          template
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error sending notification email:', error);
      throw error;
    }
  }

  // Generate analytics report using Edge Function
  static async generateAnalyticsReport(
    reportType: string,
    dateRange: { start: string; end: string },
    filters: any = {}
  ) {
    try {
      const { data, error } = await supabase.functions.invoke('generate-analytics', {
        body: {
          reportType,
          dateRange,
          filters
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error generating analytics report:', error);
      throw error;
    }
  }

  // Process bulk operations using Edge Function
  static async processBulkOperation(
    operation: string,
    data: any[],
    options: any = {}
  ) {
    try {
      const { data: result, error } = await supabase.functions.invoke('bulk-operations', {
        body: {
          operation,
          data,
          options
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return result;
    } catch (error) {
      console.error('Error processing bulk operation:', error);
      throw error;
    }
  }
}

// Database functions service
export class SupabaseDatabaseFunctions {
  
  // Get dashboard stats using database function
  static async getDashboardStats() {
    try {
      const { data, error } = await supabase.rpc('get_dashboard_stats');
      
      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

  // Get user wallet info using database function
  static async getUserWalletInfo(userId: string) {
    try {
      const { data, error } = await supabase.rpc('get_user_wallet_info', {
        p_user_id: userId
      });
      
      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error getting wallet info:', error);
      throw error;
    }
  }

  // Process multi-level referral bonus when user becomes premium
  static async processReferralBonus(
    referrerId: string,
    referredUserId: string,
    bonusAmount = 100.00
  ) {
    try {
      console.log('Multi-level referral bonus processing:', { referrerId, referredUserId, bonusAmount });

      // Check if multi-level referral system is enabled
      const isEnabled = await multiLevelReferralService.isMultiLevelReferralEnabled();
      if (!isEnabled) {
        throw new Error('Multi-level referral system is disabled');
      }

      // Get referred user information
      const { data: referredUser, error: userError } = await supabase
        .from('users')
        .select('id, email, full_name, is_premium, referred_by')
        .eq('id', referredUserId)
        .single();

      if (userError || !referredUser) {
        throw new Error('Referred user not found');
      }

      if (!referredUser.is_premium) {
        throw new Error('User is not premium - no bonuses will be processed');
      }

      if (!referredUser.referred_by) {
        throw new Error('User has no referral code');
      }

      // Get referrer information to validate
      const { data: referrer, error: referrerError } = await supabase
        .from('users')
        .select('id, email, referral_code, is_premium')
        .eq('id', referrerId)
        .single();

      if (referrerError || !referrer) {
        throw new Error('Referrer not found');
      }

      if (!referrer.is_premium) {
        throw new Error('Referrer is not premium');
      }

      // Verify that the referral code matches
      if (referredUser.referred_by !== referrer.referral_code) {
        throw new Error('Referral code mismatch');
      }

      // Process multi-level referral
      const result = await multiLevelReferralService.processMultiLevelReferral(
        referredUserId,
        referredUser.email,
        referrer.referral_code
      );

      if (result.success) {
        return {
          success: true,
          message: 'Multi-level referral bonus processed successfully',
          totalBonusDistributed: result.totalBonusDistributed,
          levelsProcessed: result.levelsProcessed,
          referralId: result.referralId,
          distributions: result.chainUsers.map(user => ({
            userId: user.id,
            level: user.level,
            email: user.email,
            name: user.full_name
          }))
        };
      } else {
        throw new Error(`Multi-level referral processing failed: ${result.errors.join(', ')}`);
      }

    } catch (error) {
      console.error('Error processing multi-level referral bonus:', error);
      throw error;
    }
  }

  // Upgrade user to premium using database function
  static async upgradeToPremium(userId: string, paymentAmount = 299.00) {
    try {
      const { data, error } = await supabase.rpc('upgrade_to_premium', {
        p_user_id: userId,
        p_payment_amount: paymentAmount
      });
      
      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error upgrading to premium:', error);
      throw error;
    }
  }

  // Create notification using database function
  static async createNotification(
    userId: string,
    title: string,
    message: string,
    type = 'info',
    metadata = {}
  ) {
    try {
      const { data, error } = await supabase.rpc('create_notification', {
        p_user_id: userId,
        p_title: title,
        p_message: message,
        p_type: type,
        p_metadata: metadata
      });
      
      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Mark notification as read using database function
  static async markNotificationRead(notificationId: string) {
    try {
      const { data, error } = await supabase.rpc('mark_notification_read', {
        notification_id: notificationId
      });
      
      if (error) {
        throw new Error(error.message);
      }

      return data;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
}
