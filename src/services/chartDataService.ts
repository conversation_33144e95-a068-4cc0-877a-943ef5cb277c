import { supabase } from '../lib/supabase';

export interface RevenueDataPoint {
  label: string;
  value: number;
}

export interface UserDistributionData {
  label: string;
  value: number;
  color: string;
}

class ChartDataService {
  // Get revenue trend data for the last 7 days
  async getRevenueTrendData(): Promise<RevenueDataPoint[]> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('total_amount, created_at')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Group by day and sum revenue
      const dailyRevenue: { [key: string]: number } = {};
      
      // Initialize last 7 days with 0
      for (let i = 6; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        const dateKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        dailyRevenue[dateKey] = 0;
      }

      // Sum actual revenue by day
      data?.forEach(order => {
        const date = new Date(order.created_at);
        const dateKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        if (dailyRevenue.hasOwnProperty(dateKey)) {
          dailyRevenue[dateKey] += order.total_amount;
        }
      });

      return Object.entries(dailyRevenue).map(([label, value]) => ({
        label,
        value
      }));
    } catch (error) {
      console.error('Error fetching revenue trend data:', error);
      // Return sample data on error
      return this.getSampleRevenueData();
    }
  }

  // Get user distribution data
  async getUserDistributionData(): Promise<UserDistributionData[]> {
    try {
      const { data: users, error } = await supabase
        .from('users')
        .select('is_premium, is_admin, kyc_status');

      if (error) throw error;

      const distribution = {
        premium: 0,
        regular: 0,
        admin: 0,
        kyc_pending: 0
      };

      users?.forEach(user => {
        if (user.is_admin) {
          distribution.admin++;
        } else if (user.is_premium) {
          distribution.premium++;
        } else {
          distribution.regular++;
        }

        if (user.kyc_status === 'under_review') {
          distribution.kyc_pending++;
        }
      });

      return [
        {
          label: 'Premium Users',
          value: distribution.premium,
          color: '#10B981' // green
        },
        {
          label: 'Regular Users',
          value: distribution.regular,
          color: '#3B82F6' // blue
        },
        {
          label: 'Admin Users',
          value: distribution.admin,
          color: '#8B5CF6' // purple
        },
        {
          label: 'KYC Pending',
          value: distribution.kyc_pending,
          color: '#F59E0B' // amber
        }
      ].filter(item => item.value > 0); // Only show categories with data
    } catch (error) {
      console.error('Error fetching user distribution data:', error);
      // Return sample data on error
      return this.getSampleUserDistributionData();
    }
  }

  // Get order status distribution
  async getOrderStatusData(): Promise<UserDistributionData[]> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('status');

      if (error) throw error;

      const statusCount: { [key: string]: number } = {};
      data?.forEach(order => {
        statusCount[order.status] = (statusCount[order.status] || 0) + 1;
      });

      const statusColors: { [key: string]: string } = {
        'pending': '#F59E0B',    // amber
        'confirmed': '#3B82F6',  // blue
        'shipped': '#8B5CF6',    // purple
        'delivered': '#10B981',  // green
        'cancelled': '#EF4444'   // red
      };

      return Object.entries(statusCount).map(([status, count]) => ({
        label: status.charAt(0).toUpperCase() + status.slice(1),
        value: count,
        color: statusColors[status] || '#6B7280'
      }));
    } catch (error) {
      console.error('Error fetching order status data:', error);
      return [];
    }
  }

  // Get wallet transaction trends
  async getWalletTrendData(): Promise<RevenueDataPoint[]> {
    try {
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('amount, type, created_at')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Group by day and sum credits
      const dailyCredits: { [key: string]: number } = {};
      
      // Initialize last 7 days with 0
      for (let i = 6; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        const dateKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        dailyCredits[dateKey] = 0;
      }

      // Sum credits by day
      data?.forEach(transaction => {
        if (transaction.type === 'credit') {
          const date = new Date(transaction.created_at);
          const dateKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          if (dailyCredits.hasOwnProperty(dateKey)) {
            dailyCredits[dateKey] += transaction.amount;
          }
        }
      });

      return Object.entries(dailyCredits).map(([label, value]) => ({
        label,
        value
      }));
    } catch (error) {
      console.error('Error fetching wallet trend data:', error);
      return [];
    }
  }

  // Sample data for fallback
  private getSampleRevenueData(): RevenueDataPoint[] {
    return [
      { label: 'Jun 22', value: 150 },
      { label: 'Jun 23', value: 200 },
      { label: 'Jun 24', value: 180 },
      { label: 'Jun 25', value: 300 },
      { label: 'Jun 26', value: 250 },
      { label: 'Jun 27', value: 400 },
      { label: 'Jun 28', value: 350 }
    ];
  }

  private getSampleUserDistributionData(): UserDistributionData[] {
    return [
      { label: 'Premium Users', value: 5, color: '#10B981' },
      { label: 'Regular Users', value: 3, color: '#3B82F6' },
      { label: 'Admin Users', value: 1, color: '#8B5CF6' }
    ];
  }
}

export const chartDataService = new ChartDataService();
