import toast from 'react-hot-toast';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  additionalData?: any;
}

export interface ErrorLog {
  id: string;
  timestamp: Date;
  error: Error;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved: boolean;
}

class ErrorHandler {
  private errorLogs: ErrorLog[] = [];
  private maxLogs = 100;

  // Log error with context
  logError(error: Error, context: ErrorContext = {}, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') {
    const errorLog: ErrorLog = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      error,
      context,
      severity,
      resolved: false
    };

    this.errorLogs.unshift(errorLog);
    
    // Keep only the most recent logs
    if (this.errorLogs.length > this.maxLogs) {
      this.errorLogs = this.errorLogs.slice(0, this.maxLogs);
    }

    // Log to console for development
    console.error(`[${severity.toUpperCase()}] ${context.component || 'Unknown'}:`, error);
    
    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(errorLog);
    }

    return errorLog.id;
  }

  // Handle authentication errors
  handleAuthError(error: any, context: ErrorContext = {}) {
    const authContext = { ...context, component: 'Authentication' };
    
    if (error.message?.includes('Invalid login credentials')) {
      toast.error('Invalid email or password. Please try again.');
      return this.logError(error, authContext, 'low');
    }
    
    if (error.message?.includes('Email not confirmed')) {
      toast.error('Please check your email and confirm your account.');
      return this.logError(error, authContext, 'medium');
    }
    
    if (error.message?.includes('Too many requests')) {
      toast.error('Too many login attempts. Please wait a moment and try again.');
      return this.logError(error, authContext, 'medium');
    }
    
    if (error.message?.includes('Database error')) {
      toast.error('System temporarily unavailable. Please try again later.');
      return this.logError(error, authContext, 'high');
    }

    // Generic auth error
    toast.error('Authentication failed. Please try again.');
    return this.logError(error, authContext, 'medium');
  }

  // Handle database errors
  handleDatabaseError(error: any, context: ErrorContext = {}) {
    const dbContext = { ...context, component: 'Database' };
    
    if (error.code === '23505') { // Unique constraint violation
      toast.error('This information already exists. Please use different values.');
      return this.logError(error, dbContext, 'low');
    }
    
    if (error.code === '23503') { // Foreign key constraint violation
      toast.error('Invalid reference. Please check your data.');
      return this.logError(error, dbContext, 'medium');
    }
    
    if (error.code === '42P01') { // Table does not exist
      toast.error('System configuration error. Please contact support.');
      return this.logError(error, dbContext, 'critical');
    }
    
    if (error.message?.includes('timeout')) {
      toast.error('Request timed out. Please try again.');
      return this.logError(error, dbContext, 'medium');
    }

    // Generic database error
    toast.error('Database operation failed. Please try again.');
    return this.logError(error, dbContext, 'high');
  }

  // Handle payment errors
  handlePaymentError(error: any, context: ErrorContext = {}) {
    const paymentContext = { ...context, component: 'Payment' };
    
    if (error.message?.includes('insufficient funds')) {
      toast.error('Insufficient funds in your wallet.');
      return this.logError(error, paymentContext, 'low');
    }
    
    if (error.message?.includes('payment gateway')) {
      toast.error('Payment gateway error. Please try a different payment method.');
      return this.logError(error, paymentContext, 'high');
    }
    
    if (error.message?.includes('card declined')) {
      toast.error('Payment declined. Please check your card details.');
      return this.logError(error, paymentContext, 'low');
    }

    // Generic payment error
    toast.error('Payment failed. Please try again.');
    return this.logError(error, paymentContext, 'medium');
  }

  // Handle network errors
  handleNetworkError(error: any, context: ErrorContext = {}) {
    const networkContext = { ...context, component: 'Network' };
    
    if (error.message?.includes('Failed to fetch') || error.code === 'NETWORK_ERROR') {
      toast.error('Network connection failed. Please check your internet connection.');
      return this.logError(error, networkContext, 'medium');
    }
    
    if (error.code === 'TIMEOUT') {
      toast.error('Request timed out. Please try again.');
      return this.logError(error, networkContext, 'medium');
    }

    // Generic network error
    toast.error('Network error occurred. Please try again.');
    return this.logError(error, networkContext, 'medium');
  }

  // Handle file upload errors
  handleFileUploadError(error: any, context: ErrorContext = {}) {
    const uploadContext = { ...context, component: 'FileUpload' };
    
    if (error.message?.includes('file too large')) {
      toast.error('File size too large. Please choose a smaller file.');
      return this.logError(error, uploadContext, 'low');
    }
    
    if (error.message?.includes('invalid file type')) {
      toast.error('Invalid file type. Please upload a supported format.');
      return this.logError(error, uploadContext, 'low');
    }
    
    if (error.message?.includes('storage quota')) {
      toast.error('Storage limit exceeded. Please contact support.');
      return this.logError(error, uploadContext, 'high');
    }

    // Generic upload error
    toast.error('File upload failed. Please try again.');
    return this.logError(error, uploadContext, 'medium');
  }

  // Generic error handler
  handleError(error: any, context: ErrorContext = {}) {
    // Try to categorize the error
    if (error.message?.includes('auth') || error.message?.includes('login') || error.message?.includes('token')) {
      return this.handleAuthError(error, context);
    }
    
    if (error.code?.startsWith('23') || error.message?.includes('database') || error.message?.includes('relation')) {
      return this.handleDatabaseError(error, context);
    }
    
    if (error.message?.includes('payment') || error.message?.includes('transaction')) {
      return this.handlePaymentError(error, context);
    }
    
    if (error.message?.includes('fetch') || error.message?.includes('network') || error.code === 'NETWORK_ERROR') {
      return this.handleNetworkError(error, context);
    }
    
    if (error.message?.includes('file') || error.message?.includes('upload')) {
      return this.handleFileUploadError(error, context);
    }

    // Fallback generic error
    const genericContext = { ...context, component: context.component || 'Unknown' };
    toast.error('An unexpected error occurred. Please try again.');
    return this.logError(error, genericContext, 'medium');
  }

  // Get error logs
  getErrorLogs(severity?: 'low' | 'medium' | 'high' | 'critical'): ErrorLog[] {
    if (severity) {
      return this.errorLogs.filter(log => log.severity === severity);
    }
    return [...this.errorLogs];
  }

  // Mark error as resolved
  resolveError(errorId: string) {
    const errorLog = this.errorLogs.find(log => log.id === errorId);
    if (errorLog) {
      errorLog.resolved = true;
    }
  }

  // Clear error logs
  clearLogs() {
    this.errorLogs = [];
  }

  // Send to monitoring service (placeholder)
  private sendToMonitoring(errorLog: ErrorLog) {
    // In production, send to services like:
    // - Sentry
    // - LogRocket
    // - Bugsnag
    // - Custom monitoring endpoint
    
    console.log('Sending to monitoring service:', errorLog);
  }

  // Retry mechanism
  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context: ErrorContext = {}
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          this.handleError(error, { ...context, action: 'retry_failed', additionalData: { attempts: attempt } });
          throw error;
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    
    throw lastError;
  }
}

export const errorHandler = new ErrorHandler();
