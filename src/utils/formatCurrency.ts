/**
 * Currency formatting utilities for the application
 */

/**
 * Format a number as Indian Rupees currency
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number | string,
  options: {
    showSymbol?: boolean;
    showDecimals?: boolean;
    locale?: string;
  } = {}
): string => {
  const {
    showSymbol = true,
    showDecimals = true,
    locale = 'en-IN'
  } = options;

  // Convert string to number if needed
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Handle invalid numbers
  if (isNaN(numericAmount)) {
    return showSymbol ? '₹0.00' : '0.00';
  }

  // Format the number
  const formatter = new Intl.NumberFormat(locale, {
    style: showSymbol ? 'currency' : 'decimal',
    currency: 'INR',
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0,
  });

  let formatted = formatter.format(numericAmount);

  // For Indian locale, replace the currency symbol if needed
  if (showSymbol && locale === 'en-IN') {
    formatted = formatted.replace(/₹|INR/g, '₹');
  }

  return formatted;
};

/**
 * Format currency without symbol
 * @param amount - The amount to format
 * @returns Formatted number string
 */
export const formatAmount = (amount: number | string): string => {
  return formatCurrency(amount, { showSymbol: false });
};

/**
 * Format currency without decimals
 * @param amount - The amount to format
 * @returns Formatted currency string without decimals
 */
export const formatCurrencyWhole = (amount: number | string): string => {
  return formatCurrency(amount, { showDecimals: false });
};

/**
 * Format currency for display in compact form (K, L, Cr)
 * @param amount - The amount to format
 * @returns Compact formatted currency string
 */
export const formatCurrencyCompact = (amount: number | string): string => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) {
    return '₹0';
  }

  if (numericAmount >= 10000000) { // 1 Crore
    return `₹${(numericAmount / 10000000).toFixed(1)}Cr`;
  } else if (numericAmount >= 100000) { // 1 Lakh
    return `₹${(numericAmount / 100000).toFixed(1)}L`;
  } else if (numericAmount >= 1000) { // 1 Thousand
    return `₹${(numericAmount / 1000).toFixed(1)}K`;
  } else {
    return formatCurrency(numericAmount);
  }
};

/**
 * Parse currency string to number
 * @param currencyString - Currency string to parse
 * @returns Numeric value
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString) return 0;
  
  // Remove currency symbols and spaces
  const cleanString = currencyString
    .replace(/[₹,\s]/g, '')
    .replace(/[^\d.-]/g, '');
  
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Add two currency amounts safely
 * @param amount1 - First amount
 * @param amount2 - Second amount
 * @returns Sum of amounts
 */
export const addCurrency = (
  amount1: number | string,
  amount2: number | string
): number => {
  const num1 = typeof amount1 === 'string' ? parseCurrency(amount1) : amount1;
  const num2 = typeof amount2 === 'string' ? parseCurrency(amount2) : amount2;
  
  return Math.round((num1 + num2) * 100) / 100; // Round to 2 decimal places
};

/**
 * Subtract two currency amounts safely
 * @param amount1 - First amount (minuend)
 * @param amount2 - Second amount (subtrahend)
 * @returns Difference of amounts
 */
export const subtractCurrency = (
  amount1: number | string,
  amount2: number | string
): number => {
  const num1 = typeof amount1 === 'string' ? parseCurrency(amount1) : amount1;
  const num2 = typeof amount2 === 'string' ? parseCurrency(amount2) : amount2;
  
  return Math.round((num1 - num2) * 100) / 100; // Round to 2 decimal places
};

/**
 * Multiply currency amount safely
 * @param amount - Amount to multiply
 * @param multiplier - Multiplier
 * @returns Product of amount and multiplier
 */
export const multiplyCurrency = (
  amount: number | string,
  multiplier: number
): number => {
  const num = typeof amount === 'string' ? parseCurrency(amount) : amount;
  
  return Math.round((num * multiplier) * 100) / 100; // Round to 2 decimal places
};

/**
 * Check if amount is valid currency value
 * @param amount - Amount to validate
 * @returns True if valid currency amount
 */
export const isValidCurrency = (amount: number | string): boolean => {
  const num = typeof amount === 'string' ? parseCurrency(amount) : amount;
  return !isNaN(num) && num >= 0 && isFinite(num);
};

/**
 * Format percentage
 * @param value - Percentage value (0-100)
 * @param decimals - Number of decimal places
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  if (isNaN(value)) return '0%';
  return `${value.toFixed(decimals)}%`;
};

/**
 * Calculate percentage of amount
 * @param amount - Base amount
 * @param percentage - Percentage (0-100)
 * @returns Calculated percentage amount
 */
export const calculatePercentage = (
  amount: number | string,
  percentage: number
): number => {
  const num = typeof amount === 'string' ? parseCurrency(amount) : amount;
  return Math.round((num * percentage / 100) * 100) / 100;
};

// Default export
export default formatCurrency;
