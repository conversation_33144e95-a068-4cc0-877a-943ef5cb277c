import { supabase } from '../lib/supabase';

class TokenManager {
  private lastRefreshTime = 0;
  private refreshCooldown = 5000; // 5 seconds between refresh attempts
  private isRefreshing = false;

  // Check if we can refresh the token (rate limiting)
  canRefreshToken(): boolean {
    const now = Date.now();
    return !this.isRefreshing && (now - this.lastRefreshTime) > this.refreshCooldown;
  }

  // Refresh token with rate limiting
  async refreshTokenSafely(): Promise<boolean> {
    if (!this.canRefreshToken()) {
      console.log('Token refresh skipped due to rate limiting');
      return false;
    }

    this.isRefreshing = true;
    this.lastRefreshTime = Date.now();

    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error('Token refresh failed:', error);
        return false;
      }

      console.log('Token refreshed successfully');
      return true;
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  // Check if current session is valid
  async isSessionValid(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        return false;
      }

      // Check if token is expired or will expire soon (within 5 minutes)
      const expiresAt = session.expires_at;
      const now = Math.floor(Date.now() / 1000);
      const fiveMinutes = 5 * 60;

      return expiresAt ? (expiresAt - now) > fiveMinutes : false;
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    }
  }

  // Get current user with session validation
  async getCurrentUserSafely() {
    try {
      // First check if session is valid
      const isValid = await this.isSessionValid();
      
      if (!isValid) {
        // Try to refresh if possible
        const refreshed = await this.refreshTokenSafely();
        if (!refreshed) {
          return { user: null, error: 'Session expired and refresh failed' };
        }
      }

      const { data: { user }, error } = await supabase.auth.getUser();
      return { user, error: error?.message };
    } catch (error: any) {
      return { user: null, error: error.message };
    }
  }

  // Clear all tokens and session data
  async clearSession() {
    try {
      await supabase.auth.signOut();
      this.lastRefreshTime = 0;
      this.isRefreshing = false;
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  }
}

export const tokenManager = new TokenManager();
