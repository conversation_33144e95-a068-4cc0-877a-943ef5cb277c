import { supabase } from '../lib/supabase';
import { mlmReferralService, MLM_CONFIG } from '../services/mlmReferralService';
import { referralService } from '../services/referralService';

export interface TestResult {
  test_name: string;
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

export class ReferralSystemTester {
  
  // Test database connectivity and table structure
  async testDatabaseStructure(): Promise<TestResult> {
    try {
      console.log('🔍 Testing database structure...');
      
      const tables = ['users', 'referrals', 'wallet_transactions', 'admin_settings'];
      const results: any = {};
      
      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);
          
          results[table] = {
            exists: !error,
            error: error?.message || null,
            sample_count: data?.length || 0
          };
        } catch (err) {
          results[table] = {
            exists: false,
            error: err instanceof Error ? err.message : 'Unknown error'
          };
        }
      }
      
      const allTablesExist = Object.values(results).every((result: any) => result.exists);
      
      return {
        test_name: 'Database Structure',
        success: allTablesExist,
        message: allTablesExist ? 'All required tables exist' : 'Some tables are missing',
        details: results
      };
    } catch (error) {
      return {
        test_name: 'Database Structure',
        success: false,
        message: 'Database structure test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Test MLM configuration
  async testMLMConfiguration(): Promise<TestResult> {
    try {
      console.log('⚙️ Testing MLM configuration...');
      
      const config = {
        bonus_amount: MLM_CONFIG.BONUS_AMOUNT,
        direct_referral_limit: MLM_CONFIG.DIRECT_REFERRAL_LIMIT,
        grandparent_referral_position: MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION
      };
      
      // Check if configuration is valid
      const isValid = config.bonus_amount > 0 &&
                     config.direct_referral_limit > 0 &&
                     config.grandparent_referral_position > config.direct_referral_limit;
      
      return {
        test_name: 'MLM Configuration',
        success: isValid,
        message: isValid ? 'MLM configuration is valid' : 'MLM configuration has issues',
        details: config
      };
    } catch (error) {
      return {
        test_name: 'MLM Configuration',
        success: false,
        message: 'MLM configuration test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Test referral code generation
  async testReferralCodeGeneration(): Promise<TestResult> {
    try {
      console.log('🔑 Testing referral code generation...');
      
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return {
          test_name: 'Referral Code Generation',
          success: false,
          message: 'User not authenticated - cannot test referral code generation',
          error: 'Authentication required'
        };
      }
      
      // Try to generate a referral code
      const referralCode = await referralService.generateReferralCode(user.id);
      
      if (referralCode) {
        return {
          test_name: 'Referral Code Generation',
          success: true,
          message: 'Referral code generated successfully',
          details: { referral_code: referralCode }
        };
      } else {
        return {
          test_name: 'Referral Code Generation',
          success: false,
          message: 'Failed to generate referral code',
          error: 'Generation failed'
        };
      }
    } catch (error) {
      return {
        test_name: 'Referral Code Generation',
        success: false,
        message: 'Referral code generation test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Test MLM logic calculation
  async testMLMLogicCalculation(): Promise<TestResult> {
    try {
      console.log('🧮 Testing MLM logic calculation...');
      
      const testCases = [
        { position: 1, expected_recipient: 'direct_referrer', description: '1st referral' },
        { position: 2, expected_recipient: 'direct_referrer', description: '2nd referral' },
        { position: 3, expected_recipient: 'grandparent', description: '3rd referral' },
        { position: 4, expected_recipient: 'direct_referrer', description: '4th referral' },
        { position: 5, expected_recipient: 'direct_referrer', description: '5th referral' }
      ];
      
      const results = testCases.map(testCase => {
        let actualRecipient;
        if (testCase.position <= MLM_CONFIG.DIRECT_REFERRAL_LIMIT) {
          actualRecipient = 'direct_referrer';
        } else if (testCase.position === MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION) {
          actualRecipient = 'grandparent';
        } else {
          actualRecipient = 'direct_referrer'; // 4th+ go back to direct referrer
        }
        
        const isCorrect = actualRecipient === testCase.expected_recipient;
        
        return {
          ...testCase,
          actual_recipient: actualRecipient,
          is_correct: isCorrect
        };
      });
      
      const allCorrect = results.every(result => result.is_correct);
      
      return {
        test_name: 'MLM Logic Calculation',
        success: allCorrect,
        message: allCorrect ? 'All MLM logic calculations are correct' : 'Some MLM logic calculations failed',
        details: results
      };
    } catch (error) {
      return {
        test_name: 'MLM Logic Calculation',
        success: false,
        message: 'MLM logic calculation test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Test user tree retrieval
  async testUserTreeRetrieval(): Promise<TestResult> {
    try {
      console.log('🌳 Testing user tree retrieval...');
      
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return {
          test_name: 'User Tree Retrieval',
          success: false,
          message: 'User not authenticated - cannot test tree retrieval',
          error: 'Authentication required'
        };
      }
      
      // Try to get user's referral tree
      const userTree = await mlmReferralService.getUserReferralTree(user.id);
      
      if (userTree) {
        return {
          test_name: 'User Tree Retrieval',
          success: true,
          message: 'User tree retrieved successfully',
          details: {
            user_id: userTree.id,
            user_name: userTree.name,
            referral_count: userTree.referral_count,
            has_referrer: !!userTree.referrer_id,
            direct_referrals: userTree.direct_referrals.length
          }
        };
      } else {
        return {
          test_name: 'User Tree Retrieval',
          success: false,
          message: 'Failed to retrieve user tree',
          error: 'Tree retrieval failed'
        };
      }
    } catch (error) {
      return {
        test_name: 'User Tree Retrieval',
        success: false,
        message: 'User tree retrieval test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Test wallet transaction creation
  async testWalletTransactionCreation(): Promise<TestResult> {
    try {
      console.log('💰 Testing wallet transaction creation...');
      
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        return {
          test_name: 'Wallet Transaction Creation',
          success: false,
          message: 'User not authenticated - cannot test wallet transactions',
          error: 'Authentication required'
        };
      }
      
      // Check if user exists in users table
      const { data: dbUser, error: userError } = await supabase
        .from('users')
        .select('id, wallet_balance')
        .eq('id', user.id)
        .single();
      
      if (userError || !dbUser) {
        return {
          test_name: 'Wallet Transaction Creation',
          success: false,
          message: 'User not found in database - cannot test wallet transactions',
          error: 'User not found'
        };
      }
      
      // Check if wallet_transactions table is accessible
      const { data: transactions, error: transactionError } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user.id)
        .limit(5);
      
      if (transactionError) {
        return {
          test_name: 'Wallet Transaction Creation',
          success: false,
          message: 'Cannot access wallet transactions table',
          error: transactionError.message
        };
      }
      
      return {
        test_name: 'Wallet Transaction Creation',
        success: true,
        message: 'Wallet transaction system is accessible',
        details: {
          user_wallet_balance: dbUser.wallet_balance,
          recent_transactions: transactions?.length || 0
        }
      };
    } catch (error) {
      return {
        test_name: 'Wallet Transaction Creation',
        success: false,
        message: 'Wallet transaction test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Test admin settings access
  async testAdminSettingsAccess(): Promise<TestResult> {
    try {
      console.log('⚙️ Testing admin settings access...');
      
      // Try to read MLM-related admin settings
      const { data: settings, error } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value, setting_type')
        .in('setting_key', ['mlm_system_enabled', 'mlm_bonus_amount', 'premium_monthly_price']);
      
      if (error) {
        return {
          test_name: 'Admin Settings Access',
          success: false,
          message: 'Cannot access admin settings',
          error: error.message
        };
      }
      
      const mlmSettings = settings?.filter(s => s.setting_key.startsWith('mlm_')) || [];
      const premiumSettings = settings?.filter(s => s.setting_key.includes('premium')) || [];

      return {
        test_name: 'Admin Settings Access',
        success: true,
        message: 'Admin settings accessible',
        details: {
          total_settings: settings?.length || 0,
          mlm_settings: mlmSettings.length,
          premium_settings: premiumSettings.length,
          settings_found: settings?.map(s => s.setting_key) || []
        }
      };
    } catch (error) {
      return {
        test_name: 'Admin Settings Access',
        success: false,
        message: 'Admin settings test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  // Run all tests
  async runAllTests(): Promise<{
    overall_success: boolean;
    total_tests: number;
    passed_tests: number;
    failed_tests: number;
    results: TestResult[];
  }> {
    console.log('🚀 Running comprehensive referral system tests...');
    
    const tests = [
      () => this.testDatabaseStructure(),
      () => this.testMLMConfiguration(),
      () => this.testReferralCodeGeneration(),
      () => this.testMLMLogicCalculation(),
      () => this.testUserTreeRetrieval(),
      () => this.testWalletTransactionCreation(),
      () => this.testAdminSettingsAccess()
    ];
    
    const results: TestResult[] = [];
    
    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
        
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${result.test_name}: ${result.message}`);
        
        if (result.error) {
          console.log(`   Error: ${result.error}`);
        }
      } catch (error) {
        results.push({
          test_name: 'Unknown Test',
          success: false,
          message: 'Test execution failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    const passedTests = results.filter(r => r.success).length;
    const failedTests = results.filter(r => !r.success).length;
    const overallSuccess = failedTests === 0;
    
    console.log('');
    console.log('📊 Test Summary:');
    console.log(`   Total Tests: ${results.length}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Overall: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    return {
      overall_success: overallSuccess,
      total_tests: results.length,
      passed_tests: passedTests,
      failed_tests: failedTests,
      results
    };
  }
}

// Export singleton instance
export const referralSystemTester = new ReferralSystemTester();
