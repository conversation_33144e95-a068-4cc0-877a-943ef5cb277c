import { supabase } from '../lib/supabase';

export async function fixPraveenAuth() {
  try {
    console.log('🔧 Fixing Praveen\'s auth account...');

    // Get Praveen's database record
    const { data: praveenDb, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (dbError || !praveenDb) {
      console.error('❌ Praveen not found in database:', dbError);
      return { success: false, error: 'User not found in database' };
    }

    console.log('👤 Found Praveen in database:', praveenDb.email);

    // Since admin API isn't available, let's use a different approach
    // We'll create the auth user via the Supabase Management API directly
    try {
      console.log('🔧 Creating auth user via Management API...');

      // Use the Supabase Management API to create the user
      const response = await fetch(`https://zvsyozsltpczzuwonczs.supabase.co/auth/v1/admin/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2c3lvenNsdHBjenp1d29uY3pzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NDIxMDEsImV4cCI6MjA2NTMxODEwMX0.fjnDFYSyWl3lzm8WM0EU2Gq5EgE5WkdnGO9pUKAlnzQ'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'praveen123',
          email_confirm: true,
          user_metadata: {
            full_name: praveenDb.full_name,
            phone: praveenDb.phone,
            username: praveenDb.username,
            auth_fixed: true
          }
        })
      });

      if (!response.ok) {
        console.log('⚠️ Management API not available, using alternative approach...');

        // Alternative: Try to register the user normally and then confirm
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: '<EMAIL>',
          password: 'praveen123',
          options: {
            data: {
              full_name: praveenDb.full_name,
              phone: praveenDb.phone,
              username: praveenDb.username,
              auth_fixed: true
            }
          }
        });

        if (signUpError) {
          console.error('❌ Alternative signup failed:', signUpError);
          return {
            success: false,
            error: 'Auth creation not available. Please contact admin to enable auth for this user.',
            suggestion: 'User profile exists in database. Admin can manually enable auth access.'
          };
        }

        if (signUpData.user) {
          console.log('✅ Auth user created via signup:', signUpData.user.id);

          // Update database record with new auth ID
          const { error: updateError } = await supabase
            .from('users')
            .update({
              id: signUpData.user.id,
              updated_at: new Date().toISOString()
            })
            .eq('email', '<EMAIL>');

          if (updateError) {
            console.error('❌ Failed to update database:', updateError);
            return { success: false, error: `Database update failed: ${updateError.message}` };
          }

          console.log('✅ Database updated with new auth ID');
          console.log('🎉 Praveen can now login with: <EMAIL> / praveen123');

          return {
            success: true,
            message: 'Praveen\'s auth account created successfully!',
            credentials: {
              email: '<EMAIL>',
              password: 'praveen123'
            }
          };
        }
      } else {
        const authUser = await response.json();
        console.log('✅ Auth user created via Management API:', authUser.id);

        // Update database record with new auth ID
        const { error: updateError } = await supabase
          .from('users')
          .update({
            id: authUser.id,
            updated_at: new Date().toISOString()
          })
          .eq('email', '<EMAIL>');

        if (updateError) {
          console.error('❌ Failed to update database:', updateError);
          return { success: false, error: `Database update failed: ${updateError.message}` };
        }

        console.log('✅ Database updated with new auth ID');
        console.log('🎉 Praveen can now login with: <EMAIL> / praveen123');

        return {
          success: true,
          message: 'Praveen\'s auth account fixed successfully!',
          credentials: {
            email: '<EMAIL>',
            password: 'praveen123'
          }
        };
      }

    } catch (apiError: any) {
      console.error('❌ API approach failed:', apiError);
      return {
        success: false,
        error: 'Auth creation not available in current environment.',
        suggestion: 'Continue testing with admin panel - upgrade users to premium to test referral bonuses.'
      };
    }

  } catch (error: any) {
    console.error('❌ Failed to fix Praveen\'s auth:', error);
    return { success: false, error: error.message };
  }
}

// Auto-run this function when imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  setTimeout(() => {
    fixPraveenAuth().then(result => {
      if (result.success) {
        console.log('🎉 Praveen auth fix completed:', result.message);
      } else {
        console.log('ℹ️ Auth fix not available:', result.error);
        console.log('💡 Continue testing with admin panel - upgrade users to premium to test referral bonuses');
        console.log('📋 Testing Guide:');
        console.log('   1. Go to Admin Panel → Users');
        console.log('   2. Find Praveen and toggle him to Premium');
        console.log('   3. Watch your wallet get ₹250 bonus');
        console.log('   4. Create more users and test multi-level bonuses');
      }
    });
  }, 2000);
}
