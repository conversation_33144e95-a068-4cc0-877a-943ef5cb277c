import { supabase } from '../lib/supabase';
import { WalletTransaction } from '../types';

export interface WalletOperationResult {
  success: boolean;
  error?: string;
  newBalance?: number;
}

/**
 * Add money to a user's wallet (Admin only)
 */
export const addMoneyToWallet = async (
  userId: string,
  amount: number,
  description: string,
  adminId: string
): Promise<WalletOperationResult> => {
  try {
    // Validate amount
    if (amount <= 0) {
      return { success: false, error: 'Amount must be greater than 0' };
    }

    if (amount > 100000) {
      return { success: false, error: 'Maximum amount is ₹1,00,000 per transaction' };
    }

    // Start a transaction
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('wallet_balance')
      .eq('id', userId)
      .single();

    if (fetchError) {
      return { success: false, error: 'User not found' };
    }

    const currentBalance = currentUser.wallet_balance || 0;
    const newBalance = currentBalance + amount;

    // Add transaction record
    const { error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert({
        user_id: userId,
        type: 'credit',
        amount: amount,
        description: description,
        reference_type: 'admin',
        reference_id: `ADMIN_${adminId}_${Date.now()}`
      });

    if (transactionError) {
      return { success: false, error: 'Failed to create transaction record' };
    }

    // Update user wallet balance
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        wallet_balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      return { success: false, error: 'Failed to update wallet balance' };
    }

    return { success: true, newBalance };
  } catch (error) {
    console.error('Error adding money to wallet:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Deduct money from a user's wallet (for orders, etc.)
 */
export const deductMoneyFromWallet = async (
  userId: string,
  amount: number,
  description: string,
  referenceType: 'order' | 'admin' = 'order',
  referenceId?: string
): Promise<WalletOperationResult> => {
  try {
    // Validate amount
    if (amount <= 0) {
      return { success: false, error: 'Amount must be greater than 0' };
    }

    // Get current balance
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('wallet_balance')
      .eq('id', userId)
      .single();

    if (fetchError) {
      return { success: false, error: 'User not found' };
    }

    const currentBalance = currentUser.wallet_balance || 0;

    if (currentBalance < amount) {
      return { success: false, error: 'Insufficient wallet balance' };
    }

    const newBalance = currentBalance - amount;

    // Add transaction record
    const { error: transactionError } = await supabase
      .from('wallet_transactions')
      .insert({
        user_id: userId,
        type: 'debit',
        amount: amount,
        description: description,
        reference_type: referenceType,
        reference_id: referenceId || `${referenceType.toUpperCase()}_${Date.now()}`
      });

    if (transactionError) {
      return { success: false, error: 'Failed to create transaction record' };
    }

    // Update user wallet balance
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        wallet_balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      return { success: false, error: 'Failed to update wallet balance' };
    }

    return { success: true, newBalance };
  } catch (error) {
    console.error('Error deducting money from wallet:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Get user's current wallet balance
 */
export const getWalletBalance = async (userId: string): Promise<number> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('wallet_balance')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching wallet balance:', error);
      return 0;
    }

    return data.wallet_balance || 0;
  } catch (error) {
    console.error('Error fetching wallet balance:', error);
    return 0;
  }
};

/**
 * Get user's wallet transaction history
 */
export const getWalletTransactions = async (
  userId: string,
  limit?: number
): Promise<WalletTransaction[]> => {
  try {
    let query = supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching wallet transactions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching wallet transactions:', error);
    return [];
  }
};

/**
 * Validate wallet transaction limits
 */
export const validateWalletTransaction = (
  amount: number,
  type: 'credit' | 'debit',
  currentBalance?: number
): { valid: boolean; error?: string } => {
  if (amount <= 0) {
    return { valid: false, error: 'Amount must be greater than 0' };
  }

  if (type === 'credit' && amount > 100000) {
    return { valid: false, error: 'Maximum credit amount is ₹1,00,000 per transaction' };
  }

  if (type === 'debit' && currentBalance !== undefined && currentBalance < amount) {
    return { valid: false, error: 'Insufficient wallet balance' };
  }

  return { valid: true };
};

/**
 * Format currency for display
 */
export const formatCurrency = (amount: number): string => {
  return `₹${amount.toFixed(2)}`;
};

/**
 * Format transaction date for display
 */
export const formatTransactionDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
