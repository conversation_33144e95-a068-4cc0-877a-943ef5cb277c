import { supabase } from '../lib/supabase';

/**
 * Utility functions for authentication management
 */

// Clear all authentication data and reset state
export const clearAllAuthData = async (): Promise<void> => {
  try {
    // Sign out from Supabase
    await supabase.auth.signOut();
    
    // Clear all localStorage items related to Supabase
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('supabase') || key.includes('sb-'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Clear sessionStorage
    const sessionKeysToRemove = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (key.includes('supabase') || key.includes('sb-') || key.includes('lastLoggedUser'))) {
        sessionKeysToRemove.push(key);
      }
    }
    sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));
    
    console.log('✅ All authentication data cleared');
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

// Handle API errors gracefully
export const handleAPIError = (error: any, context: string = 'API call'): void => {
  console.error(`Error in ${context}:`, error);

  if (error?.code === 'PGRST116') {
    console.log('No data found - this is expected in some cases');
    return;
  }

  if (error?.status === 406) {
    console.warn('Content negotiation error - check API headers');
    return;
  }

  if (error?.message?.includes('CORS')) {
    console.warn('CORS error - API call blocked by browser');
    return;
  }
};

// Check if user has valid session
export const hasValidSession = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.error('Session check error:', error);
      return false;
    }
    return !!session;
  } catch (error) {
    console.error('Error checking session:', error);
    return false;
  }
};

// Refresh session if needed
export const refreshSessionIfNeeded = async (): Promise<boolean> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session error:', error);
      await clearAllAuthData();
      return false;
    }
    
    if (!session) {
      return false;
    }
    
    // Check if token is close to expiry (within 5 minutes)
    const expiresAt = session.expires_at;
    const now = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = expiresAt - now;
    
    if (timeUntilExpiry < 300) { // Less than 5 minutes
      const { data, error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) {
        console.error('Token refresh error:', refreshError);
        await clearAllAuthData();
        return false;
      }
      return !!data.session;
    }
    
    return true;
  } catch (error) {
    console.error('Error refreshing session:', error);
    await clearAllAuthData();
    return false;
  }
};

// Initialize auth state on app start
export const initializeAuth = async (): Promise<void> => {
  try {
    // Check for valid session
    const hasSession = await hasValidSession();
    
    if (!hasSession) {
      // Clear any stale auth data
      await clearAllAuthData();
    } else {
      // Refresh session if needed
      await refreshSessionIfNeeded();
    }
  } catch (error) {
    console.error('Error initializing auth:', error);
    await clearAllAuthData();
  }
};

// Safe sign out function
export const safeSignOut = async (): Promise<void> => {
  try {
    await supabase.auth.signOut();
    await clearAllAuthData();
    
    // Redirect to home page
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  } catch (error) {
    console.error('Error during sign out:', error);
    // Force clear even if sign out fails
    await clearAllAuthData();
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }
};

// Get current user safely
export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error('Get user error:', error);
      return null;
    }
    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Check if user is admin
export const isCurrentUserAdmin = async (): Promise<boolean> => {
  try {
    const user = await getCurrentUser();
    if (!user?.email) return false;
    
    return user.email === '<EMAIL>' || user.email === '<EMAIL>';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};
