import { supabase } from '../lib/supabase';

/**
 * Sync auth user with database user profile
 * This handles cases where auth users exist but don't have corresponding database profiles
 */
export const syncAuthWithDatabase = async (authUser: any) => {
  try {
    console.log('🔄 Syncing auth user with database:', authUser.email);

    // First, check if user profile exists in database
    const { data: existingProfile, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', authUser.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('❌ Error fetching user profile:', fetchError);
      throw fetchError;
    }

    if (existingProfile) {
      console.log('✅ User profile found in database');
      return existingProfile;
    }

    // If no profile exists, create one
    console.log('📝 Creating user profile in database...');
    
    const newProfile = {
      id: authUser.id,
      email: authUser.email,
      full_name: authUser.user_metadata?.full_name || authUser.email.split('@')[0],
      username: authUser.email.split('@')[0],
      is_premium: false,
      wallet_balance: 0,
      mobile_verified: false,
      kyc_status: 'pending',
      ewallet_unlocked: false,
      wallet_unlocked: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: createdProfile, error: createError } = await supabase
      .from('users')
      .insert([newProfile])
      .select()
      .single();

    if (createError) {
      console.error('❌ Error creating user profile:', createError);
      throw createError;
    }

    console.log('✅ User profile created successfully');
    return createdProfile;

  } catch (error: any) {
    console.error('❌ Auth sync failed:', error);
    // Don't throw error - allow sign in to continue without profile
    return null;
  }
};

/**
 * Check if there are orphaned database users (users in database but not in auth)
 */
export const findOrphanedUsers = async () => {
  try {
    const { data: dbUsers, error: dbError } = await supabase
      .from('users')
      .select('id, email, full_name');

    if (dbError) throw dbError;

    const orphanedUsers = [];
    
    for (const dbUser of dbUsers || []) {
      // Check if this user exists in auth
      const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(dbUser.id);
      
      if (authError || !authUser.user) {
        orphanedUsers.push(dbUser);
      }
    }

    return orphanedUsers;
  } catch (error) {
    console.error('Error finding orphaned users:', error);
    return [];
  }
};

/**
 * Safe sign in that handles profile sync
 */
export const safeSignIn = async (email: string, password: string) => {
  try {
    console.log('🔐 Starting safe sign in for:', email);

    // Step 1: Authenticate with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError) {
      console.error('❌ Authentication failed:', authError);
      throw authError;
    }

    if (!authData.user) {
      throw new Error('No user data returned from authentication');
    }

    console.log('✅ Authentication successful');

    // Step 2: Sync with database profile
    const profile = await syncAuthWithDatabase(authData.user);

    return {
      user: authData.user,
      profile,
      session: authData.session
    };

  } catch (error: any) {
    console.error('❌ Safe sign in failed:', error);
    throw error;
  }
};
