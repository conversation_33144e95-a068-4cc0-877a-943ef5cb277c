// Image utility functions for handling product images

export const DEFAULT_PRODUCT_IMAGES = [
  'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=400&fit=crop&auto=format', // Herbal tea
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&auto=format', // Green tea
  'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&auto=format', // Herbal supplements
  'https://images.unsplash.com/photo-1505944270255-72b8c68c6a70?w=400&h=400&fit=crop&auto=format', // Natural products
  'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=400&fit=crop&auto=format', // Herbal medicine
  'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=400&fit=crop&auto=format', // Essential oils
  'https://images.unsplash.com/photo-1512290923902-8a9f81dc236c?w=400&h=400&fit=crop&auto=format', // Natural remedies
  'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop&auto=format', // Organic products
  'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop&auto=format', // Herbal extracts
  'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=400&fit=crop&auto=format', // Natural wellness
];

export const DEFAULT_HERO_IMAGE = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&auto=format';

export const DEFAULT_PRODUCT_IMAGE = 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=400&fit=crop&auto=format';

/**
 * Get a random fallback image for products
 */
export const getRandomProductImage = (): string => {
  return DEFAULT_PRODUCT_IMAGES[Math.floor(Math.random() * DEFAULT_PRODUCT_IMAGES.length)];
};

/**
 * Get a safe image URL with fallback
 */
export const getSafeImageUrl = (imageUrl?: string, fallback?: string): string => {
  if (imageUrl && imageUrl.trim() !== '') {
    return imageUrl;
  }
  return fallback || DEFAULT_PRODUCT_IMAGE;
};

/**
 * Handle image load error by setting a fallback
 */
export const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>, fallback?: string) => {
  const target = event.target as HTMLImageElement;
  if (target.src !== (fallback || DEFAULT_PRODUCT_IMAGE)) {
    target.src = fallback || DEFAULT_PRODUCT_IMAGE;
  }
};

/**
 * Preload an image to check if it's valid
 */
export const preloadImage = (src: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = src;
  });
};

/**
 * Get optimized image URL with size parameters
 */
export const getOptimizedImageUrl = (url: string, width: number = 400, height: number = 400): string => {
  // If it's an Unsplash URL, add optimization parameters
  if (url.includes('unsplash.com')) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}w=${width}&h=${height}&fit=crop&auto=format`;
  }
  
  // For other URLs, return as-is
  return url;
};
