import { supabase } from '../lib/supabase';

export interface DatabaseTestResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

export class DatabaseTester {
  
  // Test basic connection
  static async testConnection(): Promise<DatabaseTestResult> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .limit(1);

      if (error) {
        return {
          success: false,
          message: 'Database connection failed',
          error: error.message
        };
      }

      return {
        success: true,
        message: 'Database connection successful',
        details: data
      };
    } catch (error) {
      return {
        success: false,
        message: 'Database connection error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Test authentication
  static async testAuth(): Promise<DatabaseTestResult> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error) {
        return {
          success: false,
          message: 'Auth test failed',
          error: error.message
        };
      }

      return {
        success: true,
        message: user ? 'User authenticated' : 'No user authenticated',
        details: { user: user?.email || 'No user' }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Auth test error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Test table existence
  static async testTables(): Promise<DatabaseTestResult> {
    const tables = ['users', 'products', 'orders', 'categories', 'wallet_transactions'];
    const results: any = {};

    try {
      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('id')
            .limit(1);

          results[table] = {
            exists: !error,
            error: error?.message || null
          };
        } catch (err) {
          results[table] = {
            exists: false,
            error: err instanceof Error ? err.message : 'Unknown error'
          };
        }
      }

      const allTablesExist = Object.values(results).every((result: any) => result.exists);

      return {
        success: allTablesExist,
        message: allTablesExist ? 'All tables exist' : 'Some tables missing',
        details: results
      };
    } catch (error) {
      return {
        success: false,
        message: 'Table test error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Test database functions
  static async testFunctions(): Promise<DatabaseTestResult> {
    try {
      // Test a simple function call
      const { data, error } = await supabase.rpc('get_dashboard_stats');

      if (error) {
        return {
          success: false,
          message: 'Database function test failed',
          error: error.message
        };
      }

      return {
        success: true,
        message: 'Database functions working',
        details: data
      };
    } catch (error) {
      return {
        success: false,
        message: 'Database function test error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Run all tests
  static async runAllTests(): Promise<{
    connection: DatabaseTestResult;
    auth: DatabaseTestResult;
    tables: DatabaseTestResult;
    functions: DatabaseTestResult;
    overall: boolean;
  }> {
    console.log('🔍 Running database tests...');

    const connection = await this.testConnection();
    console.log('📡 Connection test:', connection.success ? '✅' : '❌', connection.message);

    const auth = await this.testAuth();
    console.log('🔐 Auth test:', auth.success ? '✅' : '❌', auth.message);

    const tables = await this.testTables();
    console.log('📋 Tables test:', tables.success ? '✅' : '❌', tables.message);

    const functions = await this.testFunctions();
    console.log('⚙️ Functions test:', functions.success ? '✅' : '❌', functions.message);

    const overall = connection.success && tables.success;
    console.log('🎯 Overall status:', overall ? '✅ Database is working!' : '❌ Database has issues');

    return {
      connection,
      auth,
      tables,
      functions,
      overall
    };
  }
}
