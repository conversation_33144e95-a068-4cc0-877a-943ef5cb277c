import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { referralService } from '../services/referralService';
import { multiLevelReferralService } from '../services/multiLevelReferralService';
import toast from 'react-hot-toast';

export interface ReferralValidation {
  isValid: boolean;
  referrerName?: string;
  bonusAmount?: number;
  error?: string;
}

export interface UseReferralRegistrationReturn {
  referralCode: string;
  setReferralCode: (code: string) => void;
  validatingReferral: boolean;
  referralValidation: ReferralValidation | null;
  validateReferralCode: () => Promise<void>;
  processReferralOnRegistration: (newUserId: string, newUserEmail?: string) => Promise<void>;
  extractReferralFromUrl: () => void;
}

export const useReferralRegistration = (): UseReferralRegistrationReturn => {
  const [referralCode, setReferralCode] = useState('');
  const [validatingReferral, setValidatingReferral] = useState(false);
  const [referralValidation, setReferralValidation] = useState<ReferralValidation | null>(null);

  // Extract referral code from URL parameters on component mount
  useEffect(() => {
    extractReferralFromUrl();
  }, []);

  const extractReferralFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const refCode = urlParams.get('ref') || urlParams.get('referral');
    
    if (refCode) {
      setReferralCode(refCode.toUpperCase());
      // Auto-validate if code is found in URL
      setTimeout(() => {
        validateReferralCode();
      }, 500);
    }
  };

  const validateReferralCode = async () => {
    if (!referralCode.trim()) {
      setReferralValidation(null);
      return;
    }

    setValidatingReferral(true);
    setReferralValidation(null);

    try {
      // Find referrer by code (updated for lifetime premium model)
      const { data: referrer, error: referrerError } = await supabase
        .from('users')
        .select('id, full_name, is_premium, premium_lifetime_access')
        .eq('referral_code', referralCode.toUpperCase())
        .single();

      if (referrerError || !referrer) {
        setReferralValidation({
          isValid: false,
          error: 'Invalid referral code. Please check and try again.'
        });
        return;
      }

      // Check if referrer has lifetime premium access
      const isPremiumActive = referrer.is_premium && referrer.premium_lifetime_access;

      if (!isPremiumActive) {
        setReferralValidation({
          isValid: false,
          error: 'This referral code is from a non-premium user. Only premium users can refer others.'
        });
        return;
      }

      // Get bonus amount from settings
      const { data: bonusSettings } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'referral_bonus_amount')
        .single();

      const bonusAmount = parseFloat(bonusSettings?.setting_value || '100');

      setReferralValidation({
        isValid: true,
        referrerName: referrer.full_name,
        bonusAmount
      });

      toast.success(`Valid referral code! You'll receive ₹${bonusAmount} bonus.`);

    } catch (error) {
      console.error('Error validating referral code:', error);
      setReferralValidation({
        isValid: false,
        error: 'Error validating referral code. Please try again.'
      });
    } finally {
      setValidatingReferral(false);
    }
  };

  const processReferralOnRegistration = async (newUserId: string, newUserEmail?: string) => {
    if (!referralCode.trim() || !referralValidation?.isValid) {
      return;
    }

    try {
      console.log('Multi-level referral processing on registration:', { newUserId, newUserEmail, referralCode });

      // Get user email if not provided
      let userEmail = newUserEmail;
      if (!userEmail) {
        const { data: userData } = await supabase
          .from('users')
          .select('email, is_premium')
          .eq('id', newUserId)
          .single();
        userEmail = userData?.email;
      }

      if (!userEmail) {
        console.error('Could not get user email for referral processing');
        return;
      }

      // Check if user is premium (required for multi-level processing)
      const { data: user } = await supabase
        .from('users')
        .select('is_premium')
        .eq('id', newUserId)
        .single();

      if (!user?.is_premium) {
        // Store referral code for future processing when user becomes premium
        await supabase
          .from('users')
          .update({ referred_by: referralCode })
          .eq('id', newUserId);

        toast.success('Referral code saved! Bonuses will be processed when you upgrade to premium.');
        return;
      }

      // User is premium, process multi-level referral immediately
      const result = await multiLevelReferralService.processMultiLevelReferral(
        newUserId,
        userEmail,
        referralCode
      );

      if (result.success) {
        toast.success(
          `Welcome! Multi-level referral bonuses distributed: ₹${result.totalBonusDistributed} across ${result.levelsProcessed} levels`,
          { duration: 6000 }
        );

        // Store referral relationship
        await supabase
          .from('users')
          .update({ referred_by: referralCode })
          .eq('id', newUserId);

      } else {
        console.error('Multi-level referral processing failed:', result.errors);

        // Still store the referral code for potential future processing
        await supabase
          .from('users')
          .update({ referred_by: referralCode })
          .eq('id', newUserId);

        toast.success('Referral code saved! There was an issue processing bonuses, but they may be processed later.');
      }

    } catch (error) {
      console.error('Error processing referral on registration:', error);

      // Try to at least save the referral code
      try {
        await supabase
          .from('users')
          .update({ referred_by: referralCode })
          .eq('id', newUserId);

        toast.success('Referral code saved! Bonuses will be processed when you upgrade to premium.');
      } catch (saveError) {
        console.error('Failed to save referral code:', saveError);
      }
    }
  };

  return {
    referralCode,
    setReferralCode,
    validatingReferral,
    referralValidation,
    validateReferralCode,
    processReferralOnRegistration,
    extractReferralFromUrl
  };
};
