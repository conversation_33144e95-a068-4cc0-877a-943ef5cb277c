import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

// Generic hook for Supabase Realtime subscriptions with optimization
export function useSupabaseRealtime<T>(
  table: string,
  filter?: { column: string; value: any },
  initialData: T[] = []
) {
  const [data, setData] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let channel: RealtimeChannel;
    let isMounted = true;

    const setupRealtime = async () => {
      try {
        // Initial data fetch
        let query = supabase.from(table).select('*');

        if (filter) {
          query = query.eq(filter.column, filter.value);
        }

        const { data: initialData, error: fetchError } = await query;

        if (fetchError) {
          if (isMounted) {
            setError(fetchError.message);
            setLoading(false);
          }
          return;
        }

        if (isMounted) {
          setData(initialData || []);
          setLoading(false);
        }

        // Setup realtime subscription with unique channel name
        const channelName = `${table}_${filter ? `${filter.column}_${filter.value}` : 'all'}_${Date.now()}`;

        channel = supabase
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: table,
              ...(filter && { filter: `${filter.column}=eq.${filter.value}` })
            },
            (payload) => {
              if (!isMounted) return;

              // Reduced logging to prevent console spam
              if (import.meta.env.DEV) {
                console.log(`📡 ${table} update:`, payload.eventType);
              }

              switch (payload.eventType) {
                case 'INSERT':
                  setData(prev => [...prev, payload.new as T]);
                  break;
                case 'UPDATE':
                  setData(prev =>
                    prev.map(item =>
                      (item as any).id === payload.new.id ? payload.new as T : item
                    )
                  );
                  break;
                case 'DELETE':
                  setData(prev =>
                    prev.filter(item => (item as any).id !== payload.old.id)
                  );
                  break;
              }
            }
          )
          .subscribe();

      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Unknown error');
          setLoading(false);
        }
      }
    };

    setupRealtime();

    return () => {
      isMounted = false;
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, [table, filter?.column, filter?.value]);

  return { data, loading, error, setData };
}

// Specific hooks for different data types
export function useRealtimeWalletTransactions(userId: string) {
  return useSupabaseRealtime(
    'wallet_transactions',
    { column: 'user_id', value: userId }
  );
}

export function useRealtimeNotifications(userId: string) {
  return useSupabaseRealtime(
    'notifications',
    { column: 'user_id', value: userId }
  );
}

export function useRealtimeOrders(userId?: string) {
  return useSupabaseRealtime(
    'orders',
    userId ? { column: 'user_id', value: userId } : undefined
  );
}

export function useRealtimeProducts() {
  return useSupabaseRealtime('products');
}

export function useRealtimeUsers() {
  return useSupabaseRealtime('users');
}

// Hook for real-time dashboard stats
export function useRealtimeDashboardStats() {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const { data, error } = await supabase.rpc('get_dashboard_stats');
        
        if (error) {
          setError(error.message);
        } else {
          setStats(data);
        }
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setLoading(false);
      }
    };

    // Initial fetch
    fetchStats();

    // Setup realtime subscription for stats updates
    const channel = supabase
      .channel('dashboard_stats')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'users' },
        () => fetchStats()
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        () => fetchStats()
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'wallet_transactions' },
        () => fetchStats()
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return { stats, loading, error };
}

// Hook for user wallet info with realtime updates
export function useRealtimeWalletInfo(userId: string) {
  const [walletInfo, setWalletInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) return;

    const fetchWalletInfo = async () => {
      try {
        const { data, error } = await supabase.rpc('get_user_wallet_info', {
          p_user_id: userId
        });
        
        if (error) {
          setError(error.message);
        } else {
          setWalletInfo(data);
        }
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setLoading(false);
      }
    };

    // Initial fetch
    fetchWalletInfo();

    // Setup realtime subscription for wallet updates
    const channel = supabase
      .channel(`wallet_info_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'wallet_transactions',
          filter: `user_id=eq.${userId}`
        },
        () => fetchWalletInfo()
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${userId}`
        },
        () => fetchWalletInfo()
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId]);

  return { walletInfo, loading, error };
}
