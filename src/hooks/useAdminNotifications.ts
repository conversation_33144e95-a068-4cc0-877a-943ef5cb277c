import { useState, useEffect } from 'react';
import { adminNotificationService, AdminNotification } from '../services/adminNotificationService';

export const useAdminNotifications = () => {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Subscribe to notification changes
    const unsubscribe = adminNotificationService.subscribe((newNotifications) => {
      setNotifications(newNotifications);
      setUnreadCount(adminNotificationService.getUnreadCount());
    });

    // Initialize with current notifications
    setNotifications(adminNotificationService.getNotifications());
    setUnreadCount(adminNotificationService.getUnreadCount());

    return unsubscribe;
  }, []);

  const markAsRead = (notificationId: string) => {
    adminNotificationService.markAsRead(notificationId);
  };

  const markAllAsRead = () => {
    adminNotificationService.markAllAsRead();
  };

  const clearNotification = (notificationId: string) => {
    adminNotificationService.clearNotification(notificationId);
  };

  const clearAllNotifications = () => {
    adminNotificationService.clearAllNotifications();
  };

  const createNotification = (data: {
    type: AdminNotification['type'];
    title: string;
    message: string;
    data?: any;
    priority?: AdminNotification['priority'];
  }) => {
    adminNotificationService.createNotification(data);
  };

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    createNotification
  };
};
