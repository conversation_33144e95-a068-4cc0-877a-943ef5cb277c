import { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { superAdminService, AdminPermissions } from '../services/superAdminService';

export interface SuperAdminHook {
  isSuperAdmin: boolean;
  permissions: AdminPermissions;
  hasPermission: (module: string, action: string) => boolean;
  loading: boolean;
  error: string | null;
  refreshPermissions: () => Promise<void>;
}

export const useSuperAdmin = (): SuperAdminHook => {
  const { user } = useAuthStore();
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [permissions, setPermissions] = useState<AdminPermissions>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadPermissions = async () => {
    if (!user?.id) {
      setIsSuperAdmin(false);
      setPermissions({});
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Check if user is super admin
      const superAdminStatus = await superAdminService.isSuperAdmin(user.id);
      setIsSuperAdmin(superAdminStatus);

      // Get user permissions
      const userPermissions = await superAdminService.getUserPermissions(user.id);
      setPermissions(userPermissions);

    } catch (err: any) {
      console.error('Error loading super admin permissions:', err);
      setError(err.message || 'Failed to load permissions');
      setIsSuperAdmin(false);
      setPermissions({});
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = (module: string, action: string): boolean => {
    // Super admins have all permissions
    if (isSuperAdmin) {
      return true;
    }

    const modulePermissions = permissions[module as keyof AdminPermissions];
    if (!modulePermissions) {
      return false;
    }

    return modulePermissions[action as keyof typeof modulePermissions] === true;
  };

  const refreshPermissions = async () => {
    await loadPermissions();
  };

  useEffect(() => {
    loadPermissions();
  }, [user?.id]);

  return {
    isSuperAdmin,
    permissions,
    hasPermission,
    loading,
    error,
    refreshPermissions
  };
};

// Convenience hooks for specific permissions
export const useCanManageUsers = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canCreate: hasPermission('users', 'create'),
    canRead: hasPermission('users', 'read'),
    canUpdate: hasPermission('users', 'update'),
    canDelete: hasPermission('users', 'delete'),
    canManageRoles: hasPermission('users', 'manage_roles')
  };
};

export const useCanManageWallet = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canRead: hasPermission('wallet', 'read'),
    canUpdate: hasPermission('wallet', 'update'),
    canTransfer: hasPermission('wallet', 'transfer'),
    canAdjustBalance: hasPermission('wallet', 'adjust_balance')
  };
};

export const useCanManageProducts = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canCreate: hasPermission('products', 'create'),
    canRead: hasPermission('products', 'read'),
    canUpdate: hasPermission('products', 'update'),
    canDelete: hasPermission('products', 'delete'),
    canManageInventory: hasPermission('products', 'manage_inventory')
  };
};

export const useCanManageOrders = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canRead: hasPermission('orders', 'read'),
    canUpdate: hasPermission('orders', 'update'),
    canRefund: hasPermission('orders', 'refund')
  };
};

export const useCanManageKYC = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canReview: hasPermission('kyc', 'review'),
    canApprove: hasPermission('kyc', 'approve'),
    canReject: hasPermission('kyc', 'reject'),
    canDelete: hasPermission('kyc', 'delete')
  };
};

export const useCanAccessSystem = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canAccessDatabase: hasPermission('system', 'database_access'),
    canBackup: hasPermission('system', 'backup'),
    canRestore: hasPermission('system', 'restore'),
    canManageSettings: hasPermission('system', 'settings')
  };
};

export const useCanViewAnalytics = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canViewAll: hasPermission('analytics', 'view_all'),
    canExport: hasPermission('analytics', 'export'),
    canViewFinancialReports: hasPermission('analytics', 'financial_reports')
  };
};

export const useCanManageNotifications = () => {
  const { hasPermission } = useSuperAdmin();
  return {
    canSend: hasPermission('notifications', 'send'),
    canBroadcast: hasPermission('notifications', 'broadcast'),
    canManageTemplates: hasPermission('notifications', 'manage_templates')
  };
};
