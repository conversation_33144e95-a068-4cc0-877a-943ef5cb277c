import React, { useState } from 'react';
import { MLM_CONFIG } from '../services/mlmReferralService';

const ReferralEarningsGuide: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<number>(1);

  const scenarios = [
    {
      id: 1,
      title: "Your First 2 Referrals",
      description: "When you refer your 1st and 2nd person",
      earnings: [
        { position: 1, referral: "Person A", earner: "You", amount: MLM_CONFIG.BONUS_AMOUNT },
        { position: 2, referral: "Person B", earner: "You", amount: MLM_CONFIG.BONUS_AMOUNT }
      ],
      totalEarnings: MLM_CONFIG.BONUS_AMOUNT * 2,
      color: "green"
    },
    {
      id: 2,
      title: "Your 3rd Referral Only",
      description: "When you refer your 3rd person",
      earnings: [
        { position: 3, referral: "Person C", earner: "Your Referrer", amount: MLM_CONFIG.BONUS_AMOUNT }
      ],
      totalEarnings: 0,
      color: "blue"
    },
    {
      id: 3,
      title: "Your 4th+ Referrals",
      description: "When you refer your 4th, 5th, 6th... person",
      earnings: [
        { position: 4, referral: "Person D", earner: "You", amount: MLM_CONFIG.BONUS_AMOUNT },
        { position: 5, referral: "Person E", earner: "You", amount: MLM_CONFIG.BONUS_AMOUNT }
      ],
      totalEarnings: "Unlimited",
      color: "green"
    },
    {
      id: 4,
      title: "When Others Refer (3rd Level Only)",
      description: "When people you referred make their 3rd referral",
      earnings: [
        { position: 3, referral: "Person A's 3rd referral", earner: "You", amount: MLM_CONFIG.BONUS_AMOUNT },
        { position: 3, referral: "Person B's 3rd referral", earner: "You", amount: MLM_CONFIG.BONUS_AMOUNT }
      ],
      totalEarnings: "Unlimited potential",
      color: "purple"
    }
  ];

  const EarningsCard: React.FC<{ scenario: typeof scenarios[0] }> = ({ scenario }) => (
    <div className={`p-6 rounded-lg border-2 ${
      scenario.color === 'green' ? 'border-green-200 bg-green-50' :
      scenario.color === 'blue' ? 'border-blue-200 bg-blue-50' :
      'border-purple-200 bg-purple-50'
    }`}>
      <h3 className={`text-xl font-bold mb-2 ${
        scenario.color === 'green' ? 'text-green-800' :
        scenario.color === 'blue' ? 'text-blue-800' :
        'text-purple-800'
      }`}>
        {scenario.title}
      </h3>
      <p className="text-gray-600 mb-4">{scenario.description}</p>
      
      <div className="space-y-3">
        {scenario.earnings.map((earning, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
            <div>
              <span className="font-medium">#{earning.position} - {earning.referral}</span>
            </div>
            <div className="text-right">
              <div className="font-bold text-lg">₹{earning.amount}</div>
              <div className="text-sm text-gray-600">to {earning.earner}</div>
            </div>
          </div>
        ))}
      </div>
      
      <div className={`mt-4 p-3 rounded-lg ${
        scenario.color === 'green' ? 'bg-green-100' :
        scenario.color === 'blue' ? 'bg-blue-100' :
        'bg-purple-100'
      }`}>
        <div className="text-center">
          <div className="text-sm text-gray-600">Your Total Earnings</div>
          <div className={`text-2xl font-bold ${
            scenario.color === 'green' ? 'text-green-800' :
            scenario.color === 'blue' ? 'text-blue-800' :
            'text-purple-800'
          }`}>
            {typeof scenario.totalEarnings === 'number' ? `₹${scenario.totalEarnings}` : scenario.totalEarnings}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">💰 MLM Referral Earnings Guide</h1>
            <p className="text-gray-600">
              Understand exactly how much money you earn for each referral in the IIT MLM system
            </p>
          </div>

          {/* Quick Summary */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg mb-8">
            <h2 className="text-2xl font-bold mb-4">💡 Quick Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold">₹{MLM_CONFIG.BONUS_AMOUNT}</div>
                <div className="text-blue-100">Per Referral Bonus</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">₹{MLM_CONFIG.BONUS_AMOUNT * 2}</div>
                <div className="text-blue-100">Your First 2 Referrals</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">∞</div>
                <div className="text-blue-100">Unlimited Potential</div>
              </div>
            </div>
          </div>

          {/* MLM Rules */}
          <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg mb-8">
            <h2 className="text-xl font-bold text-yellow-800 mb-4">📋 MLM Rules</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-start gap-3">
                <span className="text-2xl">✅</span>
                <div>
                  <h3 className="font-semibold text-yellow-800">1st & 2nd Referrals</h3>
                  <p className="text-yellow-700 text-sm">You earn ₹{MLM_CONFIG.BONUS_AMOUNT} for each of your first 2 referrals</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-2xl">⬆️</span>
                <div>
                  <h3 className="font-semibold text-yellow-800">3rd Referral Only</h3>
                  <p className="text-yellow-700 text-sm">Your 3rd referral earns ₹{MLM_CONFIG.BONUS_AMOUNT} for your referrer</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-2xl">🔄</span>
                <div>
                  <h3 className="font-semibold text-yellow-800">4th+ Referrals</h3>
                  <p className="text-yellow-700 text-sm">Your 4th+ referrals earn ₹{MLM_CONFIG.BONUS_AMOUNT} for you again</p>
                </div>
              </div>
            </div>
          </div>

          {/* Earnings Scenarios */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">💰 Earnings Scenarios</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {scenarios.map((scenario) => (
                <EarningsCard key={scenario.id} scenario={scenario} />
              ))}
            </div>
          </div>

          {/* Detailed Example */}
          <div className="bg-gray-50 p-6 rounded-lg mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">🎯 Real Example: Rachel's Earnings</h2>
            
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-green-600 mb-2">✅ Rachel's Direct Earnings</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>1st Referral (Chris)</span>
                    <span className="font-bold text-green-600">+₹{MLM_CONFIG.BONUS_AMOUNT}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>2nd Referral (Maria)</span>
                    <span className="font-bold text-green-600">+₹{MLM_CONFIG.BONUS_AMOUNT}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-bold">
                    <span>Rachel's Total</span>
                    <span className="text-green-600">₹{MLM_CONFIG.BONUS_AMOUNT * 2}</span>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-blue-600 mb-2">⬆️ Rachel's 3rd Referral</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>3rd Referral (Peter)</span>
                    <span className="font-bold text-blue-600">₹{MLM_CONFIG.BONUS_AMOUNT} → Rachel's Referrer</span>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-green-600 mb-2">🔄 Rachel's 4th+ Referrals</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>4th Referral (Sarah)</span>
                    <span className="font-bold text-green-600">₹{MLM_CONFIG.BONUS_AMOUNT} → Rachel</span>
                  </div>
                  <div className="flex justify-between">
                    <span>5th Referral (John)</span>
                    <span className="font-bold text-green-600">₹{MLM_CONFIG.BONUS_AMOUNT} → Rachel</span>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-purple-600 mb-2">🔄 When Rachel's Referrals Refer Others (3rd Level Only)</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Chris's 3rd referral</span>
                    <span className="font-bold text-purple-600">+₹{MLM_CONFIG.BONUS_AMOUNT} → Rachel</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Maria's 3rd referral</span>
                    <span className="font-bold text-purple-600">+₹{MLM_CONFIG.BONUS_AMOUNT} → Rachel</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Earning Potential Calculator */}
          <div className="bg-white border-2 border-blue-200 p-6 rounded-lg">
            <h2 className="text-2xl font-bold text-blue-800 mb-4">🧮 Your Earning Potential</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3">Direct Referrals (You Earn)</h3>
                <div className="space-y-2">
                  <div className="flex justify-between p-2 bg-green-50 rounded">
                    <span>2 direct referrals</span>
                    <span className="font-bold">₹{MLM_CONFIG.BONUS_AMOUNT * 2}</span>
                  </div>
                  <div className="flex justify-between p-2 bg-blue-50 rounded">
                    <span>+ Unlimited 3rd+ level bonuses</span>
                    <span className="font-bold">₹{MLM_CONFIG.BONUS_AMOUNT} each</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-3">Requirements</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✅</span>
                    <span>Referred person must become premium</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✅</span>
                    <span>Bonus paid instantly to wallet</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✅</span>
                    <span>No limit on total earnings</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralEarningsGuide;
