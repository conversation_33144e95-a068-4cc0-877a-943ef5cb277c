import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AddProductForm } from '../components/admin/AddProductForm';
import { useProductStore } from '../stores/productStore';

export const AddProductPage: React.FC = () => {
  const navigate = useNavigate();
  const { fetchCategories } = useProductStore();

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleSuccess = () => {
    navigate('/products');
  };

  const handleCancel = () => {
    navigate('/products');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <AddProductForm onSuccess={handleSuccess} onCancel={handleCancel} />
    </div>
  );
}; 