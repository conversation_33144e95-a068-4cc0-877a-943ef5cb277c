import React, { useState, useEffect } from 'react';
import { DatabaseTester, DatabaseTestResult } from '../utils/dbTest';

interface TestResults {
  connection: DatabaseTestResult;
  auth: DatabaseTestResult;
  tables: DatabaseTestResult;
  functions: DatabaseTestResult;
  overall: boolean;
}

const DatabaseTest: React.FC = () => {
  const [results, setResults] = useState<TestResults | null>(null);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    try {
      const testResults = await DatabaseTester.runAllTests();
      setResults(testResults);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    runTests();
  }, []);

  const TestResultCard: React.FC<{ title: string; result: DatabaseTestResult; icon: string }> = ({ title, result, icon }) => (
    <div className={`p-4 rounded-lg border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
      <div className="flex items-center gap-2 mb-2">
        <span className="text-xl">{icon}</span>
        <h3 className="font-semibold">{title}</h3>
        <span className={`text-sm px-2 py-1 rounded ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {result.success ? 'PASS' : 'FAIL'}
        </span>
      </div>
      <p className="text-sm text-gray-600 mb-2">{result.message}</p>
      {result.error && (
        <p className="text-xs text-red-600 bg-red-100 p-2 rounded">
          Error: {result.error}
        </p>
      )}
      {result.details && (
        <details className="text-xs text-gray-500">
          <summary className="cursor-pointer">Details</summary>
          <pre className="mt-2 bg-gray-100 p-2 rounded overflow-auto">
            {JSON.stringify(result.details, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-800">Database Connection Test</h1>
            <button
              onClick={runTests}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Run Tests'}
            </button>
          </div>

          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Running database tests...</p>
            </div>
          )}

          {results && (
            <div className="space-y-4">
              <div className={`p-4 rounded-lg text-center ${results.overall ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <h2 className="text-xl font-bold">
                  {results.overall ? '✅ Database is working properly!' : '❌ Database has issues'}
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <TestResultCard
                  title="Connection Test"
                  result={results.connection}
                  icon="📡"
                />
                <TestResultCard
                  title="Authentication Test"
                  result={results.auth}
                  icon="🔐"
                />
                <TestResultCard
                  title="Tables Test"
                  result={results.tables}
                  icon="📋"
                />
                <TestResultCard
                  title="Functions Test"
                  result={results.functions}
                  icon="⚙️"
                />
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">Environment Info</h3>
                <div className="text-sm text-blue-700">
                  <p>Supabase URL: {import.meta.env.VITE_SUPABASE_URL}</p>
                  <p>Anon Key: {import.meta.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatabaseTest;
