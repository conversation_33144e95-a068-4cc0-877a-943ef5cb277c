import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Grid, List } from 'lucide-react';
import { useProductStore } from '../stores/productStore';
import { ProductCard } from '../components/products/ProductCard';
import { ProductFilters } from '../components/products/ProductFilters';

const ProductsPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  const { 
    isLoading, 
    setSearchQuery, 
    getFilteredProducts,
    fetchProducts,
    fetchCategories
  } = useProductStore();

  const filteredProducts = getFilteredProducts();

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    
    // Set search query from URL params
    const searchQuery = searchParams.get('search');
    if (searchQuery) {
      setSearchQuery(searchQuery);
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Natural Products
          </h1>
          <p className="text-sm sm:text-base text-gray-600">
            Discover our complete collection of premium natural products for your wellness journey.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Filters Sidebar */}
          <aside className="lg:w-64 flex-shrink-0">
            <ProductFilters
              isOpen={isFiltersOpen}
              onToggle={() => setIsFiltersOpen(!isFiltersOpen)}
            />
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {/* Toolbar */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 bg-white p-4 rounded-lg shadow-sm gap-4">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setIsFiltersOpen(!isFiltersOpen)}
                  className="lg:hidden flex items-center space-x-2 px-4 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors touch-target"
                >
                  <span className="text-sm font-medium">Filters</span>
                </button>
                <span className="text-sm sm:text-base text-gray-600">
                  {filteredProducts.length} products found
                </span>
              </div>

              <div className="flex items-center justify-end space-x-2">
                <span className="text-sm text-gray-500 hidden sm:inline">View:</span>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded touch-target ${
                    viewMode === 'grid'
                      ? 'bg-green-100 text-green-600'
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded touch-target ${
                    viewMode === 'list'
                      ? 'bg-green-100 text-green-600'
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <List className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Products Grid */}
            {isLoading ? (
              <div className="responsive-grid">
                {[...Array(9)].map((_, i) => (
                  <div key={i} className="bg-white rounded-xl shadow-sm p-4 animate-pulse">
                    <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12 sm:py-16 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col items-center justify-center px-4">
                <div className="w-20 h-20 sm:w-28 sm:h-28 bg-green-100 rounded-full mx-auto mb-6 flex items-center justify-center text-green-600">
                  <svg className="w-12 h-12 sm:w-16 sm:h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m0 0H7"></path></svg>
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3">
                  No Products Found
                </h3>
                <p className="text-sm sm:text-base text-gray-600 max-w-md mb-6 sm:mb-8">
                  We couldn't find any products matching your current filters or search terms.
                  Try adjusting your selections or clearing all filters.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    // Note: setFilters function needs to be imported from store
                    fetchProducts();
                  }}
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors shadow-md touch-target"
                >
                  Clear All Filters
                </button>
              </div>
            ) : (
              <div className={`grid gap-4 sm:gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                  : 'grid-cols-1'
              }`}>
                {filteredProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            )}

            {/* Load More Button */}
            {filteredProducts.length > 0 && (
              <div className="text-center mt-8 sm:mt-12">
                <button className="px-6 sm:px-8 py-3 bg-green-600 text-white font-semibold rounded-full hover:bg-green-700 transition-colors touch-target">
                  Load More Products
                </button>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;