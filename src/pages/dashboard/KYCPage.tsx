import React, { useState, useEffect } from 'react';
import { Shield, Upload, FileText, CheckCircle, AlertCircle, Clock, XCircle } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface KYCFormData {
  aadhaarFront: File | null;
  aadhaarBack: File | null;
  panCard: File | null;
  accountNumber: string;
  ifscCode: string;
  accountHolderName: string;
  bankName: string;
}

const KYCPage: React.FC = () => {
  const { user, refreshUser } = useAuthStore();
  const [formData, setFormData] = useState<KYCFormData>({
    aadhaarFront: null,
    aadhaarBack: null,
    panCard: null,
    accountNumber: '',
    ifscCode: '',
    accountHolderName: '',
    bankName: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const getKYCStatusInfo = () => {
    const status = user?.kyc_status || 'pending';
    switch (status) {
      case 'approved':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          text: 'KYC Approved',
          description: 'Your account is fully verified'
        };
      case 'under_review':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          text: 'Under Review',
          description: 'We\'re reviewing your documents'
        };
      case 'rejected':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          text: 'KYC Rejected',
          description: 'Please resubmit your documents'
        };
      default:
        return {
          icon: AlertCircle,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          text: 'KYC Pending',
          description: 'Complete your verification'
        };
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof Pick<KYCFormData, 'aadhaarFront' | 'aadhaarBack' | 'panCard'>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }
      
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Only JPG, PNG, and PDF files are allowed');
        return;
      }

      setFormData(prev => ({ ...prev, [field]: file }));
    }
  };

  const uploadFile = async (file: File, type: string): Promise<string> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${user?.id}/${type}_${Date.now()}.${fileExt}`;

    const { data, error } = await supabase.storage
      .from('kyc-documents')
      .upload(fileName, file);

    if (error) {
      throw new Error(`Failed to upload ${type}: ${error.message}`);
    }

    const { data: { publicUrl } } = supabase.storage
      .from('kyc-documents')
      .getPublicUrl(fileName);

    return publicUrl;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.aadhaarFront || !formData.aadhaarBack || !formData.panCard) {
      toast.error('Please upload all required documents');
      return;
    }

    if (!formData.accountNumber || !formData.ifscCode || !formData.accountHolderName || !formData.bankName) {
      toast.error('Please fill in all banking details');
      return;
    }

    setIsSubmitting(true);
    setIsUploading(true);

    try {
      // Upload documents
      const [aadhaarFrontUrl, aadhaarBackUrl, panCardUrl] = await Promise.all([
        uploadFile(formData.aadhaarFront, 'aadhaar_front'),
        uploadFile(formData.aadhaarBack, 'aadhaar_back'),
        uploadFile(formData.panCard, 'pan_card')
      ]);

      setIsUploading(false);

      // Update user record with KYC data
      const { error } = await supabase
        .from('users')
        .update({
          aadhaar_front_url: aadhaarFrontUrl,
          aadhaar_back_url: aadhaarBackUrl,
          pan_card_url: panCardUrl,
          account_number_encrypted: formData.accountNumber, // In production, encrypt this
          ifsc_code: formData.ifscCode,
          account_holder_name: formData.accountHolderName,
          bank_name: formData.bankName,
          kyc_status: 'under_review',
          kyc_submitted_at: new Date().toISOString()
        })
        .eq('id', user?.id);

      if (error) {
        throw new Error(error.message);
      }

      toast.success('KYC documents submitted successfully! We will review them within 24-48 hours.');
      await refreshUser();
      
    } catch (error) {
      console.error('KYC submission error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit KYC documents');
    } finally {
      setIsSubmitting(false);
      setIsUploading(false);
    }
  };

  const kycStatus = getKYCStatusInfo();

  // If KYC is already approved, show success message
  if (user?.kyc_status === 'approved') {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className={`${kycStatus.bgColor} rounded-xl p-6 text-center`}>
          <kycStatus.icon className={`w-16 h-16 ${kycStatus.color} mx-auto mb-4`} />
          <h2 className={`text-2xl font-bold ${kycStatus.color} mb-2`}>{kycStatus.text}</h2>
          <p className="text-gray-600">{kycStatus.description}</p>
        </div>
      </div>
    );
  }

  // If KYC is under review, show status
  if (user?.kyc_status === 'under_review') {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className={`${kycStatus.bgColor} rounded-xl p-6 text-center`}>
          <kycStatus.icon className={`w-16 h-16 ${kycStatus.color} mx-auto mb-4`} />
          <h2 className={`text-2xl font-bold ${kycStatus.color} mb-2`}>{kycStatus.text}</h2>
          <p className="text-gray-600 mb-4">{kycStatus.description}</p>
          <p className="text-sm text-gray-500">
            Submitted on: {user?.kyc_submitted_at ? new Date(user.kyc_submitted_at).toLocaleDateString() : 'Unknown'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="text-center mb-8">
        <Shield className="w-16 h-16 text-green-600 mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete KYC Verification</h1>
        <p className="text-gray-600">
          Upload your documents to verify your identity and unlock all features
        </p>
      </div>

      {/* Current Status */}
      <div className={`${kycStatus.bgColor} rounded-xl p-4 mb-8`}>
        <div className="flex items-center space-x-3">
          <kycStatus.icon className={`w-6 h-6 ${kycStatus.color}`} />
          <div>
            <h3 className={`font-semibold ${kycStatus.color}`}>{kycStatus.text}</h3>
            <p className="text-sm text-gray-600">{kycStatus.description}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Document Upload Section */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Upload Documents</h2>
          <div className="space-y-6">
            {/* Aadhaar Front */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Aadhaar Card (Front) *
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative">
                {formData.aadhaarFront ? (
                  <div className="space-y-2">
                    <FileText className="w-8 h-8 text-green-600 mx-auto" />
                    <p className="text-sm text-gray-600">{formData.aadhaarFront.name}</p>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, aadhaarFront: null }))}
                      className="text-red-600 hover:text-red-700 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                    <p className="text-xs text-gray-500">JPG, PNG, PDF (max 5MB)</p>
                  </div>
                )}
                <input
                  type="file"
                  accept=".jpg,.jpeg,.png,.pdf"
                  onChange={(e) => handleFileChange(e, 'aadhaarFront')}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            </div>

            {/* Aadhaar Back */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Aadhaar Card (Back) *
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative">
                {formData.aadhaarBack ? (
                  <div className="space-y-2">
                    <FileText className="w-8 h-8 text-green-600 mx-auto" />
                    <p className="text-sm text-gray-600">{formData.aadhaarBack.name}</p>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, aadhaarBack: null }))}
                      className="text-red-600 hover:text-red-700 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                    <p className="text-xs text-gray-500">JPG, PNG, PDF (max 5MB)</p>
                  </div>
                )}
                <input
                  type="file"
                  accept=".jpg,.jpeg,.png,.pdf"
                  onChange={(e) => handleFileChange(e, 'aadhaarBack')}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            </div>

            {/* PAN Card */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                PAN Card *
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative">
                {formData.panCard ? (
                  <div className="space-y-2">
                    <FileText className="w-8 h-8 text-green-600 mx-auto" />
                    <p className="text-sm text-gray-600">{formData.panCard.name}</p>
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, panCard: null }))}
                      className="text-red-600 hover:text-red-700 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                    <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                    <p className="text-xs text-gray-500">JPG, PNG, PDF (max 5MB)</p>
                  </div>
                )}
                <input
                  type="file"
                  accept=".jpg,.jpeg,.png,.pdf"
                  onChange={(e) => handleFileChange(e, 'panCard')}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Banking Details Section */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Banking Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Account Number *
              </label>
              <input
                type="text"
                value={formData.accountNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, accountNumber: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter account number"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                IFSC Code *
              </label>
              <input
                type="text"
                value={formData.ifscCode}
                onChange={(e) => setFormData(prev => ({ ...prev, ifscCode: e.target.value.toUpperCase() }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter IFSC code"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Account Holder Name *
              </label>
              <input
                type="text"
                value={formData.accountHolderName}
                onChange={(e) => setFormData(prev => ({ ...prev, accountHolderName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter account holder name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Name *
              </label>
              <input
                type="text"
                value={formData.bankName}
                onChange={(e) => setFormData(prev => ({ ...prev, bankName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter bank name"
                required
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting || !formData.aadhaarFront || !formData.aadhaarBack || !formData.panCard}
          className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target"
        >
          {isUploading ? 'Uploading Documents...' : isSubmitting ? 'Submitting...' : 'Submit KYC Documents'}
        </button>
      </form>
    </div>
  );
};

export default KYCPage;
