import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Wallet, 
  ShoppingBag, 
  Users, 
  Crown, 
  Plus, 
  Eye, 
  FileCheck, 
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Share2,
  Copy,
  TrendingUp,
  Gift,
  History,
  RefreshCw
} from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import { useRealtimeWalletTransactions } from '../../hooks/useSupabaseRealtime';
import toast from 'react-hot-toast';

interface DashboardStats {
  walletBalance: number;
  totalOrders: number;
  referralCount: number;
  totalSpent: number;
  referralEarnings: number;
}

interface Transaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  created_at: string;
}

interface Referral {
  id: string;
  referred_user_id: string;
  bonus_amount: number;
  status: string;
  created_at: string;
  referred_user?: {
    full_name: string;
    email: string;
  };
}

const DashboardOverview: React.FC = () => {
  const { user, refreshUser } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats>({
    walletBalance: 0,
    totalOrders: 0,
    referralCount: 0,
    totalSpent: 0,
    referralEarnings: 0
  });
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  const [recentReferrals, setRecentReferrals] = useState<Referral[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Real-time wallet transactions
  const { data: realtimeTransactions, loading: transactionsLoading } = useRealtimeWalletTransactions(user?.id || '');

  useEffect(() => {
    if (user?.id) {
      loadDashboardData();
      // Refresh user data to ensure premium status is up to date
      refreshUser();
    }
  }, [user?.id]);

  // Update recent transactions when real-time data changes
  useEffect(() => {
    if (realtimeTransactions && realtimeTransactions.length > 0) {
      // Take the 5 most recent transactions
      const recent = realtimeTransactions
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5);
      setRecentTransactions(recent);

      // Also update wallet balance in stats
      if (user?.id) {
        loadStats(); // Refresh stats to get updated wallet balance
      }
    }
  }, [realtimeTransactions, user?.id]);

  // Auto-refresh transactions every 30 seconds as fallback
  useEffect(() => {
    if (!user?.id) return;

    const interval = setInterval(() => {
      console.log('Auto-refreshing transactions...');
      loadRecentTransactions();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [user?.id]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadStats(),
        loadRecentTransactions(),
        loadRecentReferrals()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await Promise.all([
        refreshUser(),
        loadDashboardData(),
        loadRecentTransactions() // Force reload transactions
      ]);
      toast.success('Dashboard refreshed successfully');
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      toast.error('Failed to refresh dashboard');
    } finally {
      setRefreshing(false);
    }
  };

  const loadStats = async () => {
    if (!user?.id) return;

    try {
      // Get fresh wallet balance from database
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('wallet_balance')
        .eq('id', user.id)
        .single();

      if (userError) throw userError;
      const walletBalance = userData.wallet_balance || 0;

      // Get total orders (mock for now - replace with real orders table)
      const totalOrders = 0; // TODO: Implement when orders table exists

      // Get referral count
      const { count: referralCount } = await supabase
        .from('referrals')
        .select('*', { count: 'exact', head: true })
        .eq('referrer_id', user.id);

      // Get total spent from debit transactions
      const { data: debitTransactions } = await supabase
        .from('wallet_transactions')
        .select('amount')
        .eq('user_id', user.id)
        .eq('type', 'debit');

      const totalSpent = debitTransactions?.reduce((sum, t) => sum + t.amount, 0) || 0;

      // Get referral earnings
      const { data: referralTransactions } = await supabase
        .from('wallet_transactions')
        .select('amount')
        .eq('user_id', user.id)
        .eq('reference_type', 'referral');

      const referralEarnings = referralTransactions?.reduce((sum, t) => sum + t.amount, 0) || 0;

      setStats({
        walletBalance,
        totalOrders,
        referralCount: referralCount || 0,
        totalSpent,
        referralEarnings
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const loadRecentTransactions = async () => {
    if (!user?.id) return;

    try {
      console.log('Loading recent transactions for user:', user.id);
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('id, type, amount, description, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      console.log('Loaded transactions:', data);
      setRecentTransactions(data || []);
    } catch (error) {
      console.error('Error loading recent transactions:', error);
    }
  };

  const loadRecentReferrals = async () => {
    if (!user?.id) return;

    try {
      const { data, error } = await supabase
        .from('referrals')
        .select(`
          id,
          referred_user_id,
          bonus_amount,
          status,
          created_at,
          referred_user:users!referrals_referred_user_id_fkey(full_name, email)
        `)
        .eq('referrer_id', user.id)
        .order('created_at', { ascending: false })
        .limit(3);

      if (error) throw error;
      setRecentReferrals(data || []);
    } catch (error) {
      console.error('Error loading recent referrals:', error);
    }
  };

  const copyReferralCode = () => {
    if (user?.referral_code) {
      navigator.clipboard.writeText(user.referral_code);
      toast.success('Referral code copied to clipboard!');
    }
  };

  const shareReferralCode = () => {
    if (user?.referral_code) {
      const shareText = `Join Start Juicce with my referral code: ${user.referral_code}`;
      if (navigator.share) {
        navigator.share({
          title: 'Join Start Juicce',
          text: shareText,
          url: window.location.origin
        });
      } else {
        navigator.clipboard.writeText(shareText);
        toast.success('Referral message copied to clipboard!');
      }
    }
  };

  const getKYCStatusInfo = () => {
    const status = user?.kyc_status || 'pending';
    switch (status) {
      case 'approved':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          text: 'KYC Approved',
          description: 'Your account is fully verified'
        };
      case 'under_review':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          text: 'Under Review',
          description: 'We\'re reviewing your documents'
        };
      case 'rejected':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          text: 'KYC Rejected',
          description: 'Please resubmit your documents'
        };
      default:
        return {
          icon: AlertCircle,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          text: 'KYC Pending',
          description: 'Complete your verification'
        };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const kycStatus = getKYCStatusInfo();

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            Welcome back, {user?.full_name?.split(' ')[0] || 'User'}! 👋
          </h1>
          <p className="text-gray-600 mt-1">
            Here's what's happening with your Start Juicce account
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors touch-target"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* KYC Status Banner */}
      <div className={`${kycStatus.bgColor} rounded-xl p-4`}>
        <div className="flex items-center space-x-3">
          <kycStatus.icon className={`w-6 h-6 ${kycStatus.color}`} />
          <div className="flex-1">
            <h3 className={`font-semibold ${kycStatus.color}`}>{kycStatus.text}</h3>
            <p className="text-sm text-gray-600">{kycStatus.description}</p>
          </div>
          {user?.kyc_status !== 'approved' && (
            <Link
              to="/dashboard/kyc"
              className="bg-white px-4 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors touch-target"
            >
              Complete KYC
            </Link>
          )}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Wallet Balance - Premium Only */}
        {user?.is_premium ? (
          <Link to="/dashboard/wallet" className="bg-white rounded-xl shadow-lg p-6 col-span-2 lg:col-span-1 hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Wallet className="w-6 h-6 text-green-600" />
              </div>
              <Plus className="w-5 h-5 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">{formatCurrency(stats.walletBalance)}</h3>
            <p className="text-sm text-gray-600">Wallet Balance</p>
          </Link>
        ) : (
          <Link to="/dashboard/premium" className="bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-300 rounded-xl shadow-lg p-6 col-span-2 lg:col-span-1 hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-yellow-600" />
              </div>
              <Plus className="w-5 h-5 text-yellow-600" />
            </div>
            <h3 className="text-lg font-bold text-yellow-700">Upgrade to Premium</h3>
            <p className="text-sm text-yellow-600">Unlock wallet features</p>
          </Link>
        )}

        {/* Total Orders */}
        <Link to="/dashboard/orders" className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
            <ShoppingBag className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{stats.totalOrders}</h3>
          <p className="text-sm text-gray-600">Total Orders</p>
        </Link>

        {/* Referrals - Premium Only */}
        {user?.is_premium ? (
          <Link to="/dashboard/referrals" className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.referralCount}</h3>
            <p className="text-sm text-gray-600">Referrals</p>
          </Link>
        ) : (
          <Link to="/dashboard/premium" className="bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-300 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <Crown className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-bold text-purple-700">Premium Feature</h3>
            <p className="text-sm text-purple-600">Unlock referrals</p>
          </Link>
        )}

        {/* Membership Status */}
        <Link to="/dashboard/premium" className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
          <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
            <Crown className="w-6 h-6 text-yellow-600" />
          </div>
          <h3 className="text-lg font-bold text-gray-900">
            {user?.is_premium ? 'Premium' : 'Basic'}
          </h3>
          <p className="text-sm text-gray-600">Membership</p>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
              {/* Add Money - Premium Only */}
              {user?.is_premium ? (
                <Link to="/dashboard/wallet" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors touch-target">
                  <Plus className="w-8 h-8 text-green-600 mb-2" />
                  <span className="text-sm font-medium text-gray-700">Add Money</span>
                </Link>
              ) : (
                <Link to="/dashboard/premium" className="flex flex-col items-center p-4 border border-yellow-300 bg-yellow-50 rounded-lg hover:border-yellow-500 hover:bg-yellow-100 transition-colors touch-target">
                  <Crown className="w-8 h-8 text-yellow-600 mb-2" />
                  <span className="text-sm font-medium text-yellow-700">Add Money</span>
                  <span className="text-xs text-yellow-600">Premium</span>
                </Link>
              )}

              <Link to="/products" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors touch-target">
                <ShoppingBag className="w-8 h-8 text-blue-600 mb-2" />
                <span className="text-sm font-medium text-gray-700">Place Order</span>
              </Link>

              <Link to="/register" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors touch-target">
                <FileCheck className="w-8 h-8 text-purple-600 mb-2" />
                <span className="text-sm font-medium text-gray-700">View KYC</span>
              </Link>

              {/* Refer Friends - Premium Only */}
              {user?.is_premium ? (
                <Link to="/dashboard/referrals" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-yellow-500 hover:bg-yellow-50 transition-colors touch-target">
                  <Gift className="w-8 h-8 text-yellow-600 mb-2" />
                  <span className="text-sm font-medium text-gray-700">Refer Friends</span>
                </Link>
              ) : (
                <Link to="/dashboard/premium" className="flex flex-col items-center p-4 border border-yellow-300 bg-yellow-50 rounded-lg hover:border-yellow-500 hover:bg-yellow-100 transition-colors touch-target">
                  <Crown className="w-8 h-8 text-yellow-600 mb-2" />
                  <span className="text-sm font-medium text-yellow-700">Refer Friends</span>
                  <span className="text-xs text-yellow-600">Premium</span>
                </Link>
              )}

              <Link to="/dashboard/orders" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-gray-500 hover:bg-gray-50 transition-colors touch-target">
                <History className="w-8 h-8 text-gray-600 mb-2" />
                <span className="text-sm font-medium text-gray-700">Order History</span>
              </Link>

              <Link to="/dashboard/premium" className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors touch-target">
                <TrendingUp className="w-8 h-8 text-green-600 mb-2" />
                <span className="text-sm font-medium text-gray-700">{user?.is_premium ? 'Premium Active' : 'Go Premium'}</span>
              </Link>
            </div>
          </div>

          {/* Recent Transactions - Premium Only */}
          {user?.is_premium ? (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Recent Transactions</h2>
                <Link to="/dashboard/wallet" className="text-green-600 hover:text-green-700 font-medium flex items-center space-x-1 touch-target">
                  <Eye className="w-4 h-4" />
                  <span>View All</span>
                </Link>
              </div>

              {recentTransactions.length > 0 ? (
                <div className="space-y-4">
                  {recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          transaction.type === 'credit' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                        }`}>
                          {transaction.type === 'credit' ? <Plus className="w-5 h-5" /> : <ShoppingBag className="w-5 h-5" />}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{transaction.description}</p>
                          <p className="text-sm text-gray-500">{formatDate(transaction.created_at)}</p>
                        </div>
                      </div>
                      <p className={`font-semibold ${
                        transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Wallet className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
                  <p className="text-gray-500">Your transaction history will appear here</p>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gradient-to-br from-yellow-50 to-amber-100 border-2 border-yellow-300 rounded-xl shadow-lg p-6">
              <div className="text-center py-8">
                <Crown className="w-16 h-16 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-yellow-800 mb-2">Premium Feature</h3>
                <p className="text-yellow-700 mb-4">Upgrade to premium to view your transaction history</p>
                <Link
                  to="/dashboard/premium"
                  className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors touch-target"
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Upgrade Now
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Referral Program - Premium Only */}
          {user?.is_premium ? (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Referral Program</h2>

              {user?.referral_code ? (
                <div className="space-y-4">
                  {/* Referral Code */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <p className="text-sm text-gray-600 mb-2">Your Referral Code</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-bold text-green-600">{user.referral_code}</span>
                      <div className="flex space-x-2">
                        <button
                          onClick={copyReferralCode}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors touch-target"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                        <button
                          onClick={shareReferralCode}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors touch-target"
                        >
                          <Share2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Referral Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <p className="text-xl font-bold text-gray-900">{stats.referralCount}</p>
                      <p className="text-sm text-gray-600">People Referred</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xl font-bold text-green-600">{formatCurrency(stats.referralEarnings)}</p>
                      <p className="text-sm text-gray-600">Bonuses Earned</p>
                    </div>
                  </div>

                  {/* Recent Referrals */}
                  {recentReferrals.length > 0 && (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">Recent Referrals</h3>
                      <div className="space-y-2">
                        {recentReferrals.map((referral) => (
                          <div key={referral.id} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">
                              {referral.referred_user?.full_name || 'New User'}
                            </span>
                            <span className="text-green-600 font-medium">
                              +{formatCurrency(referral.bonus_amount)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <Link
                    to="/dashboard/referrals"
                    className="block w-full text-center bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors touch-target"
                  >
                    View All Referrals
                  </Link>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Referral Code</h3>
                  <p className="text-gray-500 mb-4">Complete your KYC to get a referral code</p>
                  <Link
                    to="/register"
                    className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors touch-target"
                  >
                    Complete KYC
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-300 rounded-xl shadow-lg p-6">
              <div className="text-center py-8">
                <Crown className="w-16 h-16 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-purple-800 mb-2">Premium Feature</h3>
                <p className="text-purple-700 mb-4">Upgrade to premium to access the referral program and start earning</p>
                <Link
                  to="/dashboard/premium"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors touch-target"
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Upgrade Now
                </Link>
              </div>
            </div>
          )}

          {/* Order Summary */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Order Summary</h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Total Orders</span>
                <span className="font-semibold text-gray-900">{stats.totalOrders}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Total Spent</span>
                <span className="font-semibold text-gray-900">{formatCurrency(stats.totalSpent)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Average Order</span>
                <span className="font-semibold text-gray-900">
                  {stats.totalOrders > 0 ? formatCurrency(stats.totalSpent / stats.totalOrders) : '₹0.00'}
                </span>
              </div>
            </div>

            <Link
              to="/products"
              className="w-full mt-6 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 touch-target"
            >
              <ShoppingBag className="w-5 h-5" />
              <span>Browse Products</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
