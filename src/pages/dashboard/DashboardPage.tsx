import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import {
  User,
  Package,
  Wallet,
  Crown,
  Users,
  Settings,
  LogOut,
  Star,
  Menu,
  X,
  Home,
  Shield
} from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { useWalletStore } from '../../stores/walletStore';
import DashboardOverview from './DashboardOverview';
import ProfilePage from './ProfilePage';
import OrdersPage from './OrdersPage';
import WalletPage from './WalletPage';
import EnhancedReferralPage from './EnhancedReferralPage';
import PremiumPage from './PremiumPage';
import KYCPage from './KYCPage';
import SystemTestDashboard from '../../components/testing/SystemTestDashboard';

export default function DashboardPage() {
  const location = useLocation();
  const { user, logout } = useAuthStore();
  const { balance, fetchBalance } = useWalletStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchBalance(user.id);
    }
  }, [user?.id, fetchBalance]);

  const navigation = [
    { name: 'Overview', href: '/dashboard', icon: Settings, current: location.pathname === '/dashboard' },
    { name: 'Profile', href: '/dashboard/profile', icon: User, current: location.pathname === '/dashboard/profile' },
    { name: 'Orders', href: '/dashboard/orders', icon: Package, current: location.pathname === '/dashboard/orders' },
    { name: 'Premium', href: '/dashboard/premium', icon: Crown, current: location.pathname === '/dashboard/premium' },
    { name: 'Referrals', href: '/dashboard/referrals', icon: Users, current: location.pathname === '/dashboard/referrals' },
    ...(user?.kyc_status !== 'approved' ? [
      { name: 'Complete KYC', href: '/dashboard/kyc', icon: Shield, current: location.pathname === '/dashboard/kyc' }
    ] : []),
    ...(user?.is_premium ? [
      { name: 'Wallet', href: '/dashboard/wallet', icon: Wallet, current: location.pathname === '/dashboard/wallet' }
    ] : []),
    ...(user?.email === '<EMAIL>' ? [
      { name: 'System Tests', href: '/dashboard/tests', icon: Shield, current: location.pathname === '/dashboard/tests' }
    ] : []),
  ];

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">Please login to access dashboard</h2>
          <Link
            to="/login"
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors touch-target"
          >
            Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Header */}
        <div className="dashboard-mobile-header lg:hidden bg-white shadow-sm p-4 flex items-center justify-between relative z-50">
          {/* Mobile Logo */}
          <Link
            to="/"
            className="dashboard-logo flex items-center gap-2 hover:opacity-80 transition-opacity touch-target"
            onClick={() => setSidebarOpen(false)}
          >
            <div className="logo-icon w-8 h-8 rounded-full overflow-hidden bg-white shadow-sm transition-transform">
              <img
                src="/logo.png"
                alt="Start Juicce Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">Start Juicce</h3>
              <p className="text-xs text-gray-500">Dashboard</p>
            </div>
          </Link>

          {/* User Info & Menu Button */}
          <div className="flex items-center gap-3">
            {/* User Premium Badge */}
            {user.is_premium && (
              <div className="flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                <Star className="h-3 w-3 fill-current" />
                Premium
              </div>
            )}

            {/* Hamburger Menu Button */}
            <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('Hamburger clicked, current state:', sidebarOpen);
              setSidebarOpen(!sidebarOpen);
            }}
            className="dashboard-hamburger relative z-50 p-3 text-gray-800 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200 touch-target bg-white border-2 border-gray-300 hover:border-green-400 shadow-md hover:shadow-lg active:scale-95 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            style={{
              minWidth: '44px',
              minHeight: '44px',
              backgroundColor: '#ffffff',
              border: '2px solid #10b981',
              boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
              zIndex: 9999
            }}
            type="button"
            aria-label={sidebarOpen ? 'Close menu' : 'Open menu'}
          >
            {sidebarOpen ? (
              <X className="h-6 w-6 mx-auto" />
            ) : (
              <Menu className="h-6 w-6 mx-auto" />
            )}
          </button>
          </div>
        </div>

        {/* Sidebar */}
        <div className={`${
          sidebarOpen ? 'block' : 'hidden'
        } lg:block w-full lg:w-64 bg-white shadow-sm lg:min-h-screen relative z-40 lg:z-auto`}>
          <div className="p-4 lg:p-6">
            {/* Logo/Brand Section */}
            <Link
              to="/"
              className="dashboard-logo flex items-center gap-3 mb-6 p-3 rounded-lg hover:bg-gray-50 transition-colors group touch-target"
              onClick={() => setSidebarOpen(false)}
            >
              <div className="logo-icon w-10 h-10 rounded-full overflow-hidden bg-white shadow-sm group-hover:scale-105 transition-transform">
                <img
                  src="/logo.png"
                  alt="Start Juicce Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="flex-1">
                <h2 className="text-lg font-bold text-gray-900 group-hover:text-green-600 transition-colors">Start Juicce</h2>
                <p className="text-xs text-gray-500">Back to Store</p>
              </div>
              <Home className="h-4 w-4 text-gray-400 group-hover:text-green-600 transition-colors" />
            </Link>
            {/* Desktop User Info */}
            <div className="hidden lg:flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{user.full_name}</h3>
                <div className="flex items-center gap-2">
                  {user.is_premium && (
                    <div className="flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                      <Star className="h-3 w-3 fill-current" />
                      Premium
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Wallet Balance - Only for Premium Users */}
            {user.is_premium && (
              <div className="bg-green-50 p-4 rounded-lg mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-green-600 font-medium">Wallet Balance</p>
                    <p className="text-xl lg:text-2xl font-bold text-green-700">₹{balance}</p>
                  </div>
                  <Wallet className="h-6 w-6 lg:h-8 lg:w-8 text-green-600" />
                </div>
              </div>
            )}

            {/* Premium Upgrade Prompt for Non-Premium Users */}
            {!user.is_premium && (
              <div className="bg-gradient-to-r from-yellow-50 to-amber-50 p-4 rounded-lg mb-6 border border-yellow-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-yellow-700 font-medium">Upgrade to Premium</p>
                    <p className="text-xs text-yellow-600">Unlock full access</p>
                  </div>
                  <Crown className="h-6 w-6 lg:h-8 lg:w-8 text-yellow-600" />
                </div>
              </div>
            )}

            {/* Navigation */}
            <nav className="space-y-1 lg:space-y-2">
              {/* Back to Store Link */}
              <Link
                to="/"
                onClick={() => setSidebarOpen(false)}
                className="flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-blue-50 hover:text-blue-700 transition-colors touch-target border border-blue-200 hover:border-blue-300"
              >
                <Home className="h-5 w-5" />
                Back to Store
              </Link>

              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium transition-colors touch-target ${
                    item.current
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  {item.name}
                </Link>
              ))}

              <button
                onClick={() => {
                  logout();
                  setSidebarOpen(false);
                }}
                className="w-full flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 transition-colors touch-target"
              >
                <LogOut className="h-5 w-5" />
                Logout
              </button>
            </nav>
          </div>
        </div>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <div className="flex-1 p-4 lg:p-8">
          <Routes>
            <Route path="/" element={<DashboardOverview />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/orders" element={<OrdersPage />} />
            <Route path="/premium" element={<PremiumPage />} />
            <Route path="/referrals" element={<EnhancedReferralPage />} />
            <Route path="/kyc" element={<KYCPage />} />
            {user.is_premium && (
              <>
                <Route path="/wallet" element={<WalletPage />} />
              </>
            )}
            {user.email === '<EMAIL>' && (
              <Route path="/tests" element={<SystemTestDashboard />} />
            )}
          </Routes>
        </div>
      </div>
    </div>
  );
}