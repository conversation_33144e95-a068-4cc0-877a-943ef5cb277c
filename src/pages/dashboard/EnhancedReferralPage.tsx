import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import { referralService, ReferralStats, ReferralBonusReceived } from '../../services/referralService';
import { walletService } from '../../services/walletService';
import { premiumService } from '../../services/premiumService';
import {
  Users,
  Gift,
  Crown,
  Copy,
  Share2,
  TrendingUp,
  Wallet,
  Lock,
  Unlock,
  Star,
  CheckCircle,
  Clock,
  DollarSign,
  Zap,
  ArrowRight,
  RefreshCw
} from 'lucide-react';
import toast from 'react-hot-toast';

const EnhancedReferralPage: React.FC = () => {
  const { user, refreshUser } = useAuthStore();
  const [activeTab, setActiveTab] = useState<'overview' | 'network' | 'progress' | 'bonuses'>('overview');
  const [referralCode, setReferralCode] = useState<string>('');
  const [stats, setStats] = useState<ReferralStats>({
    totalReferrals: 0,
    completedReferrals: 0,
    pendingReferrals: 0,
    totalEarnings: 0,
    premiumReferrals: 0,
    ewalletUnlocked: false,
    specialStatus: false
  });
  const [unlockProgress, setUnlockProgress] = useState({
    current: 0,
    required: 3,
    unlocked: false,
    progress: 0
  });
  const [loading, setLoading] = useState(true);
  const [generatingCode, setGeneratingCode] = useState(false);
  const [isPremium, setIsPremium] = useState(false);
  const [bonusesReceived, setBonusesReceived] = useState<ReferralBonusReceived[]>([]);

  useEffect(() => {
    if (user?.id) {
      checkPremiumStatus();
      if (user.is_premium) {
        loadReferralData();
      } else {
        setLoading(false);
      }
    }
  }, [user?.id]);

  const checkPremiumStatus = async () => {
    if (!user?.id) return;

    try {
      const isActive = await premiumService.isPremiumActive(user.id);
      setIsPremium(isActive);
    } catch (error) {
      console.error('Error checking premium status:', error);
      setIsPremium(false);
    }
  };

  const loadReferralData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Refresh user data first to get latest information
      await refreshUser();

      // Load referral stats
      const referralStats = await referralService.getReferralStats(user.id);
      setStats(referralStats);

      // Load unlock progress
      const progress = await walletService.getUnlockProgress(user.id);
      setUnlockProgress(progress);

      // Load referral bonuses received
      const bonuses = await referralService.getReferralBonusesReceived(user.id);
      setBonusesReceived(bonuses);

      // Get user's referral code
      if (user.referral_code) {
        setReferralCode(user.referral_code);
      }

      console.log('EnhancedReferralPage loaded stats:', {
        totalReferrals: referralStats.totalReferrals,
        premiumReferrals: referralStats.premiumReferrals,
        totalEarnings: referralStats.totalEarnings,
        ewalletUnlocked: referralStats.ewalletUnlocked,
        unlockProgress: progress
      });
    } catch (error) {
      console.error('Error loading referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateCode = async () => {
    if (!user?.id) return;

    // Check if user is premium
    const isPremium = await premiumService.isPremiumActive(user.id);
    if (!isPremium) {
      toast.error('Only premium users can generate referral codes');
      return;
    }

    setGeneratingCode(true);
    try {
      const code = await referralService.generateReferralCode(user.id);
      if (code) {
        setReferralCode(code);
        toast.success('Referral code generated successfully!');
      } else {
        toast.error('Failed to generate referral code');
      }
    } catch (error) {
      console.error('Error generating referral code:', error);
      toast.error('Failed to generate referral code');
    } finally {
      setGeneratingCode(false);
    }
  };

  const handleCopyCode = () => {
    if (referralCode) {
      navigator.clipboard.writeText(referralCode);
      toast.success('Referral code copied to clipboard!');
    }
  };

  const handleShareCode = () => {
    if (referralCode) {
      const shareUrl = `${window.location.origin}/register?ref=${referralCode}`;
      navigator.clipboard.writeText(shareUrl);
      toast.success('Referral link copied to clipboard!');
    }
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Referral Code Section */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Gift className="w-5 h-5 text-green-600" />
            Your Referral Code
          </h3>
          {stats.specialStatus && (
            <div className="flex items-center gap-1 bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-sm">
              <Crown className="w-4 h-4" />
              Special Status
            </div>
          )}
        </div>

        {referralCode ? (
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 border-2 border-dashed border-green-300">
              <div className="text-center">
                <div className="text-2xl font-mono font-bold text-green-600 mb-2">
                  {referralCode}
                </div>
                <div className="flex justify-center gap-2">
                  <button
                    onClick={handleCopyCode}
                    className="flex items-center gap-1 bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700"
                  >
                    <Copy className="w-4 h-4" />
                    Copy Code
                  </button>
                  <button
                    onClick={handleShareCode}
                    className="flex items-center gap-1 bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700"
                  >
                    <Share2 className="w-4 h-4" />
                    Share Link
                  </button>
                </div>
              </div>
            </div>
            
            <div className="text-sm text-gray-600 text-center">
              Share this code with friends to earn ₹100 per premium referral!
            </div>
          </div>
        ) : (
          <div className="text-center">
            <button
              onClick={handleGenerateCode}
              disabled={generatingCode}
              className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {generatingCode ? 'Generating...' : 'Generate Referral Code'}
            </button>
            <p className="text-sm text-gray-600 mt-2">
              Premium membership required to generate referral codes
            </p>
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Referrals</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalReferrals}</p>
            </div>
            <Users className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Premium Referrals</p>
              <p className="text-2xl font-bold text-green-600">{stats.premiumReferrals}</p>
            </div>
            <Crown className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Earnings</p>
              <p className="text-2xl font-bold text-purple-600">₹{stats.totalEarnings}</p>
            </div>
            <DollarSign className="w-8 h-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">E-Wallet Status</p>
              <p className="text-sm font-semibold text-green-600">
                Unlocked
              </p>
            </div>
            <Unlock className="w-8 h-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Referral Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            Completed Referrals
          </h4>
          <div className="text-2xl font-bold text-green-600 mb-1">
            {stats.completedReferrals}
          </div>
          <p className="text-sm text-gray-600">
            Users who became premium members
          </p>
        </div>

        <div className="bg-white rounded-lg p-4 border border-gray-200">
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Clock className="w-5 h-5 text-yellow-600" />
            Pending Referrals
          </h4>
          <div className="text-2xl font-bold text-yellow-600 mb-1">
            {stats.pendingReferrals}
          </div>
          <p className="text-sm text-gray-600">
            Users who haven't upgraded to premium yet
          </p>
        </div>
      </div>
    </div>
  );

  const renderProgressTab = () => (
    <div className="space-y-6">
      {/* E-Wallet Status - Always Unlocked for Premium */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center gap-2">
          <Wallet className="w-5 h-5 text-green-600" />
          E-Wallet Status
        </h3>

        <div className="space-y-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 text-green-600 mb-3">
              <Unlock className="w-8 h-8" />
              <span className="text-xl font-bold">E-Wallet Unlocked!</span>
            </div>
            <p className="text-green-800">
              Your e-wallet is automatically unlocked as a premium member.
            </p>
            <p className="text-sm text-green-600 mt-2">
              Enjoy full access to all wallet features including transactions, withdrawals, and referral bonuses.
            </p>
          </div>
        </div>
      </div>

      {/* Benefits Overview */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Star className="w-5 h-5 text-yellow-500" />
          Referral Benefits
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Current Benefits:</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                ₹100 per premium referral
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Unlimited referral levels
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Real-time bonus tracking
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Unlock at 3 Referrals:</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <Crown className="w-4 h-4 text-purple-500" />
                Full e-wallet access
              </li>
              <li className="flex items-center gap-2">
                <Crown className="w-4 h-4 text-purple-500" />
                Zero withdrawal fees
              </li>
              <li className="flex items-center gap-2">
                <Crown className="w-4 h-4 text-purple-500" />
                Special referral status
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBonusesTab = () => (
    <div className="space-y-6">
      {/* Bonuses Received Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
          <Gift className="w-5 h-5 text-purple-600" />
          Referral Bonuses Received
        </h3>
        <p className="text-gray-600">
          Track all the referral bonuses you've earned from your network
        </p>

        {/* Show who referred this user */}
        {user?.referred_by_id && (
          <div className="mt-4 p-3 bg-white rounded-lg border border-purple-200">
            <p className="text-sm text-gray-600 mb-1">You were referred by:</p>
            <p className="font-medium text-purple-700">
              {user.referrer_name || 'Unknown User'}
            </p>
            {user.referrer_email && (
              <p className="text-xs text-gray-500">{user.referrer_email}</p>
            )}
          </div>
        )}
      </div>

      {/* Bonuses List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Recent Bonuses</h4>

          {bonusesReceived.length === 0 ? (
            <div className="text-center py-8">
              <Gift className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-2">No referral bonuses received yet</p>
              <p className="text-sm text-gray-400">
                You'll see bonuses here when people join using your referral code
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {bonusesReceived.map((bonus) => (
                <div
                  key={bonus.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <DollarSign className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">
                          ₹{bonus.amount.toFixed(2)} Referral Bonus
                        </h5>
                        <p className="text-sm text-gray-600">
                          Level {bonus.referral_level} • From {bonus.referrer_name}
                        </p>
                      </div>
                    </div>
                    <div className="ml-13">
                      <p className="text-sm text-gray-500 mb-1">
                        {bonus.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-400">
                        <span>Referrer: {bonus.referrer_email}</span>
                        {bonus.referred_user_name && (
                          <span>For: {bonus.referred_user_name}</span>
                        )}
                        <span>{new Date(bonus.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-green-600">
                      +₹{bonus.amount.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-500">
                      Level {bonus.referral_level}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Bonuses</p>
              <p className="text-xl font-semibold text-gray-900">
                ₹{bonusesReceived.reduce((sum, bonus) => sum + bonus.amount, 0).toFixed(2)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Gift className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Bonus Count</p>
              <p className="text-xl font-semibold text-gray-900">
                {bonusesReceived.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg Bonus</p>
              <p className="text-xl font-semibold text-gray-900">
                ₹{bonusesReceived.length > 0
                  ? (bonusesReceived.reduce((sum, bonus) => sum + bonus.amount, 0) / bonusesReceived.length).toFixed(2)
                  : '0.00'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  // Premium Gate for Non-Premium Users
  if (!isPremium && !user?.is_premium) {
    return (
      <div className="max-w-6xl mx-auto p-6 relative">
        {/* Blurred Background Content */}
        <div className="blur-sm pointer-events-none select-none">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Multi-Level Referral System</h1>
            <p className="text-gray-600">
              Refer friends and earn unlimited bonuses through our advanced referral program
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Overview', icon: TrendingUp },
                { id: 'progress', label: 'Progress', icon: Wallet }
              ].map((tab) => (
                <button
                  key={tab.id}
                  className="flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm border-green-500 text-green-600"
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Mock Content */}
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Gift className="w-5 h-5 text-green-600" />
                  Your Referral Code
                </h3>
              </div>
              <div className="bg-white rounded-lg p-4 border-2 border-dashed border-green-300">
                <div className="text-center">
                  <div className="text-2xl font-mono font-bold text-green-600 mb-2">
                    PREMIUM24
                  </div>
                  <div className="flex justify-center gap-2">
                    <button className="flex items-center gap-1 bg-green-600 text-white px-3 py-1 rounded-md text-sm">
                      <Copy className="w-4 h-4" />
                      Copy Code
                    </button>
                    <button className="flex items-center gap-1 bg-blue-600 text-white px-3 py-1 rounded-md text-sm">
                      <Share2 className="w-4 h-4" />
                      Share Link
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Referrals</p>
                    <p className="text-2xl font-bold text-gray-900">15</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Premium Referrals</p>
                    <p className="text-2xl font-bold text-green-600">8</p>
                  </div>
                  <Crown className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Earnings</p>
                    <p className="text-2xl font-bold text-purple-600">₹1,200</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-600" />
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">E-Wallet Status</p>
                    <p className="text-sm font-semibold text-gray-900">Unlocked</p>
                  </div>
                  <Unlock className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Premium Upgrade Overlay */}
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm">
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 border border-gray-200">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-white" />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Premium Required
              </h2>

              <p className="text-gray-600 mb-6">
                Unlock the powerful multi-level referral system and start earning unlimited bonuses!
              </p>

              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-3 text-left">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm text-gray-700">Generate unique referral codes</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm text-gray-700">Earn ₹100 per premium referral</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm text-gray-700">Unlock e-wallet after 3 referrals</span>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <span className="text-sm text-gray-700">Multi-level bonus system</span>
                </div>
              </div>

              <Link
                to="/dashboard/premium"
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center gap-2 group"
              >
                <Zap className="w-5 h-5" />
                Upgrade to Premium
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>

              <p className="text-xs text-gray-500 mt-3">
                Only ₹299/month • Non-refundable • Start earning immediately
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Multi-Level Referral System</h1>
          <p className="text-gray-600">
            Refer friends and earn unlimited bonuses through our advanced referral program
          </p>
        </div>
        <button
          onClick={loadReferralData}
          className="p-3 bg-green-100 hover:bg-green-200 rounded-lg transition-colors"
          title="Refresh data"
        >
          <RefreshCw className="h-5 w-5 text-green-600" />
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'progress', label: 'Progress', icon: Wallet },
            { id: 'bonuses', label: 'Bonuses Received', icon: Gift }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverviewTab()}
      {activeTab === 'progress' && renderProgressTab()}
      {activeTab === 'bonuses' && renderBonusesTab()}
    </div>
  );
};

export default EnhancedReferralPage;
