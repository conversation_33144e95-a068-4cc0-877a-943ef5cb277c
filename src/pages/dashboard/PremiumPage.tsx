import React, { useState } from 'react';
import { Crown, Star, Check, Zap, Users, Gift } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import { referralCodeGenerator } from '../../services/referralCodeGenerator';
import toast from 'react-hot-toast';

export default function PremiumPage() {
  const { user, checkUser } = useAuthStore();
  const [loading, setLoading] = useState(false);

  const premiumFeatures = [
    {
      icon: <Star className="h-6 w-6" />,
      title: 'Exclusive Products',
      description: 'Access to premium-only herbal products and limited editions'
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: 'Referral Program',
      description: 'Earn money by referring friends and family'
    },
    {
      icon: <Gift className="h-6 w-6" />,
      title: 'Special Discounts',
      description: 'Get exclusive discounts on all products'
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: 'Priority Support',
      description: 'Get priority customer support and faster delivery'
    }
  ];

  const handleUpgradeToPremium = async () => {
    if (!user) {
      toast.error('Please login to upgrade to premium');
      return;
    }

    setLoading(true);
    try {
      // First verify user exists in database with retry logic
      let existingUser = null;
      let checkError = null;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries && !existingUser) {
        const { data, error } = await supabase
          .from('users')
          .select('id, email, is_premium')
          .eq('id', user.id)
          .single();

        if (!error && data) {
          existingUser = data;
          break;
        }

        checkError = error;
        retryCount++;

        if (retryCount < maxRetries) {
          console.log(`⚠️ Retry ${retryCount}/${maxRetries} for user verification...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        }
      }

      if (checkError || !existingUser) {
        console.error('User verification failed after retries:', checkError);
        throw new Error('User not found in database. Please try refreshing the page or logging out and back in.');
      }

      if (existingUser.is_premium) {
        toast.success('You are already a premium user!');
        setLoading(false);
        return;
      }
      // In a real app, this would integrate with Stripe/Razorpay
      // For demo purposes, we'll simulate the lifetime premium upgrade
      console.log('Upgrading user to premium:', user.id);

      const { data, error } = await supabase
        .from('users')
        .update({
          is_premium: true,
          premium_lifetime_access: true,
          premium_purchase_confirmed: true,
          premium_no_refund_accepted: true,
          premium_purchased_at: new Date().toISOString(),
          ewallet_unlocked: true,
          wallet_unlocked: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) {
        console.error('Database update error:', error);
        throw new Error(`Failed to update user: ${error.message}`);
      }

      console.log('Premium upgrade successful, generating referral code...');

      // Generate referral code for the new premium user
      const referralResult = await referralCodeGenerator.generateAndAssignForPremiumUser(
        user.id,
        user.email || ''
      );

      // Refresh user data to get updated premium status
      await checkUser();

      if (referralResult.success) {
        toast.success(`Welcome to Lifetime Premium! 🎉\nYour referral code: ${referralResult.referralCode}`);
      } else {
        toast.success('Welcome to Lifetime Premium! 🎉\n(Referral code will be generated shortly)');
      }
    } catch (error: any) {
      console.error('Error upgrading to premium:', error);

      // Provide more specific error messages
      if (error.message?.includes('not found')) {
        toast.error('User account not found. Please try logging out and back in.');
      } else if (error.message?.includes('already a premium')) {
        toast.success('You are already a premium user!');
      } else {
        toast.error(`Failed to upgrade to premium: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  if (user?.is_premium) {
    return (
      <div>
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-full text-lg font-semibold mb-4">
            <Crown className="h-6 w-6 fill-current" />
            Premium Member
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">You're a Premium Member!</h1>
          <p className="text-gray-600">
            Enjoy all the exclusive benefits and features available to premium members.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {premiumFeatures.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-yellow-100 text-yellow-600 rounded-lg">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-lg border border-yellow-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Premium Status</h2>
          <p className="text-gray-700 mb-4">
            Your lifetime premium membership is active - no expiration!
          </p>
          {user.premium_purchased_at && (
            <p className="text-sm text-gray-600">
              Purchased on: {new Date(user.premium_purchased_at).toLocaleDateString()}
            </p>
          )}
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Lifetime Access
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="text-center mb-12">
        <div className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-full text-lg font-semibold mb-6">
          <Crown className="h-6 w-6 fill-current" />
          Upgrade to Premium
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Unlock Exclusive Benefits
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Get access to premium products, referral program, and exclusive discounts with our premium membership.
        </p>
      </div>

      {/* Pricing Card */}
      <div className="max-w-md mx-auto mb-12">
        <div className="bg-white rounded-2xl shadow-xl border-2 border-yellow-200 overflow-hidden">
          <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white p-6 text-center">
            <Crown className="h-12 w-12 mx-auto mb-4 fill-current" />
            <h2 className="text-2xl font-bold mb-2">Premium Membership</h2>
            <div className="text-4xl font-bold mb-2">₹999</div>
            <p className="text-yellow-100">per year</p>
          </div>
          
          <div className="p-6">
            <ul className="space-y-4 mb-8">
              {premiumFeatures.map((feature, index) => (
                <li key={index} className="flex items-start gap-3">
                  <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">{feature.title}</p>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </li>
              ))}
            </ul>

            <button
              onClick={handleUpgradeToPremium}
              disabled={loading}
              className="w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-white py-4 rounded-lg font-semibold text-lg hover:from-yellow-500 hover:to-yellow-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Processing...' : 'Upgrade Now'}
            </button>

            <p className="text-xs text-gray-500 mt-4 text-center">
              In a real application, this would integrate with Stripe or Razorpay for secure payments.
            </p>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {premiumFeatures.map((feature, index) => (
          <div key={index} className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-start gap-4">
              <div className="p-4 bg-yellow-100 text-yellow-600 rounded-xl">
                {feature.icon}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}