import React, { useState, useEffect, useCallback } from 'react';
import {
  Wallet,
  History,
  CreditCard,
  Gift,
  AlertCircle,
  Filter,
  ArrowUpRight,
  ArrowDownLeft,
  Plus,
  Minus
} from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import { formatCurrency, formatTransactionDate } from '../../utils/walletUtils';
import toast from 'react-hot-toast';
import '../../styles/wallet-mobile.css';

interface Transaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_type: string;
  created_at: string;
}

export default function WalletPage() {
  const { user, refreshUser } = useAuthStore();

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'credit' | 'debit'>('all');

  // Fetch wallet transactions
  const fetchTransactions = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTransactions(data || []);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (user?.id) {
      fetchTransactions();
    }
  }, [user?.id, fetchTransactions]);

  // Check if user is premium
  if (!user?.is_premium) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Premium Required</h2>
          <p className="text-gray-600">You need a premium subscription to access the wallet.</p>
        </div>
      </div>
    );
  }

  // Premium users always have full wallet access - no lock functionality
  // Removed all wallet lock logic

  // Removed add money functionality - Admin only feature

  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'all') return true;
    return transaction.type === filter;
  });

  const getTransactionIcon = (type: string) => {
    return type === 'credit' ? (
      <Plus className="h-5 w-5 text-green-500" />
    ) : (
      <Minus className="h-5 w-5 text-red-500" />
    );
  };

  const getTransactionColor = (type: string) => {
    return type === 'credit' ? 'text-green-600' : 'text-red-600';
  };

  // Removed local formatting functions - using utils instead

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Wallet</h1>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 w-full sm:w-auto">
          <div className="flex items-center gap-2 text-blue-700">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Need to add money?</span>
          </div>
          <p className="text-xs text-blue-600 mt-1">Contact administrator to add funds to your wallet</p>
        </div>
      </div>

      {/* Wallet Balance Card */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 sm:p-8 rounded-2xl">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-green-100 mb-2 text-sm sm:text-base">Available Balance</p>
            <p className="text-3xl sm:text-4xl font-bold">{formatCurrency(user?.wallet_balance || 0)}</p>
          </div>
          <Wallet className="h-12 w-12 sm:h-16 sm:w-16 text-green-200" />
        </div>
      </div>

      {/* Wallet Features Info */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Wallet Features</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <CreditCard className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Instant Payments</p>
              <p className="text-xs text-gray-500">Quick checkout</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Wallet className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Secure Storage</p>
              <p className="text-xs text-gray-500">Protected funds</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Gift className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Cashback</p>
              <p className="text-xs text-gray-500">Earn rewards</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <History className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Track History</p>
              <p className="text-xs text-gray-500">Full audit trail</p>
            </div>
          </div>
        </div>
      </div>

      {/* Transaction Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-6">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-500" />
          <span className="text-sm font-medium text-gray-700 sm:hidden">Filter:</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {[
            { key: 'all', label: 'All Transactions' },
            { key: 'credit', label: 'Money In' },
            { key: 'debit', label: 'Money Out' }
          ].map((option) => (
            <button
              key={option.key}
              onClick={() => setFilter(option.key as any)}
              className={`px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-colors touch-target ${
                filter === option.key
                  ? 'bg-green-100 text-green-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Transactions List */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Transaction History</h2>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-12 px-4">
            <Wallet className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
            <p className="text-sm sm:text-base text-gray-500">Your wallet transactions will appear here</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="p-4 sm:p-6">
                {/* Mobile Layout */}
                <div className="sm:hidden">
                  <div className="flex items-start gap-3 mb-3">
                    {getTransactionIcon(transaction.type)}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 text-sm truncate">{transaction.description}</h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatTransactionDate(transaction.created_at)}
                      </p>
                      {transaction.reference_type && (
                        <p className="text-xs text-gray-500 capitalize mt-1">
                          {transaction.reference_type}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold text-lg ${getTransactionColor(transaction.type)}`}>
                      {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                    </p>
                  </div>
                </div>

                {/* Desktop Layout */}
                <div className="hidden sm:flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <h3 className="font-medium text-gray-900">{transaction.description}</h3>
                      <p className="text-sm text-gray-500">
                        {formatTransactionDate(transaction.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                      {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                    </p>
                    {transaction.reference_type && (
                      <p className="text-xs text-gray-500 capitalize">
                        {transaction.reference_type}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>


    </div>
  );
}