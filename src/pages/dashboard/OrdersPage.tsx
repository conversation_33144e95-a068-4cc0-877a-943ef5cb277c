import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuthStore } from '../../stores/authStore';
import { toast } from 'react-hot-toast';
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  ArrowLeft,
  MapPin,
  Calendar,
  CreditCard
} from 'lucide-react';

interface Order {
  id: string;
  order_number: string;
  status: string;
  total_amount: number;
  wallet_used: number;
  shipping_cost: number;
  delivery_address: string;
  created_at: string;
  updated_at: string;
  items?: OrderItem[];
  status_history?: StatusHistory[];
  shipment?: Shipment | null;
  order_items?: any[];
}

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    name: string;
    image_url: string;
    sku: string;
  };
}

interface StatusHistory {
  status: string;
  previous_status: string;
  notes: string;
  created_at: string;
  changed_by: string;
}

interface Shipment {
  id: string;
  tracking_number: string;
  carrier: string;
  status: string;
  estimated_delivery: string;
  actual_delivery: string;
}

export default function OrdersPage() {
  const { user } = useAuthStore();
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [view, setView] = useState<'list' | 'details'>('list');

  useEffect(() => {
    if (user) {
      loadOrders();
    }
  }, [user]);

  const loadOrders = async () => {
    try {
      setLoading(true);

      const { data: ordersData, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          status,
          total_amount,
          wallet_used,
          shipping_cost,
          delivery_address,
          created_at,
          updated_at,
          order_items (
            id,
            quantity,
            price,
            products (
              id,
              name,
              image_url,
              sku
            )
          )
        `)
        .eq('user_id', user!.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setOrders(ordersData || []);
    } catch (error) {
      console.error('Error loading orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const loadOrderDetails = async (orderId: string) => {
    try {
      const { data, error } = await supabase.rpc('get_order_details', {
        p_order_id: orderId
      });

      if (error) throw error;

      if (data && !data.error) {
        setSelectedOrder({
          ...data.order,
          items: data.items || [],
          status_history: data.status_history || [],
          shipment: data.shipment
        });
        setView('details');
      } else {
        toast.error('Order details not found');
      }
    } catch (error) {
      console.error('Error loading order details:', error);
      toast.error('Failed to load order details');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'confirmed': return <CheckCircle className="h-5 w-5 text-blue-500" />;
      case 'processing': return <Package className="h-5 w-5 text-purple-500" />;
      case 'shipped': return <Truck className="h-5 w-5 text-orange-500" />;
      case 'delivered': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'cancelled': return <XCircle className="h-5 w-5 text-red-500" />;
      default: return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'shipped': return 'bg-orange-100 text-orange-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading orders...</span>
      </div>
    );
  }

  if (view === 'details' && selectedOrder) {
    return (
      <div className="max-w-4xl mx-auto p-4 sm:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
          <button
            onClick={() => setView('list')}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 touch-target"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Orders
          </button>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
            Order {selectedOrder.order_number}
          </h1>
        </div>

        {/* Order Status */}
        <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
            <div className="flex items-center gap-3">
              {getStatusIcon(selectedOrder.status)}
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedOrder.status)}`}>
                {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
              </span>
            </div>
            <div className="text-left sm:text-right">
              <p className="text-sm text-gray-500">Order Total</p>
              <p className="text-lg sm:text-xl font-bold text-gray-900">{formatCurrency(selectedOrder.total_amount)}</p>
            </div>
          </div>

          {/* Order Info Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Order Date</p>
                <p className="font-medium">{formatDate(selectedOrder.created_at)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <CreditCard className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Payment</p>
                <p className="font-medium">
                  {selectedOrder.wallet_used > 0 ? 'Wallet' : 'Cash on Delivery'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Delivery Address</p>
                <p className="font-medium text-sm">{selectedOrder.delivery_address}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tracking Information */}
        {selectedOrder.shipment && (
          <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 mb-4 sm:mb-6">
            <h3 className="text-lg font-semibold mb-4">Tracking Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Tracking Number</p>
                <p className="font-mono font-medium">{selectedOrder.shipment.tracking_number}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Carrier</p>
                <p className="font-medium">{selectedOrder.shipment.carrier}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Estimated Delivery</p>
                <p className="font-medium">
                  {selectedOrder.shipment.estimated_delivery ?
                    new Date(selectedOrder.shipment.estimated_delivery).toLocaleDateString('en-IN') :
                    'Not available'
                  }
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Shipment Status</p>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedOrder.shipment.status)}`}>
                  {selectedOrder.shipment.status}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Order Items */}
        <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 mb-4 sm:mb-6">
          <h3 className="text-lg font-semibold mb-4">Order Items</h3>
          <div className="space-y-4">
            {(selectedOrder.items || selectedOrder.order_items || []).map((item: any) => (
              <div key={item.id} className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 border rounded-lg">
                <img
                  src={item.product?.image_url || '/placeholder-product.jpg'}
                  alt={item.product?.name || 'Product'}
                  className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm sm:text-base truncate">{item.product?.name || 'Product'}</h4>
                  <p className="text-xs sm:text-sm text-gray-500">SKU: {item.product?.sku || 'N/A'}</p>
                  <p className="text-xs sm:text-sm text-gray-500">Quantity: {item.quantity}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-sm sm:text-base">{formatCurrency(item.price)}</p>
                  <p className="text-xs sm:text-sm text-gray-500">per item</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Status History */}
        {selectedOrder.status_history && selectedOrder.status_history.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold mb-4">Order Timeline</h3>
            <div className="space-y-4">
              {selectedOrder.status_history.map((history, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(history.status)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(history.status)}`}>
                        {history.status.charAt(0).toUpperCase() + history.status.slice(1)}
                      </span>
                      <span className="text-sm text-gray-500">{formatDate(history.created_at)}</span>
                    </div>
                    {history.notes && (
                      <p className="text-sm text-gray-600 mt-1">{history.notes}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Updated by: {history.changed_by}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">My Orders</h1>
        <button
          onClick={loadOrders}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 touch-target"
        >
          <Package className="h-4 w-4" />
          Refresh
        </button>
      </div>

      {orders.length === 0 ? (
        <div className="text-center py-12">
          <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
          <p className="text-gray-500">Start shopping to see your orders here!</p>
        </div>
      ) : (
        <div className="grid gap-4 sm:gap-6">
          {orders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <span className="font-mono text-sm font-medium text-gray-600">
                    {order.order_number || `ORD${order.id.slice(0, 8)}`}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)} w-fit`}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </span>
                </div>
                <button
                  onClick={() => loadOrderDetails(order.id)}
                  className="flex items-center gap-2 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg touch-target w-fit"
                >
                  <Eye className="h-4 w-4" />
                  <span className="hidden sm:inline">View Details</span>
                  <span className="sm:hidden">Details</span>
                </button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500">Order Date</p>
                  <p className="font-medium">{formatDate(order.created_at)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Amount</p>
                  <p className="font-medium">{formatCurrency(order.total_amount)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Payment Method</p>
                  <p className="font-medium">
                    {order.wallet_used > 0 ? 'Wallet' : 'Cash on Delivery'}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex items-center gap-2">
                  {getStatusIcon(order.status)}
                  <span className="text-sm text-gray-600">
                    Last updated: {formatDate(order.updated_at)}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {order.order_items?.length || 0} item(s)
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

    </div>
  );
}