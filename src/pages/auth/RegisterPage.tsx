import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Eye, EyeOff, Mail, Lock, User, Users, CreditCard, Building,
  ArrowRight, ArrowLeft, CheckCircle, Phone, Upload, FileText,
  Camera, Shield, Clock, Check, X, RefreshCw
} from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { sendOTP, verifyOTP, isContactVerified } from '../../services/otpService';
import { useReferralRegistration } from '../../hooks/useReferralRegistration';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface FormData {
  // Step 1: Mobile/Email Verification
  contactMethod: 'mobile' | 'email';
  mobile: string;
  email: string;
  otp: string;
  isContactVerified: boolean;

  // Step 2: Referral Code
  referralCode: string;
  isReferralVerified: boolean;

  // Step 3: KYC Documents
  aadhaarFront: File | null;
  aadhaarBack: File | null;
  panCard: File | null;
  aadhaarFrontUrl: string;
  aadhaarBackUrl: string;
  panCardUrl: string;

  // Step 4: Banking Details
  accountNumber: string;
  ifscCode: string;
  accountHolderName: string;
  bankName: string;

  // Step 5: Personal Information
  firstName: string;
  lastName: string;
  username: string;
  password: string;
  confirmPassword: string;
}

const TOTAL_STEPS = 5;
const INDIAN_BANKS = [
  'State Bank of India (SBI)',
  'HDFC Bank',
  'ICICI Bank',
  'Axis Bank',
  'Punjab National Bank (PNB)',
  'Bank of Baroda',
  'Canara Bank',
  'Union Bank of India',
  'Bank of India',
  'Indian Bank',
  'Central Bank of India',
  'Indian Overseas Bank',
  'UCO Bank',
  'Bank of Maharashtra',
  'Punjab & Sind Bank'
];

const RegisterPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    contactMethod: 'mobile',
    mobile: '',
    email: '',
    otp: '',
    isContactVerified: false,
    referralCode: '',
    isReferralVerified: false,
    aadhaarFront: null,
    aadhaarBack: null,
    panCard: null,
    aadhaarFrontUrl: '',
    aadhaarBackUrl: '',
    panCardUrl: '',
    accountNumber: '',
    ifscCode: '',
    accountHolderName: '',
    bankName: '',
    firstName: '',
    lastName: '',
    username: '',
    password: '',
    confirmPassword: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isUploadingFiles, setIsUploadingFiles] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);
  const [canResendOtp, setCanResendOtp] = useState(true);

  const { register, isLoading, checkUser } = useAuthStore();
  const navigate = useNavigate();

  // Use the new referral registration hook
  const {
    referralCode,
    setReferralCode,
    validatingReferral,
    referralValidation,
    validateReferralCode,
    processReferralOnRegistration
  } = useReferralRegistration();

  // OTP Timer Effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (otpTimer > 0) {
      interval = setInterval(() => {
        setOtpTimer(prev => prev - 1);
      }, 1000);
    } else if (otpTimer === 0 && !canResendOtp) {
      setCanResendOtp(true);
    }
    return () => clearInterval(interval);
  }, [otpTimer, canResendOtp]);

  // Load saved form data from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('kycFormData');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Check if contact is already verified when contact changes
  useEffect(() => {
    const checkContactVerification = async () => {
      if (formData.contactMethod === 'mobile' && formData.mobile && validateMobile(formData.mobile)) {
        const verified = await isContactVerified(formData.mobile, 'mobile');
        if (verified) {
          setFormData(prev => ({ ...prev, isContactVerified: true }));
        }
      } else if (formData.contactMethod === 'email' && formData.email && validateEmail(formData.email)) {
        const verified = await isContactVerified(formData.email, 'email');
        if (verified) {
          setFormData(prev => ({ ...prev, isContactVerified: true }));
        }
      }
    };

    checkContactVerification();
  }, [formData.contactMethod, formData.mobile, formData.email]);

  // Save form data to localStorage
  useEffect(() => {
    localStorage.setItem('kycFormData', JSON.stringify(formData));
  }, [formData]);

  // Validation functions
  const validateMobile = (mobile: string): boolean => {
    const mobileRegex = /^[6-9]\d{9}$/;
    return mobileRegex.test(mobile);
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateIFSC = (ifsc: string): boolean => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscRegex.test(ifsc.toUpperCase());
  };

  const validateAccountNumber = (accountNumber: string): boolean => {
    return accountNumber.length >= 9 && accountNumber.length <= 18 && /^\d+$/.test(accountNumber);
  };

  const validateFile = (file: File): boolean => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast.error('Only JPG, PNG, and PDF files are allowed');
      return false;
    }

    if (file.size > maxSize) {
      toast.error('File size must be less than 5MB');
      return false;
    }

    return true;
  };

  // API Functions
  const handleSendOTPRequest = async (contact: string, method: 'mobile' | 'email'): Promise<boolean> => {
    try {
      const success = await sendOTP(contact, method);
      if (success) {
        setOtpTimer(60);
        setCanResendOtp(false);
      }
      return success;
    } catch (error) {
      toast.error('Failed to send OTP. Please try again.');
      return false;
    }
  };

  const handleVerifyOTPRequest = async (contact: string, method: 'mobile' | 'email', otp: string): Promise<boolean> => {
    try {
      return await verifyOTP(contact, method, otp);
    } catch (error) {
      toast.error('Failed to verify OTP. Please try again.');
      return false;
    }
  };



  const uploadFile = async (file: File, type: string): Promise<string> => {
    try {
      // Mock file upload - replace with actual Supabase Storage upload
      await new Promise(resolve => setTimeout(resolve, 2000));
      return `https://mock-storage.com/${type}_${Date.now()}.${file.name.split('.').pop()}`;
    } catch (error) {
      throw new Error('File upload failed');
    }
  };

  const lookupIFSC = async (ifsc: string): Promise<string | null> => {
    try {
      // Mock IFSC lookup - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 500));
      const bankMap: { [key: string]: string } = {
        'SBIN': 'State Bank of India (SBI)',
        'HDFC': 'HDFC Bank',
        'ICIC': 'ICICI Bank',
        'UTIB': 'Axis Bank',
        'PUNB': 'Punjab National Bank (PNB)'
      };
      const bankCode = ifsc.substring(0, 4);
      return bankMap[bankCode] || null;
    } catch (error) {
      return null;
    }
  };

  // Form handling functions
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'aadhaarFront' | 'aadhaarBack' | 'panCard') => {
    const file = e.target.files?.[0];
    if (file && validateFile(file)) {
      setFormData(prev => ({
        ...prev,
        [type]: file
      }));
    }
  };

  const handleSendOTP = async () => {
    const contact = formData.contactMethod === 'mobile' ? formData.mobile : formData.email;
    const isValid = formData.contactMethod === 'mobile'
      ? validateMobile(formData.mobile)
      : validateEmail(formData.email);

    if (!isValid) {
      toast.error(`Please enter a valid ${formData.contactMethod}`);
      return;
    }

    await handleSendOTPRequest(contact, formData.contactMethod);
  };

  const handleVerifyOTP = async () => {
    if (!formData.otp || formData.otp.length !== 6) {
      toast.error('Please enter a valid 6-digit OTP');
      return;
    }

    const contact = formData.contactMethod === 'mobile' ? formData.mobile : formData.email;
    const isValid = await handleVerifyOTPRequest(contact, formData.contactMethod, formData.otp);

    if (isValid) {
      setFormData(prev => ({ ...prev, isContactVerified: true }));
    }
  };

  const handleVerifyReferral = async () => {
    if (!referralCode.trim()) {
      toast.error('Please enter a referral code');
      return;
    }

    await validateReferralCode();
    if (referralValidation?.isValid) {
      setFormData(prev => ({ ...prev, isReferralVerified: true }));
    }
  };

  const handleUploadDocuments = async () => {
    if (!formData.aadhaarFront || !formData.aadhaarBack || !formData.panCard) {
      toast.error('Please upload all required documents');
      return;
    }

    setIsUploadingFiles(true);
    try {
      const [aadhaarFrontUrl, aadhaarBackUrl, panCardUrl] = await Promise.all([
        uploadFile(formData.aadhaarFront, 'aadhaar_front'),
        uploadFile(formData.aadhaarBack, 'aadhaar_back'),
        uploadFile(formData.panCard, 'pan_card')
      ]);

      setFormData(prev => ({
        ...prev,
        aadhaarFrontUrl,
        aadhaarBackUrl,
        panCardUrl
      }));

      toast.success('Documents uploaded successfully!');
      setCurrentStep(4);
    } catch (error) {
      toast.error('Failed to upload documents. Please try again.');
    } finally {
      setIsUploadingFiles(false);
    }
  };

  const handleIFSCChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const ifsc = e.target.value.toUpperCase();
    setFormData(prev => ({ ...prev, ifscCode: ifsc }));

    if (validateIFSC(ifsc)) {
      const bankName = await lookupIFSC(ifsc);
      if (bankName) {
        setFormData(prev => ({ ...prev, bankName }));
      }
    }
  };

  const handleNextStep = () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinalSubmit = async () => {
    // Validate personal information
    if (!formData.firstName || !formData.lastName || !formData.username || !formData.password) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (formData.password.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    try {
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        username: formData.username,
        email: formData.contactMethod === 'email' ? formData.email : `${formData.mobile}@temp.com`,
        password: formData.password,
        referralCode: referralCode, // Use the referral code from the hook
        mobile: formData.mobile,
        kycData: {
          aadhaarFrontUrl: formData.aadhaarFrontUrl,
          aadhaarBackUrl: formData.aadhaarBackUrl,
          panCardUrl: formData.panCardUrl,
          accountNumber: formData.accountNumber,
          ifscCode: formData.ifscCode,
          accountHolderName: formData.accountHolderName,
          bankName: formData.bankName
        }
      };

      console.log('Registration data:', registrationData);

      const result = await register(registrationData);

      if (result.success) {
        // Process referral if valid
        if (referralValidation?.isValid && result.user?.id) {
          const userEmail = formData.contactMethod === 'email' ? formData.email : `${formData.mobile}@temp.com`;
          await processReferralOnRegistration(result.user.id, userEmail);
        }

        localStorage.removeItem('kycFormData');

        // Ensure auth state is properly updated
        await checkUser();

        toast.success('Registration successful! Welcome to Start Juicce!');

        // Small delay to ensure state is updated before navigation
        setTimeout(() => {
          navigate('/dashboard');
        }, 500);
      } else {
        toast.error(result.error || 'Registration failed');
      }
    } catch (error) {
      toast.error('Registration failed. Please try again.');
    }
  };

  const canProceedToNextStep = (): boolean => {
    switch (currentStep) {
      case 1:
        return formData.isContactVerified;
      case 2:
        return referralValidation?.isValid || false;
      case 3:
        return !!(formData.aadhaarFrontUrl && formData.aadhaarBackUrl && formData.panCardUrl);
      case 4:
        return !!(formData.accountNumber && formData.ifscCode && formData.accountHolderName && formData.bankName);
      case 5:
        return !!(formData.firstName && formData.lastName && formData.username && formData.password && formData.confirmPassword);
      default:
        return false;
    }
  };

  const getStepTitle = (): string => {
    switch (currentStep) {
      case 1: return 'Verify Contact';
      case 2: return 'Referral Code';
      case 3: return 'Upload Documents';
      case 4: return 'Banking Details';
      case 5: return 'Personal Information';
      default: return '';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-2xl">🌿</span>
            </div>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Join Start Juicce
          </h1>
          <p className="text-sm sm:text-base text-gray-600 mb-4">
            Complete KYC verification to get started
          </p>

          {/* Step Indicator */}
          <div className="flex items-center justify-center space-x-2 sm:space-x-4 mb-6">
            {Array.from({ length: TOTAL_STEPS }, (_, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                  index + 1 < currentStep
                    ? 'bg-green-600 text-white'
                    : index + 1 === currentStep
                    ? 'bg-green-100 text-green-600 border-2 border-green-600'
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {index + 1 < currentStep ? <Check className="w-4 h-4" /> : index + 1}
                </div>
                {index < TOTAL_STEPS - 1 && (
                  <div className={`w-8 sm:w-12 h-1 mx-1 ${
                    index + 1 < currentStep ? 'bg-green-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>

          <h2 className="text-lg sm:text-xl font-semibold text-gray-800">
            Step {currentStep}: {getStepTitle()}
          </h2>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8">
          {/* Step 1: Contact Verification */}
          {currentStep === 1 && (
            <div className="space-y-6">
              {/* Contact Method Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Choose verification method
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, contactMethod: 'mobile' }))}
                    className={`p-4 border-2 rounded-lg flex items-center justify-center space-x-2 transition-colors ${
                      formData.contactMethod === 'mobile'
                        ? 'border-green-600 bg-green-50 text-green-700'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Phone className="w-5 h-5" />
                    <span className="font-medium">Mobile</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, contactMethod: 'email' }))}
                    className={`p-4 border-2 rounded-lg flex items-center justify-center space-x-2 transition-colors ${
                      formData.contactMethod === 'email'
                        ? 'border-green-600 bg-green-50 text-green-700'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Mail className="w-5 h-5" />
                    <span className="font-medium">Email</span>
                  </button>
                </div>
              </div>

              {/* Contact Input */}
              {formData.contactMethod === 'mobile' ? (
                <div>
                  <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-2">
                    Mobile Number
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">+91</span>
                    </div>
                    <input
                      id="mobile"
                      name="mobile"
                      type="tel"
                      value={formData.mobile}
                      onChange={handleChange}
                      className="block w-full pl-12 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                      placeholder="Enter 10-digit mobile number"
                      maxLength={10}
                    />
                  </div>
                </div>
              ) : (
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                      placeholder="Enter your email address"
                    />
                  </div>
                </div>
              )}

              {/* Send OTP Button */}
              <button
                type="button"
                onClick={handleSendOTP}
                disabled={otpTimer > 0 || formData.isContactVerified}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target"
              >
                {formData.isContactVerified
                  ? 'Contact Verified'
                  : otpTimer > 0
                  ? `Resend OTP in ${otpTimer}s`
                  : 'Send OTP'
                }
              </button>

              {/* OTP Input */}
              {(otpTimer > 0 || !canResendOtp) && !formData.isContactVerified && (
                <div>
                  <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-2">
                    Enter OTP
                  </label>
                  <div className="flex space-x-2">
                    <input
                      id="otp"
                      name="otp"
                      type="text"
                      value={formData.otp}
                      onChange={handleChange}
                      className="flex-1 px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-center text-lg font-mono touch-target"
                      placeholder="000000"
                      maxLength={6}
                    />
                    <button
                      type="button"
                      onClick={handleVerifyOTP}
                      disabled={formData.otp.length !== 6}
                      className="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target"
                    >
                      Verify
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    Check the green notification above for your demo OTP code
                  </p>
                </div>
              )}

              {/* Verification Status */}
              {formData.isContactVerified && (
                <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">Contact verified successfully!</span>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Referral Code Verification */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="text-center">
                <Users className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Referral Code Required
                </h3>
                <p className="text-gray-600">
                  Enter the referral code you received to continue registration
                </p>
              </div>

              <div>
                <label htmlFor="referralCode" className="block text-sm font-medium text-gray-700 mb-2">
                  Referral Code *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Users className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="referralCode"
                    name="referralCode"
                    type="text"
                    value={referralCode}
                    onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                    className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                    placeholder="Enter referral code"
                  />
                  {validatingReferral && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <RefreshCw className="h-5 w-5 text-gray-400 animate-spin" />
                    </div>
                  )}
                  {referralValidation?.isValid && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                  )}
                </div>
              </div>

              <button
                type="button"
                onClick={handleVerifyReferral}
                disabled={!referralCode.trim() || validatingReferral || referralValidation?.isValid}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target"
              >
                {validatingReferral ? 'Verifying...' : referralValidation?.isValid ? 'Verified' : 'Verify Referral Code'}
              </button>

              {formData.isReferralVerified && (
                <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">Referral code verified successfully!</span>
                </div>
              )}

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Don't have a referral code?{' '}
                  <Link to="/contact" className="text-green-600 hover:text-green-700 font-medium">
                    Contact us
                  </Link>
                </p>
              </div>
            </div>
          )}
          {/* Step 3: Document Upload */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="text-center">
                <Shield className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Upload KYC Documents
                </h3>
                <p className="text-gray-600">
                  Please upload clear photos of your documents for verification
                </p>
              </div>

              {/* Aadhaar Front */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Aadhaar Card (Front) *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                  {formData.aadhaarFront ? (
                    <div className="space-y-2">
                      <FileText className="w-8 h-8 text-green-600 mx-auto" />
                      <p className="text-sm text-gray-600">{formData.aadhaarFront.name}</p>
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, aadhaarFront: null }))}
                        className="text-red-600 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                      <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                      <p className="text-xs text-gray-500">JPG, PNG, PDF (max 5MB)</p>
                    </div>
                  )}
                  <input
                    type="file"
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={(e) => handleFileChange(e, 'aadhaarFront')}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
              </div>

              {/* Aadhaar Back */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Aadhaar Card (Back) *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative">
                  {formData.aadhaarBack ? (
                    <div className="space-y-2">
                      <FileText className="w-8 h-8 text-green-600 mx-auto" />
                      <p className="text-sm text-gray-600">{formData.aadhaarBack.name}</p>
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, aadhaarBack: null }))}
                        className="text-red-600 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                      <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                      <p className="text-xs text-gray-500">JPG, PNG, PDF (max 5MB)</p>
                    </div>
                  )}
                  <input
                    type="file"
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={(e) => handleFileChange(e, 'aadhaarBack')}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
              </div>

              {/* PAN Card */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  PAN Card *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors relative">
                  {formData.panCard ? (
                    <div className="space-y-2">
                      <FileText className="w-8 h-8 text-green-600 mx-auto" />
                      <p className="text-sm text-gray-600">{formData.panCard.name}</p>
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, panCard: null }))}
                        className="text-red-600 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                      <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                      <p className="text-xs text-gray-500">JPG, PNG, PDF (max 5MB)</p>
                    </div>
                  )}
                  <input
                    type="file"
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={(e) => handleFileChange(e, 'panCard')}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
              </div>

              <button
                type="button"
                onClick={handleUploadDocuments}
                disabled={!formData.aadhaarFront || !formData.aadhaarBack || !formData.panCard || isUploadingFiles}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-target"
              >
                {isUploadingFiles ? 'Uploading Documents...' : 'Upload Documents'}
              </button>
            </div>
          )}
          {/* Step 4: Banking Details */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="text-center">
                <Building className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Banking Information
                </h3>
                <p className="text-gray-600">
                  Enter your Indian bank account details for transactions
                </p>
              </div>

              <div>
                <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Account Number *
                </label>
                <input
                  id="accountNumber"
                  name="accountNumber"
                  type="text"
                  value={formData.accountNumber}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                  placeholder="Enter 9-18 digit account number"
                  maxLength={18}
                />
              </div>

              <div>
                <label htmlFor="ifscCode" className="block text-sm font-medium text-gray-700 mb-2">
                  IFSC Code *
                </label>
                <input
                  id="ifscCode"
                  name="ifscCode"
                  type="text"
                  value={formData.ifscCode}
                  onChange={handleIFSCChange}
                  className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target uppercase"
                  placeholder="e.g., SBIN0001234"
                  maxLength={11}
                />
              </div>

              <div>
                <label htmlFor="accountHolderName" className="block text-sm font-medium text-gray-700 mb-2">
                  Account Holder Name *
                </label>
                <input
                  id="accountHolderName"
                  name="accountHolderName"
                  type="text"
                  value={formData.accountHolderName}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                  placeholder="Enter name as per bank records"
                />
              </div>

              <div>
                <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-2">
                  Bank Name *
                </label>
                <select
                  id="bankName"
                  name="bankName"
                  value={formData.bankName}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                >
                  <option value="">Select your bank</option>
                  {INDIAN_BANKS.map((bank) => (
                    <option key={bank} value={bank}>
                      {bank}
                    </option>
                  ))}
                </select>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Your banking information is encrypted and securely stored.
                  This information is required for processing payments and withdrawals.
                </p>
              </div>
            </div>
          )}

          {/* Step 5: Personal Information */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <div className="text-center">
                <User className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Personal Information
                </h3>
                <p className="text-gray-600">
                  Complete your profile to finish registration
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleChange}
                    className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={handleChange}
                    className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                    placeholder="Enter last name"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                  Username *
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleChange}
                  className="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                  placeholder="Choose a unique username"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password *
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleChange}
                    className="block w-full px-3 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                    placeholder="Create a strong password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                  Confirm Password *
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="block w-full px-3 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500 text-base touch-target"
                    placeholder="Confirm your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-800">
                  <strong>Almost done!</strong> Review your information and complete registration.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex flex-col sm:flex-row justify-between items-center mt-8 space-y-4 sm:space-y-0 sm:space-x-4">
          {/* Previous Button */}
          <button
            type="button"
            onClick={handlePrevStep}
            disabled={currentStep === 1}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors touch-target ${
              currentStep === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>

          {/* Next/Submit Button */}
          {currentStep < TOTAL_STEPS ? (
            <button
              type="button"
              onClick={handleNextStep}
              disabled={!canProceedToNextStep()}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors touch-target ${
                canProceedToNextStep()
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              <span>Next</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          ) : (
            <button
              type="button"
              onClick={handleFinalSubmit}
              disabled={!canProceedToNextStep() || isLoading}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors touch-target ${
                canProceedToNextStep() && !isLoading
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Creating Account...</span>
                </>
              ) : (
                <>
                  <span>Complete Registration</span>
                  <CheckCircle className="w-4 h-4" />
                </>
              )}
            </button>
          )}
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <Link
              to="/login"
              className="font-medium text-green-600 hover:text-green-500"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;