import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import toast from 'react-hot-toast';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, error, isLoading, resetLoadingState } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  const [formError, setFormError] = useState('');
  const [localLoading, setLocalLoading] = useState(false);

  const from = location.state?.from?.pathname || '/';

  // Cleanup effect to reset states when component unmounts
  useEffect(() => {
    return () => {
      setLocalLoading(false);
      setFormError('');
    };
  }, []);

  // Reset loading states when component mounts
  useEffect(() => {
    resetLoadingState();
    setLocalLoading(false);
  }, [resetLoadingState]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');
    setLocalLoading(true);

    // Reset any stuck loading state first
    resetLoadingState();

    if (!email || !password) {
      setFormError('Please enter both email and password');
      setLocalLoading(false);
      return;
    }

    // Set a safety timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('⏰ Form submission timeout - resetting states');
      setLocalLoading(false);
      resetLoadingState();
      setFormError('Request timed out. Please try again.');
    }, 35000); // 35 seconds

    try {
      console.log('🔐 Attempting sign in with:', email);
      await signIn(email, password);
      console.log('✅ Sign in successful, navigating to dashboard');
      clearTimeout(timeoutId);
      setLocalLoading(false);
      navigate('/dashboard');
    } catch (error: any) {
      console.error('❌ Sign in error:', error);
      clearTimeout(timeoutId);
      setLocalLoading(false);
      resetLoadingState(); // Ensure loading state is reset
      setFormError(error.message || 'Sign in failed. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-2xl">🌿</span>
            </div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Welcome back
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Don't have an account?{' '}
            <Link
              to="/register"
              className="font-medium text-green-600 hover:text-green-500"
            >
              Sign up
            </Link>
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm"
                  placeholder="Email address"
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none relative block w-full pl-10 pr-10 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm"
                  placeholder="Password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <Link
                to="/forgot-password"
                className="font-medium text-green-600 hover:text-green-500"
              >
                Forgot your password?
              </Link>
            </div>
          </div>

          {(formError || error) && (
            <div className="text-red-500 text-sm text-center">
              {formError || error}
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading || localLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {(isLoading || localLoading) ? 'Signing in...' : 'Sign in'}
            </button>

            {/* Emergency reset button - only show if stuck loading */}
            {(isLoading || localLoading) && (
              <button
                type="button"
                onClick={() => {
                  setLocalLoading(false);
                  resetLoadingState();
                  setFormError('');
                  console.log('🔄 Emergency reset triggered');
                }}
                className="mt-2 w-full text-xs text-gray-500 hover:text-gray-700 underline"
              >
                Reset if stuck
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;