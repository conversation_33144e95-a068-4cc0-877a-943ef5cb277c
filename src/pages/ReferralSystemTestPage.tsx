import React, { useState, useEffect } from 'react';
import { referralSystemTester, TestResult } from '../utils/referralSystemTest';
import { useAuthStore } from '../stores/authStore';

interface TestSummary {
  overall_success: boolean;
  total_tests: number;
  passed_tests: number;
  failed_tests: number;
  results: TestResult[];
}

const ReferralSystemTestPage: React.FC = () => {
  const { user } = useAuthStore();
  const [testSummary, setTestSummary] = useState<TestSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedTest, setSelectedTest] = useState<TestResult | null>(null);

  useEffect(() => {
    // Auto-run tests when page loads
    runTests();
  }, []);

  const runTests = async () => {
    setLoading(true);
    try {
      const summary = await referralSystemTester.runAllTests();
      setTestSummary(summary);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setLoading(false);
    }
  };

  const TestResultCard: React.FC<{ result: TestResult; onClick: () => void }> = ({ result, onClick }) => (
    <div 
      className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
        result.success 
          ? 'bg-green-50 border-green-200 hover:bg-green-100' 
          : 'bg-red-50 border-red-200 hover:bg-red-100'
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">{result.test_name}</h3>
        <span className={`text-2xl ${result.success ? 'text-green-600' : 'text-red-600'}`}>
          {result.success ? '✅' : '❌'}
        </span>
      </div>
      <p className="text-sm text-gray-600 mt-1">{result.message}</p>
      {result.error && (
        <p className="text-xs text-red-600 mt-2 bg-red-100 p-2 rounded">
          {result.error}
        </p>
      )}
    </div>
  );

  const TestDetailsModal: React.FC<{ test: TestResult; onClose: () => void }> = ({ test, onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">{test.test_name}</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
            >
              ×
            </button>
          </div>
          
          <div className={`p-4 rounded-lg mb-4 ${
            test.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">{test.success ? '✅' : '❌'}</span>
              <span className={`font-semibold ${test.success ? 'text-green-800' : 'text-red-800'}`}>
                {test.success ? 'PASSED' : 'FAILED'}
              </span>
            </div>
            <p className="text-sm">{test.message}</p>
          </div>

          {test.error && (
            <div className="bg-red-100 border border-red-200 p-4 rounded-lg mb-4">
              <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
              <p className="text-sm text-red-700">{test.error}</p>
            </div>
          )}

          {test.details && (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-2">Test Details:</h3>
              <pre className="text-xs text-gray-600 overflow-auto">
                {JSON.stringify(test.details, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">🔍 Referral System Test Suite</h1>
            <p className="text-gray-600">
              Comprehensive testing of database connectivity and MLM referral logic
            </p>
          </div>

          {/* User Info */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h2 className="font-semibold text-blue-800 mb-2">Current User</h2>
            {user ? (
              <div className="text-sm text-blue-700">
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Status:</strong> Authenticated ✅</p>
              </div>
            ) : (
              <p className="text-red-600">Not authenticated ❌</p>
            )}
          </div>

          {/* Test Controls */}
          <div className="text-center mb-8">
            <button
              onClick={runTests}
              disabled={loading}
              className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-semibold"
            >
              {loading ? 'Running Tests...' : 'Run All Tests'}
            </button>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Running comprehensive tests...</p>
            </div>
          )}

          {/* Test Results Summary */}
          {testSummary && (
            <div className="space-y-6">
              <div className={`p-6 rounded-lg text-center ${
                testSummary.overall_success 
                  ? 'bg-green-100 text-green-800 border border-green-200' 
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}>
                <h2 className="text-2xl font-bold mb-2">
                  {testSummary.overall_success ? '✅ All Tests Passed!' : '❌ Some Tests Failed'}
                </h2>
                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div>
                    <div className="text-3xl font-bold">{testSummary.total_tests}</div>
                    <div className="text-sm">Total Tests</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600">{testSummary.passed_tests}</div>
                    <div className="text-sm">Passed</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-red-600">{testSummary.failed_tests}</div>
                    <div className="text-sm">Failed</div>
                  </div>
                </div>
              </div>

              {/* Individual Test Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {testSummary.results.map((result, index) => (
                  <TestResultCard
                    key={index}
                    result={result}
                    onClick={() => setSelectedTest(result)}
                  />
                ))}
              </div>

              {/* System Status */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h2 className="text-xl font-semibold mb-4">🎯 System Status</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="font-semibold">Database</h3>
                    <p className={`text-sm ${
                      testSummary.results.find(r => r.test_name === 'Database Structure')?.success 
                        ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {testSummary.results.find(r => r.test_name === 'Database Structure')?.success 
                        ? '✅ Connected and accessible' : '❌ Connection issues'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold">MLM Logic</h3>
                    <p className={`text-sm ${
                      testSummary.results.find(r => r.test_name === 'MLM Logic Calculation')?.success 
                        ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {testSummary.results.find(r => r.test_name === 'MLM Logic Calculation')?.success 
                        ? '✅ Logic working correctly' : '❌ Logic issues detected'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold">Referral Codes</h3>
                    <p className={`text-sm ${
                      testSummary.results.find(r => r.test_name === 'Referral Code Generation')?.success 
                        ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {testSummary.results.find(r => r.test_name === 'Referral Code Generation')?.success 
                        ? '✅ Generation working' : '❌ Generation issues'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold">Wallet System</h3>
                    <p className={`text-sm ${
                      testSummary.results.find(r => r.test_name === 'Wallet Transaction Creation')?.success 
                        ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {testSummary.results.find(r => r.test_name === 'Wallet Transaction Creation')?.success 
                        ? '✅ Transactions accessible' : '❌ Transaction issues'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Test Details Modal */}
          {selectedTest && (
            <TestDetailsModal
              test={selectedTest}
              onClose={() => setSelectedTest(null)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ReferralSystemTestPage;
