import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Shield, Truck, Award, Crown, Users, Zap, Leaf, Cookie, Flower, Syringe, Scale, Sprout, TestTube, FlaskConical, Package } from 'lucide-react';
import { useProductStore } from '../stores/productStore';
import { useAuthStore } from '../stores/authStore';
import { ProductCard } from '../components/products/ProductCard';
import { getRandomProductImage, DEFAULT_HERO_IMAGE } from '../utils/imageUtils';

const useIntersectionObserver = (options: IntersectionObserverInit) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    }, options);

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current);
      }
      observer.disconnect();
    };
  }, [options]);

  return [elementRef, isIntersecting] as const;
};

const HomePage: React.FC = () => {
  const { featuredProducts, fetchFeaturedProducts, isLoading } = useProductStore();
  const { user } = useAuthStore();

  const [professionalServicesRef, professionalServicesVisible] = useIntersectionObserver({ threshold: 0.1 });
  const [featuresRef, featuresVisible] = useIntersectionObserver({ threshold: 0.1 });
  const [premiumBenefitsRef, premiumBenefitsVisible] = useIntersectionObserver({ threshold: 0.1 });
  const [ourBestServicesRef, ourBestServicesVisible] = useIntersectionObserver({ threshold: 0.1 });
  const [weAreWhatYouNeedRef, weAreWhatYouNeedVisible] = useIntersectionObserver({ threshold: 0.1 });

  useEffect(() => {
    fetchFeaturedProducts();
  }, []);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-emerald-800 to-green-600 py-16 sm:py-20 lg:py-28 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div className="space-y-6 sm:space-y-8 text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold text-white leading-tight drop-shadow-lg animate-fade-in-down">
                Natural Wellness
                <span className="block text-lime-400">Your Path to Health</span>
              </h1>
              <p className="text-lg sm:text-xl lg:text-2xl text-green-50 leading-relaxed max-w-2xl mx-auto lg:mx-0 animate-fade-in-up">
                Discover premium natural products crafted from nature's finest ingredients.
                Your journey to holistic well-being starts here with trusted quality and expertise.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start pt-4">
                <Link
                  to="/products"
                  className="inline-flex items-center justify-center px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 bg-white text-emerald-800 font-bold rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg touch-target"
                >
                  Explore Products
                  <ArrowRight className="ml-2 sm:ml-3 h-4 w-4 sm:h-5 sm:w-5" />
                </Link>
                <Link
                  to="/about"
                  className="inline-flex items-center justify-center px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 bg-transparent text-white font-bold rounded-full border-2 border-white hover:bg-white hover:text-emerald-800 transition-all duration-300 transform hover:scale-105 shadow-lg touch-target"
                >
                  Learn More
                  <ArrowRight className="ml-2 sm:ml-3 h-4 w-4 sm:h-5 sm:w-5" />
                </Link>
              </div>
            </div>
            <div className="relative hidden lg:block animate-fade-in-right">
              <img
                src={DEFAULT_HERO_IMAGE}
                alt="Natural Products"
                className="w-full h-[400px] lg:h-[500px] object-cover rounded-3xl shadow-2xl rotate-3 transform transition-transform duration-500 hover:rotate-0 hover:scale-105"
              />
              <div className="absolute inset-0 bg-black opacity-10 rounded-3xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Services Section */}
      <section
        ref={professionalServicesRef}
        className={`py-12 sm:py-16 bg-white relative transition-all duration-1000 ${professionalServicesVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mobile/Tablet Layout */}
          <div className="lg:hidden">
            <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg">
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
                Professional services
              </h2>
              <p className="text-gray-700 mb-6 sm:mb-8">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit
                tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Zap className="w-6 h-6 sm:w-10 sm:h-10 text-green-600" />
                  </div>
                  <span className="text-base sm:text-lg font-semibold text-gray-900">Marketing</span>
                </div>
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Truck className="w-6 h-6 sm:w-10 sm:h-10 text-green-600" />
                  </div>
                  <span className="text-base sm:text-lg font-semibold text-gray-900">Distribution</span>
                </div>
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shield className="w-6 h-6 sm:w-10 sm:h-10 text-green-600" />
                  </div>
                  <span className="text-base sm:text-lg font-semibold text-gray-900">Insurance</span>
                </div>
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Award className="w-6 h-6 sm:w-10 sm:h-10 text-green-600" />
                  </div>
                  <span className="text-base sm:text-lg font-semibold text-gray-900">Consultation</span>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex justify-end">
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/d/db/Cannabis_sativa_001.JPG"
              alt="Cannabis Plant"
              className="absolute left-0 top-0 h-full w-3/5 object-cover opacity-100 z-0"
            />
            <div className="bg-white p-8 rounded-xl shadow-lg relative z-10 w-2/5 ml-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Professional services
              </h2>
              <p className="text-gray-700 mb-8">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit
                tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Zap className="w-10 h-10 text-green-600" />
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Marketing</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Truck className="w-10 h-10 text-green-600" />
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Distribution</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shield className="w-10 h-10 text-green-600" />
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Insurance</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Award className="w-10 h-10 text-green-600" />
                  </div>
                  <span className="text-lg font-semibold text-gray-900">Consultation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section
        ref={featuresRef}
        className={`py-12 sm:py-16 bg-white transition-all duration-1000 ${featuresVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Why Choose Start Juicce?
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              We're committed to providing you with the highest quality natural products
              and an exceptional shopping experience.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            <div className="text-center p-4 sm:p-6">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">
                Premium Quality
              </h3>
              <p className="text-sm sm:text-base text-gray-600">
                All our products are sourced from certified organic farms and
                undergo rigorous quality testing.
              </p>
            </div>

            <div className="text-center p-4 sm:p-6">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <Truck className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">
                Fast Delivery
              </h3>
              <p className="text-sm sm:text-base text-gray-600">
                Free shipping on orders over ₹500 with express delivery
                options available nationwide.
              </p>
            </div>

            <div className="text-center p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">
                Expert Support
              </h3>
              <p className="text-sm sm:text-base text-gray-600">
                Our team of wellness experts is available to guide you in
                choosing the right products for your needs.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Premium Section */}
      <section
        ref={premiumBenefitsRef}
        className={`py-16 ${user?.is_premium ? 'bg-gradient-to-br from-yellow-50 via-amber-50 to-yellow-100' : 'bg-gradient-to-br from-yellow-100 via-amber-100 to-yellow-200'} transition-all duration-1000 ${premiumBenefitsVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {user?.is_premium ? (
            // Premium User Content
            <>
              <div className="text-center mb-12">
                <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-200 to-amber-200 px-4 py-2 rounded-full mb-4 border border-yellow-300">
                  <Crown className="h-5 w-5 text-yellow-600" />
                  <span className="text-yellow-800 font-medium">Premium Member</span>
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Welcome to Premium!
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  You're now part of our exclusive premium community. Enjoy all the benefits and
                  explore our premium-only products and features.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div className="bg-white p-6 rounded-xl shadow-lg border-2 border-yellow-300 bg-gradient-to-br from-white to-yellow-50">
                  <div className="flex items-center justify-between mb-4">
                    <Zap className="h-8 w-8 text-yellow-600" />
                    <span className="bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full border border-yellow-300">ACTIVE</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Exclusive Products
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Access to premium-only natural formulations and limited edition products.
                  </p>
                  <Link to="/products?premium=true" className="text-yellow-600 font-semibold hover:underline hover:text-yellow-700">
                    Browse Premium Products →
                  </Link>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border-2 border-yellow-300 bg-gradient-to-br from-white to-yellow-50">
                  <div className="flex items-center justify-between mb-4">
                    <Award className="h-8 w-8 text-yellow-600" />
                    <span className="bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full border border-yellow-300">25% OFF</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Special Discounts
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Enjoy up to 25% off on all products plus exclusive member-only sales.
                  </p>
                  <Link to="/products" className="text-yellow-600 font-semibold hover:underline hover:text-yellow-700">
                    Shop with Discount →
                  </Link>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border-2 border-yellow-300 bg-gradient-to-br from-white to-yellow-50">
                  <div className="flex items-center justify-between mb-4">
                    <Users className="h-8 w-8 text-yellow-600" />
                    <span className="bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full border border-yellow-300">EARN</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Referral Rewards
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Earn wallet credits for every friend you refer who joins our platform.
                  </p>
                  <Link to="/dashboard/referrals" className="text-yellow-600 font-semibold hover:underline hover:text-yellow-700">
                    View Referrals →
                  </Link>
                </div>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center space-x-4">
                  <Link
                    to="/dashboard"
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-500 to-amber-500 text-white font-semibold rounded-full hover:from-yellow-600 hover:to-amber-600 transition-all duration-300 shadow-lg touch-target"
                  >
                    Go to Dashboard
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                  <Link
                    to="/products?premium=true"
                    className="inline-flex items-center px-6 py-3 bg-white text-yellow-600 font-semibold rounded-full border-2 border-yellow-500 hover:bg-yellow-50 hover:border-yellow-600 transition-all duration-300 shadow-lg touch-target"
                  >
                    <Crown className="mr-2 h-5 w-5" />
                    Premium Products
                  </Link>
                </div>
              </div>
            </>
          ) : (
            // Non-Premium User Content
            <>
              <div className="text-center mb-12">
                <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-amber-200 to-yellow-300 px-4 py-2 rounded-full mb-4 border border-amber-300">
                  <Crown className="h-5 w-5 text-amber-700" />
                  <span className="text-amber-800 font-medium">Premium Membership</span>
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Unlock Exclusive Benefits
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Join our premium community and enjoy exclusive products, special discounts,
                  and amazing referral rewards.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-amber-200 bg-gradient-to-br from-white to-amber-50">
                  <Zap className="h-8 w-8 text-amber-600 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Exclusive Products
                  </h3>
                  <p className="text-gray-600">
                    Access to premium-only natural formulations and limited edition products.
                  </p>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-amber-200 bg-gradient-to-br from-white to-amber-50">
                  <Award className="h-8 w-8 text-amber-600 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Special Discounts
                  </h3>
                  <p className="text-gray-600">
                    Enjoy up to 25% off on all products plus exclusive member-only sales.
                  </p>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-amber-200 bg-gradient-to-br from-white to-amber-50">
                  <Users className="h-8 w-8 text-amber-600 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Referral Rewards
                  </h3>
                  <p className="text-gray-600">
                    Earn wallet credits for every friend you refer who joins our platform.
                  </p>
                </div>
              </div>

              <div className="text-center">
                <Link
                  to="/premium"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-amber-500 to-yellow-600 text-white font-semibold rounded-full hover:from-amber-600 hover:to-yellow-700 transition-all duration-300 shadow-lg transform hover:scale-105 touch-target"
                >
                  <Crown className="mr-2 h-5 w-5" />
                  Upgrade to Premium
                </Link>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Our New Products
            </h2>
            <p className="text-lg sm:text-xl text-gray-600">
              Vulpulpate enim nulla aliquet porttitor lacus luctus accumsan. Libero volutpat sed cras
              ornare arcu dui suspendisse vivamus.
            </p>
          </div>

          {isLoading ? (
            <div className="responsive-grid">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white rounded-xl shadow-sm p-4 animate-pulse">
                  <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="responsive-grid">
              {featuredProducts.map((product) => {
                const fallbackImage = getRandomProductImage();
                return (
                  <ProductCard key={product.id} product={product} fallbackImageSrc={fallbackImage} />
                );
              })}
            </div>
          )}
        </div>
      </section>

      {/* Our Best Services Section */}
      <section 
        ref={ourBestServicesRef}
        className={`py-16 bg-white transition-all duration-1000 ${ourBestServicesVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Best Services
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Bibendum ut tristique et egestas quis ipsum suspendisse. Quis risus sed accumsan
              vulputate odio ut elementum.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Leaf className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Marijuana Medicine</h3>
              <p className="text-gray-600">Libero volutpat sed cras ornare arcu dui suspendisse vivamus.</p>
              <Link to="#" className="text-emerald-600 font-semibold mt-4 inline-block hover:underline">Read More</Link>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Leaf className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Cannabis Leaf</h3>
              <p className="text-gray-600">Malesuada proin libero nunc consequat interdum.</p>
              <Link to="#" className="text-emerald-600 font-semibold mt-4 inline-block hover:underline">Read More</Link>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Cookie className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">100% Natural Edibles</h3>
              <p className="text-gray-600">Vestibulum mattis ullamcorper velit sed ullamco.</p>
              <Link to="#" className="text-emerald-600 font-semibold mt-4 inline-block hover:underline">Read More</Link>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Flower className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Cannabis Flower</h3>
              <p className="text-gray-600">Odio facilisis mauris sit amet massa vitae tortor.</p>
              <Link to="#" className="text-emerald-600 font-semibold mt-4 inline-block hover:underline">Read More</Link>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Syringe className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">100% Fresh Apothecary</h3>
              <p className="text-gray-600">Faucibus turpis in eu mi bibendum neque egestas.</p>
              <Link to="#" className="text-emerald-600 font-semibold mt-4 inline-block hover:underline">Read More</Link>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm text-center border border-gray-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Scale className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Safe & Legal</h3>
              <p className="text-gray-600">Dolor sit amet, consectetur adipiscing elit etiam.</p>
              <Link to="#" className="text-emerald-600 font-semibold mt-4 inline-block hover:underline">Read More</Link>
            </div>
          </div>
        </div>
      </section>

      {/* We Are What You Need Section */}
      <section 
        ref={weAreWhatYouNeedRef}
        className={`py-16 bg-emerald-800 text-white transition-all duration-1000 ${weAreWhatYouNeedVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            We Are What You Need
          </h2>
          <p className="text-xl text-green-100 mb-12 max-w-3xl mx-auto">
            Consectetur libero sed tincidunt eget nullam non nisi. Non diam phasellus
            vestibulum lorem sed risus ultricies elementum.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="flex flex-col items-center p-6">
              <div className="w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6">
                <Sprout className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Organic Cannabis</h3>
              <p className="text-green-100 mb-4">
                Amet aliquam id diam maecenas ultricies eget mauris pharetra.
              </p>
              <Link to="#" className="flex items-center text-lime-400 font-semibold hover:underline">
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>
            <div className="flex flex-col items-center p-6">
              <div className="w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6">
                <TestTube className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Extraction Procedure</h3>
              <p className="text-green-100 mb-4">
                Marwuves arcu felis bibendum ut tristique. In ante met dictum.
              </p>
              <Link to="#" className="flex items-center text-lime-400 font-semibold hover:underline">
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>
            <div className="flex flex-col items-center p-6">
              <div className="w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6">
                <FlaskConical className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Formulations</h3>
              <p className="text-green-100 mb-4">
                Phyes orese felis bibendum set inr ut tristique sei nullam non.
              </p>
              <Link to="#" className="flex items-center text-lime-400 font-semibold hover:underline">
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>
            <div className="flex flex-col items-center p-6">
              <div className="w-20 h-20 bg-green-700 rounded-full flex items-center justify-center mb-6">
                <Package className="w-12 h-12 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Free Delivery</h3>
              <p className="text-green-100 mb-4">
                Non diam phasellus vestibulum lorem sed risus ultricies elementum.
              </p>
              <Link to="#" className="flex items-center text-lime-400 font-semibold hover:underline">
                <ArrowRight className="h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-green-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated with Start Juicce
          </h2>
          <p className="text-xl text-green-100 mb-8">
            Get the latest updates on new products, health tips, and exclusive offers.
          </p>
          <div className="flex flex-col sm:flex-row max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-6 py-4 rounded-l-full sm:rounded-r-none rounded-r-full border-0 focus:outline-none focus:ring-2 focus:ring-green-300"
            />
            <button className="px-8 py-4 bg-green-800 text-white font-semibold rounded-r-full sm:rounded-l-none rounded-l-full hover:bg-green-900 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;