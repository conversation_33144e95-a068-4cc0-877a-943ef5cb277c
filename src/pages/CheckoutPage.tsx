import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CreditCard, Wallet, MapPin, Package } from 'lucide-react';
import { useCartStore } from '../stores/cartStore';
import { useWalletStore } from '../stores/walletStore';
import { useAuthStore } from '../stores/authStore';
import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';

export default function CheckoutPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { items, total, clearCart } = useCartStore();
  const { balance, deductMoney } = useWalletStore();
  
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'wallet' | 'card' | 'mixed'>('card');
  const [walletAmount, setWalletAmount] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    
    if (items.length === 0) {
      navigate('/cart');
      return;
    }

    // Set default address if user has one
    if (user.address) {
      setDeliveryAddress(user.address);
    }
  }, [user, items, navigate]);

  const remainingAmount = Math.max(0, total - walletAmount);

  const handleWalletAmountChange = (amount: number) => {
    const maxWalletAmount = Math.min(balance, total);
    const newAmount = Math.max(0, Math.min(amount, maxWalletAmount));
    setWalletAmount(newAmount);
    
    if (newAmount === total) {
      setPaymentMethod('wallet');
    } else if (newAmount > 0) {
      setPaymentMethod('mixed');
    } else {
      setPaymentMethod('card');
    }
  };

  const handlePlaceOrder = async () => {
    if (!deliveryAddress.trim()) {
      toast.error('Please enter delivery address');
      return;
    }

    if (paymentMethod === 'wallet' && balance < total) {
      toast.error('Insufficient wallet balance');
      return;
    }

    setLoading(true);

    try {
      // Generate order number
      const orderNumber = `ORD${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(Date.now()).slice(-6)}`;

      // Create order with enhanced data
      const orderData = {
        user_id: user!.id,
        order_number: orderNumber,
        total_amount: total,
        wallet_used: walletAmount,
        payment_method: paymentMethod,
        delivery_address: deliveryAddress,
        shipping_cost: 0, // Free shipping for now
        tax_amount: 0, // No tax for now
        status: 'pending',
        estimated_delivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 3 days from now
      };

      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert(orderData)
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems = items.map(item => ({
        order_id: order.id,
        product_id: item.product.id,
        quantity: item.quantity,
        price: item.product.price
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Deduct wallet amount if used
      if (walletAmount > 0) {
        const success = await deductMoney(
          walletAmount,
          `Payment for order #${order.id}`,
          'order',
          order.id
        );
        
        if (!success) {
          throw new Error('Failed to deduct wallet amount');
        }
      }

      // Update product stock using inventory management function
      for (const item of items) {
        const { data: inventoryResult, error: inventoryError } = await supabase.rpc('process_inventory_transaction', {
          p_product_id: item.product.id,
          p_transaction_type: 'sale',
          p_quantity_change: -item.quantity, // Negative for sale
          p_reference_type: 'order',
          p_reference_id: order.id,
          p_notes: `Sale - Order ${orderNumber}`
        });

        if (inventoryError || !inventoryResult?.success) {
          throw new Error(`Failed to update inventory for ${item.product.name}`);
        }
      }

      // Create order notification
      await supabase.from('order_notifications').insert({
        order_id: order.id,
        user_id: user!.id,
        notification_type: 'order_placed',
        title: 'Order Placed Successfully!',
        message: `Your order ${orderNumber} has been placed successfully. We'll notify you when it's confirmed.`,
        sent_via: ['in_app']
      });

      // Clear cart
      clearCart();

      toast.success(`Order ${orderNumber} placed successfully!`);
      navigate(`/dashboard/orders`);
    } catch (error) {
      console.error('Error placing order:', error);
      toast.error('Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!user || items.length === 0) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 sm:py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8">Checkout</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Delivery Address */}
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-600" />
                Delivery Address
              </h2>
              <textarea
                value={deliveryAddress}
                onChange={(e) => setDeliveryAddress(e.target.value)}
                placeholder="Enter your complete delivery address..."
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm sm:text-base"
                rows={4}
                required
              />
            </div>

            {/* Payment Method */}
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-green-600" />
                Payment Method
              </h2>

              {/* Wallet Payment Option */}
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-gray-200 rounded-lg gap-3">
                  <div className="flex items-center gap-3">
                    <Wallet className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="font-medium text-gray-900">Use Wallet</p>
                      <p className="text-sm text-gray-500">Available: ₹{balance}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">₹</span>
                    <input
                      type="number"
                      value={walletAmount}
                      onChange={(e) => handleWalletAmountChange(Number(e.target.value))}
                      max={Math.min(balance, total)}
                      min={0}
                      className="w-20 sm:w-24 p-2 border border-gray-300 rounded text-center text-sm sm:text-base"
                    />
                  </div>
                </div>

                {remainingAmount > 0 && (
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">Card Payment</p>
                        <p className="text-sm text-gray-500">Remaining: ₹{remainingAmount}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Package className="h-5 w-5 text-green-600" />
                Order Items
              </h2>
              <div className="space-y-4">
                {items.map((item) => (
                  <div key={item.id} className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 border border-gray-200 rounded-lg">
                    <img
                      src={item.product.image_url}
                      alt={item.product.name}
                      className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 text-sm sm:text-base truncate">{item.product.name}</h3>
                      <p className="text-xs sm:text-sm text-gray-500">Quantity: {item.quantity}</p>
                    </div>
                    <p className="font-semibold text-gray-900 text-sm sm:text-base">
                      ₹{item.product.price * item.quantity}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm h-fit">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between text-sm sm:text-base">
                <span className="text-gray-600">Subtotal</span>
                <span className="font-medium">₹{total}</span>
              </div>
              <div className="flex justify-between text-sm sm:text-base">
                <span className="text-gray-600">Delivery</span>
                <span className="font-medium text-green-600">Free</span>
              </div>
              {walletAmount > 0 && (
                <div className="flex justify-between text-green-600 text-sm sm:text-base">
                  <span>Wallet Used</span>
                  <span>-₹{walletAmount}</span>
                </div>
              )}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total</span>
                  <span>₹{remainingAmount}</span>
                </div>
              </div>
            </div>

            <button
              onClick={handlePlaceOrder}
              disabled={loading || !deliveryAddress.trim()}
              className="w-full bg-green-600 text-white py-3 sm:py-4 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base touch-target"
            >
              {loading ? 'Placing Order...' : 'Place Order'}
            </button>

            <p className="text-xs text-gray-500 mt-4 text-center">
              By placing this order, you agree to our terms and conditions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}