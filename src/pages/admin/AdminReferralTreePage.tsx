import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Users, 
  Crown, 
  TrendingUp, 
  Network,
  RefreshCw,
  Download,
  Filter,
  Eye
} from 'lucide-react';
import { referralTreeService, ReferralTreeNode, ReferralTreeStats } from '../../services/referralTreeService';
import ReferralTreeVisualization from '../../components/admin/ReferralTreeVisualization';
import toast from 'react-hot-toast';

const AdminReferralTreePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [currentTree, setCurrentTree] = useState<ReferralTreeNode | null>(null);
  const [currentStats, setCurrentStats] = useState<ReferralTreeStats | null>(null);
  const [networkOverview, setNetworkOverview] = useState<any>(null);
  const [searchResults, setSearchResults] = useState<ReferralTreeNode[]>([]);
  const [viewMode, setViewMode] = useState<'overview' | 'tree' | 'search'>('overview');

  useEffect(() => {
    loadNetworkOverview();
  }, []);

  const loadNetworkOverview = async () => {
    try {
      setLoading(true);
      console.log('Loading network overview...');
      const overview = await referralTreeService.getNetworkOverview();
      console.log('Network overview loaded:', overview);
      setNetworkOverview(overview);
    } catch (error) {
      console.error('Error loading network overview:', error);
      toast.error(`Failed to load network overview: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadUserTree = async (userId: string) => {
    try {
      setLoading(true);
      const { tree, stats } = await referralTreeService.getReferralTree(userId);
      setCurrentTree(tree);
      setCurrentStats(stats);
      setSelectedUser(userId);
      setViewMode('tree');
    } catch (error) {
      console.error('Error loading user tree:', error);
      toast.error('Failed to load referral tree');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      console.log('Searching for:', searchQuery);
      const results = await referralTreeService.searchInNetwork(searchQuery);
      console.log('Search results:', results);
      setSearchResults(results);
      setViewMode('search');
    } catch (error) {
      console.error('Error searching network:', error);
      toast.error(`Failed to search network: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => `₹${amount.toFixed(2)}`;

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Network Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center space-x-3">
            <Network className="w-8 h-8 text-purple-600" />
            <div>
              <div className="text-sm text-gray-600">Total Networks</div>
              <div className="text-2xl font-bold">{networkOverview?.total_networks || 0}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center space-x-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-sm text-gray-600">Total Users</div>
              <div className="text-2xl font-bold">{networkOverview?.total_users || 0}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center space-x-3">
            <Crown className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-sm text-gray-600">Premium Users</div>
              <div className="text-2xl font-bold">{networkOverview?.total_premium_users || 0}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-orange-600" />
            <div>
              <div className="text-sm text-gray-600">Total Earnings</div>
              <div className="text-2xl font-bold">{formatCurrency(networkOverview?.total_earnings || 0)}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center space-x-3">
            <Users className="w-8 h-8 text-indigo-600" />
            <div>
              <div className="text-sm text-gray-600">Avg Network Size</div>
              <div className="text-2xl font-bold">{Math.round(networkOverview?.average_network_size || 0)}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center space-x-3">
            <Network className="w-8 h-8 text-red-600" />
            <div>
              <div className="text-sm text-gray-600">Largest Network</div>
              <div className="text-2xl font-bold">{networkOverview?.largest_network || 0}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">How to View Referral Trees</h3>
        <div className="text-blue-800 space-y-2">
          <p>• Use the search bar above to find specific users by email, name, or referral code</p>
          <p>• Click on any user from search results to view their complete referral tree</p>
          <p>• The tree shows all levels of referrals with earnings, status, and network size</p>
          <p>• Premium users are highlighted in green, standard users in blue</p>
        </div>
      </div>
    </div>
  );

  const renderSearchResults = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Search Results ({searchResults.length})</h3>
      
      {searchResults.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No users found matching your search criteria.
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {searchResults.map((user) => (
            <div 
              key={user.id}
              className="bg-white p-4 rounded-lg border hover:shadow-md cursor-pointer transition-all"
              onClick={() => loadUserTree(user.id)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  {user.is_premium ? (
                    <Crown className="w-5 h-5 text-green-600" />
                  ) : (
                    <Users className="w-5 h-5 text-blue-600" />
                  )}
                  <span className="font-semibold">{user.full_name}</span>
                </div>
                <button className="p-1 hover:bg-gray-100 rounded">
                  <Eye className="w-4 h-4" />
                </button>
              </div>
              
              <div className="text-sm text-gray-600 space-y-1">
                <div>{user.email}</div>
                <div className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                  {user.referral_code}
                </div>
              </div>

              <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">Direct Referrals:</span>
                  <span className="font-semibold ml-1">{user.direct_referrals}</span>
                </div>
                <div>
                  <span className="text-gray-500">Network Size:</span>
                  <span className="font-semibold ml-1">{user.total_network_size + 1}</span>
                </div>
                <div>
                  <span className="text-gray-500">Earnings:</span>
                  <span className="font-semibold ml-1">{formatCurrency(user.total_earnings)}</span>
                </div>
                <div>
                  <span className="text-gray-500">Wallet:</span>
                  <span className="font-semibold ml-1">{formatCurrency(user.wallet_balance)}</span>
                </div>
              </div>

              {user.ewallet_unlocked && (
                <div className="mt-2 text-xs text-green-600 font-semibold">
                  🔓 E-wallet Unlocked
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  if (loading && !currentTree && !networkOverview) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Referral Tree Visualization</h1>
          <p className="text-gray-600">View and analyze the complete referral network hierarchy</p>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={loadNetworkOverview}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search by email, name, or referral code..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <button
            onClick={handleSearch}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Search
          </button>
          <button
            onClick={() => setViewMode('overview')}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Overview
          </button>
        </div>
      </div>

      {/* Content */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {!loading && viewMode === 'overview' && renderOverview()}
      {!loading && viewMode === 'search' && renderSearchResults()}
      {!loading && viewMode === 'tree' && currentTree && currentStats && (
        <div>
          <div className="mb-4">
            <button
              onClick={() => setViewMode('overview')}
              className="text-blue-600 hover:text-blue-800"
            >
              ← Back to Overview
            </button>
          </div>
          <ReferralTreeVisualization 
            tree={currentTree} 
            stats={currentStats}
            onUserSelect={loadUserTree}
          />
        </div>
      )}
    </div>
  );
};

export default AdminReferralTreePage;
