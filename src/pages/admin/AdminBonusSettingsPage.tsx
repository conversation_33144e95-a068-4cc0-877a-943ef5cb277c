import React from 'react';
import { DollarSign, Info } from 'lucide-react';
import BonusSettings from '../../components/admin/BonusSettings';

export default function AdminBonusSettingsPage() {
  return (
    <div>
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-green-100 rounded-lg">
            <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Bonus Settings</h1>
            <p className="text-gray-600 text-sm sm:text-base">Configure referral and wallet bonus amounts</p>
          </div>
        </div>

        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
          <div className="flex items-start gap-3">
            <Info className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-900 mb-1 text-sm sm:text-base">How Bonus Settings Work</h3>
              <div className="text-xs sm:text-sm text-blue-800 space-y-1">
                <p>• <strong>Standard Referral Bonus:</strong> Amount given when someone joins using a referral code</p>
                <p>• <strong>Premium Referral Bonus:</strong> Additional amount when referred user upgrades to premium</p>
                <p>• <strong>E-wallet Unlock Bonus:</strong> One-time bonus when user makes 3 premium referrals</p>
                <p>• <strong>No Welcome Bonus:</strong> New users start with ₹0.00 - earnings only through referrals</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bonus Settings Component */}
      <BonusSettings />

      {/* Additional Information */}
      <div className="mt-6 sm:mt-8 grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6">
          <h3 className="font-semibold text-green-900 mb-3 text-sm sm:text-base">✅ Current System Features</h3>
          <ul className="space-y-2 text-green-800 text-xs sm:text-sm">
            <li>• No welcome bonus for new users</li>
            <li>• Referral-only earning system</li>
            <li>• Admin-configurable bonus amounts</li>
            <li>• Real-time updates to new transactions</li>
            <li>• 3rd referral special rule active</li>
            <li>• E-wallet unlock after 3 premium referrals</li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 sm:p-6">
          <h3 className="font-semibold text-yellow-900 mb-3 text-sm sm:text-base">⚠️ Important Notes</h3>
          <ul className="space-y-2 text-yellow-800 text-xs sm:text-sm">
            <li>• Changes apply immediately to new transactions</li>
            <li>• Existing wallet balances remain unchanged</li>
            <li>• Premium referral bonuses are additional to standard bonuses</li>
            <li>• E-wallet unlock is a one-time bonus per user</li>
            <li>• All amounts are in Indian Rupees (₹)</li>
            <li>• Minimum value is ₹0.00 for all bonuses</li>
          </ul>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 sm:mt-8 bg-gray-50 rounded-lg p-4 sm:p-6">
        <h3 className="font-semibold text-gray-900 mb-4 text-sm sm:text-base">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          <button
            onClick={() => window.location.href = '/admin/dashboard/users'}
            className="flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors touch-target"
          >
            <span className="text-lg sm:text-xl">👥</span>
            <span className="font-medium text-sm sm:text-base">Manage Users</span>
          </button>

          <button
            onClick={() => window.location.href = '/admin/dashboard/wallet'}
            className="flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors touch-target"
          >
            <span className="text-lg sm:text-xl">💰</span>
            <span className="font-medium text-sm sm:text-base">Wallet Management</span>
          </button>

          <button
            onClick={() => window.location.href = '/admin/dashboard'}
            className="flex items-center justify-center gap-2 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors touch-target sm:col-span-2 lg:col-span-1"
          >
            <span className="text-lg sm:text-xl">📊</span>
            <span className="font-medium text-sm sm:text-base">View Analytics</span>
          </button>
        </div>
      </div>
    </div>
  );
}
