import React, { useState, useEffect } from 'react';
import { Settings, Save, Gift, Percent, DollarSign } from 'lucide-react';
import { ReferralSettings } from '../../types';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';
import BonusSettings from '../../components/admin/BonusSettings';

export default function AdminSettingsPage() {
  const [referralSettings, setReferralSettings] = useState<ReferralSettings>({
    referrer_bonus: 100,
    referred_bonus: 50,
    max_referrals_per_user: 10
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('referral_settings')
        .select('*')
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setReferralSettings(data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const handleSaveReferralSettings = async () => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('referral_settings')
        .upsert(referralSettings);

      if (error) throw error;

      toast.success('Referral settings updated successfully');
    } catch (error) {
      console.error('Error saving referral settings:', error);
      toast.error('Failed to update referral settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">Manage application settings and configurations</p>
      </div>

      <div className="space-y-8">
        {/* Bonus Settings - New Admin-Configurable Bonuses */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-6">
            <DollarSign className="h-6 w-6 text-green-600" />
            <h2 className="text-xl font-semibold text-gray-900">Bonus Management</h2>
          </div>
          <BonusSettings />
        </div>

        {/* Referral Settings */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-6">
            <Gift className="h-6 w-6 text-green-600" />
            <h2 className="text-xl font-semibold text-gray-900">Referral Program Settings</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Referrer Bonus (₹)
              </label>
              <input
                type="number"
                value={referralSettings.referrer_bonus}
                onChange={(e) => setReferralSettings({
                  ...referralSettings,
                  referrer_bonus: Number(e.target.value)
                })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                min="0"
              />
              <p className="text-xs text-gray-500 mt-1">
                Amount credited to the referrer when someone uses their code
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Referred User Bonus (₹)
              </label>
              <input
                type="number"
                value={referralSettings.referred_bonus}
                onChange={(e) => setReferralSettings({
                  ...referralSettings,
                  referred_bonus: Number(e.target.value)
                })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                min="0"
              />
              <p className="text-xs text-gray-500 mt-1">
                Welcome bonus for new users who join via referral
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Referrals per User
              </label>
              <input
                type="number"
                value={referralSettings.max_referrals_per_user}
                onChange={(e) => setReferralSettings({
                  ...referralSettings,
                  max_referrals_per_user: Number(e.target.value)
                })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                min="1"
              />
              <p className="text-xs text-gray-500 mt-1">
                Maximum number of referrals allowed per user (0 for unlimited)
              </p>
            </div>
          </div>

          <div className="mt-6">
            <button
              onClick={handleSaveReferralSettings}
              disabled={loading}
              className="flex items-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              <Save className="h-4 w-4" />
              {loading ? 'Saving...' : 'Save Referral Settings'}
            </button>
          </div>
        </div>

        {/* Premium Settings */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-6">
            <Percent className="h-6 w-6 text-yellow-600" />
            <h2 className="text-xl font-semibold text-gray-900">Premium Settings</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Premium Subscription Price (₹/year)
              </label>
              <input
                type="number"
                defaultValue={999}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                min="0"
              />
              <p className="text-xs text-gray-500 mt-1">
                Annual subscription price for premium membership
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Premium Discount Percentage
              </label>
              <input
                type="number"
                defaultValue={15}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                min="0"
                max="100"
              />
              <p className="text-xs text-gray-500 mt-1">
                Discount percentage for premium members on all products
              </p>
            </div>
          </div>

          <div className="mt-6">
            <button
              className="flex items-center gap-2 bg-yellow-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-yellow-700 transition-colors"
            >
              <Save className="h-4 w-4" />
              Save Premium Settings
            </button>
          </div>
        </div>

        {/* System Settings */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-6">
            <Settings className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">System Settings</h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">Email Notifications</h3>
                <p className="text-sm text-gray-500">Send email notifications for orders and updates</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" defaultChecked className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">SMS Notifications</h3>
                <p className="text-sm text-gray-500">Send SMS notifications for order updates</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h3 className="font-medium text-gray-900">Maintenance Mode</h3>
                <p className="text-sm text-gray-500">Enable maintenance mode for system updates</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
              </label>
            </div>
          </div>

          <div className="mt-6">
            <button className="flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              <Save className="h-4 w-4" />
              Save System Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}