import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AddProductForm } from '../../components/admin/AddProductForm';
import { useProductStore } from '../../stores/productStore';
import { useAuthStore } from '../../stores/authStore';

export const AddProductPage: React.FC = () => {
  const navigate = useNavigate();
  const { fetchCategories } = useProductStore();
  // const { user } = useAuthStore();

  // Check if user is admin
  // const isAdmin = user?.email === '<EMAIL>';

  useEffect(() => {
    // if (!isAdmin) {
    //   navigate('/');
    //   return;
    // }
    fetchCategories();
  }, [fetchCategories, navigate]); // Removed isAdmin dependency

  const handleSuccess = () => {
    navigate('/admin/products');
  };

  const handleCancel = () => {
    navigate('/admin/products');
  };

  // if (!isAdmin) {
  //   return null;
  // }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
      <div className="mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Add New Product</h1>
        <p className="mt-1 text-sm text-gray-500">
          Fill out the form below to add a new product to the store.
        </p>
      </div>
      <AddProductForm onSuccess={handleSuccess} onCancel={handleCancel} />
    </div>
  );
};