import React, { useState, useEffect } from 'react';
import { Users, Search, Crown, Wallet, Edit, Trash2, Shield } from 'lucide-react';
import { User } from '../../types';
import { supabase } from '../../lib/supabase';
import { referralCodeGenerator } from '../../services/referralCodeGenerator';
import { AuthFixService } from '../../services/authFixService';
import toast from 'react-hot-toast';

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'premium' | 'regular'>('all');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePremium = async (userId: string, isPremium: boolean) => {
    try {
      const newPremiumStatus = !isPremium;

      // Update user premium status directly
      const { error: updateError } = await supabase
        .from('users')
        .update({
          is_premium: newPremiumStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) throw updateError;

      // The trigger will automatically handle referral processing for premium upgrades

      // If upgrading to premium, generate referral code
      if (newPremiumStatus) {
        const user = users.find(u => u.id === userId);
        if (user && user.email) {
          const result = await referralCodeGenerator.generateAndAssignForPremiumUser(
            userId,
            user.email
          );

          if (result.success) {
            toast.success(`Premium activated! Referral code: ${result.referralCode}`);
          } else {
            console.error('Failed to generate referral code:', result.error);
            toast.success('Premium activated! (Referral code will be generated shortly)');
          }
        } else {
          toast.success('Premium activated!');
        }
      } else {
        toast.success('Premium access removed');
      }

      // Refresh the users list to show updated data
      fetchUsers();
    } catch (error) {
      console.error('Error updating user premium status:', error);
      toast.error('Failed to update user status');
    }
  };

  const handleUpdateWallet = async (userId: string, amount: number) => {
    try {
      const { error } = await supabase.rpc('add_wallet_balance', {
        user_id: userId,
        amount: amount
      });

      if (error) throw error;

      // Add transaction record
      await supabase.from('wallet_transactions').insert({
        user_id: userId,
        type: amount > 0 ? 'credit' : 'debit',
        amount: Math.abs(amount),
        description: `Admin ${amount > 0 ? 'credit' : 'debit'}`,
        reference_type: 'admin'
      });

      setUsers(users.map(user => 
        user.id === userId 
          ? { ...user, wallet_balance: user.wallet_balance + amount }
          : user
      ));

      toast.success('Wallet updated successfully');
    } catch (error) {
      console.error('Error updating wallet:', error);
      toast.error('Failed to update wallet');
    }
  };

  const handleFixMissingPremiumBonuses = async () => {
    try {
      const { data: result, error } = await supabase.rpc('fix_missing_premium_bonuses');

      if (error) throw error;

      if (result?.success) {
        toast.success(`Fixed ${result.processed_bonuses} missing premium bonuses (₹${result.premium_bonus_amount} each)`);
        fetchUsers(); // Refresh the user list
      } else {
        toast.error(result?.error || 'Failed to fix premium bonuses');
      }
    } catch (error) {
      console.error('Error fixing premium bonuses:', error);
      toast.error('Failed to fix premium bonuses');
    }
  };

  const handleFixAuthUsers = async () => {
    try {
      toast.loading('Fixing auth users...', { id: 'auth-fix' });

      const result = await AuthFixService.fixMissingAuthUsers();

      if (result.success) {
        toast.success(result.message || 'Auth fix completed', { id: 'auth-fix' });
        if (result.results) {
          console.log('Auth fix results:', result.results);
          if (result.results.fixed > 0) {
            toast.success(`✅ Fixed ${result.results.fixed} users! They can now login with password: TempPass123!`);
          }
        }
        fetchUsers(); // Refresh the user list
      } else {
        toast.error(result.error || 'Failed to fix auth users', { id: 'auth-fix' });
      }
    } catch (error) {
      console.error('Error fixing auth users:', error);
      toast.error('Failed to fix auth users', { id: 'auth-fix' });
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filter === 'all' || 
                         (filter === 'premium' && user.is_premium) ||
                         (filter === 'regular' && !user.is_premium);

    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">User Management</h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          <button
            onClick={handleFixMissingPremiumBonuses}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
            title="Fix any missing premium referral bonuses"
          >
            Fix Premium Bonuses
          </button>
          <button
            onClick={handleFixAuthUsers}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center gap-2"
            title="Fix users who can't login (create missing auth accounts)"
          >
            <Shield className="h-4 w-4" />
            Fix Auth Issues
          </button>
          <div className="relative">
            <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full sm:w-auto pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="all">All Users</option>
            <option value="premium">Premium Users</option>
            <option value="regular">Regular Users</option>
          </select>
        </div>
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Wallet Balance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Joined
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {user.is_premium ? (
                      <span className="inline-flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                        <Crown className="h-3 w-3 fill-current" />
                        Premium
                      </span>
                    ) : (
                      <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs font-medium">
                        Regular
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Wallet className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-900">₹{user.wallet_balance}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleTogglePremium(user.id, user.is_premium)}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          user.is_premium
                            ? 'bg-red-100 text-red-700 hover:bg-red-200'
                            : 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                        }`}
                      >
                        {user.is_premium ? 'Remove Premium' : 'Make Premium'}
                      </button>
                      <button
                        onClick={() => {
                          const amount = prompt('Enter amount to add/subtract (use negative for deduction):');
                          if (amount) {
                            handleUpdateWallet(user.id, parseFloat(amount));
                          }
                        }}
                        className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
                        title="Update Wallet"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden space-y-4">
        {filteredUsers.map((user) => (
          <div key={user.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{user.full_name}</h3>
                  <p className="text-sm text-gray-500">{user.email}</p>
                </div>
              </div>
              {user.is_premium ? (
                <span className="inline-flex items-center gap-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                  <Crown className="h-3 w-3 fill-current" />
                  Premium
                </span>
              ) : (
                <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium">
                  Regular
                </span>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500 mb-1">Wallet Balance</p>
                <div className="flex items-center gap-1">
                  <Wallet className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-gray-900">₹{user.wallet_balance}</span>
                </div>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Joined</p>
                <p className="text-sm text-gray-900">{new Date(user.created_at).toLocaleDateString()}</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <button
                onClick={() => handleTogglePremium(user.id, user.is_premium)}
                className={`flex-1 px-3 py-2 rounded text-sm font-medium transition-colors ${
                  user.is_premium
                    ? 'bg-red-100 text-red-700 hover:bg-red-200'
                    : 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                }`}
              >
                {user.is_premium ? 'Remove Premium' : 'Make Premium'}
              </button>
              <button
                onClick={() => {
                  const amount = prompt('Enter amount to add/subtract (use negative for deduction):');
                  if (amount) {
                    handleUpdateWallet(user.id, parseFloat(amount));
                  }
                }}
                className="flex items-center justify-center gap-2 px-3 py-2 text-blue-600 hover:text-blue-800 border border-blue-200 hover:border-blue-300 rounded transition-colors"
                title="Update Wallet"
              >
                <Edit className="h-4 w-4" />
                <span className="text-sm">Edit Wallet</span>
              </button>
            </div>
          </div>
        ))}

        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}