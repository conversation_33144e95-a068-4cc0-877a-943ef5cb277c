import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  DollarSign, 
  Users, 
  ToggleLeft, 
  ToggleRight, 
  Save,
  RefreshCw,
  TrendingUp,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface ReferralLevelBonus {
  level: number;
  amount: number;
  description: string;
}

interface SystemSettings {
  multiLevelEnabled: boolean;
  maxLevels: number;
  ewalletThreshold: number;
  walletUnlockBonus: number;
  processingTimeout: number;
  maxConcurrentProcessing: number;
}

const AdminMultiLevelReferralPage: React.FC = () => {
  const [levelBonuses, setLevelBonuses] = useState<ReferralLevelBonus[]>([]);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    multiLevelEnabled: true,
    maxLevels: 10,
    ewalletThreshold: 3,
    walletUnlockBonus: 500,
    processingTimeout: 30,
    maxConcurrentProcessing: 5
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);

      // Load referral level bonuses
      const { data: bonusSettings, error: bonusError } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value, description')
        .like('setting_key', 'referral_level_%_bonus')
        .order('setting_key');

      if (bonusError) throw bonusError;

      const bonuses: ReferralLevelBonus[] = bonusSettings?.map(setting => {
        const match = setting.setting_key.match(/referral_level_(\d+)_bonus/);
        const level = match ? parseInt(match[1]) : 0;
        return {
          level,
          amount: parseFloat(setting.setting_value),
          description: setting.description
        };
      }).sort((a, b) => a.level - b.level) || [];

      setLevelBonuses(bonuses);

      // Load system settings
      const { data: systemSettingsData, error: systemError } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value')
        .in('setting_key', [
          'multi_level_referral_enabled',
          'max_referral_levels',
          'ewallet_unlock_threshold',
          'referral_processing_timeout',
          'max_concurrent_referral_processing'
        ]);

      if (systemError) throw systemError;

      const settingsMap = systemSettingsData?.reduce((acc, setting) => {
        acc[setting.setting_key] = setting.setting_value;
        return acc;
      }, {} as Record<string, string>) || {};

      setSystemSettings({
        multiLevelEnabled: settingsMap.multi_level_referral_enabled === 'true',
        maxLevels: parseInt(settingsMap.max_referral_levels || '10'),
        ewalletThreshold: parseInt(settingsMap.ewallet_unlock_threshold || '3'),
        walletUnlockBonus: 0,
        processingTimeout: parseInt(settingsMap.referral_processing_timeout || '30'),
        maxConcurrentProcessing: parseInt(settingsMap.max_concurrent_referral_processing || '5')
      });

    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const updateLevelBonus = (level: number, amount: number) => {
    setLevelBonuses(prev => 
      prev.map(bonus => 
        bonus.level === level 
          ? { ...bonus, amount } 
          : bonus
      )
    );
  };

  const saveSettings = async () => {
    try {
      setSaving(true);

      // Save level bonuses
      for (const bonus of levelBonuses) {
        const { error } = await supabase
          .from('admin_settings')
          .upsert({
            setting_key: `referral_level_${bonus.level}_bonus`,
            setting_value: bonus.amount.toString(),
            description: `Bonus amount for Level ${bonus.level} referrer`,
            category: 'referral_bonuses',
            updated_at: new Date().toISOString()
          });

        if (error) throw error;
      }

      // Save system settings
      const systemSettingsToSave = [
        { key: 'multi_level_referral_enabled', value: systemSettings.multiLevelEnabled.toString(), desc: 'Enable/disable the multi-level referral system' },
        { key: 'max_referral_levels', value: systemSettings.maxLevels.toString(), desc: 'Maximum number of referral levels to process' },
        { key: 'ewallet_unlock_threshold', value: systemSettings.ewalletThreshold.toString(), desc: 'Number of premium referrals required to unlock e-wallet' },
        { key: 'wallet_unlock_bonus_amount', value: systemSettings.walletUnlockBonus.toString(), desc: 'Bonus amount when e-wallet is unlocked' },
        { key: 'referral_processing_timeout', value: systemSettings.processingTimeout.toString(), desc: 'Timeout in seconds for referral processing' },
        { key: 'max_concurrent_referral_processing', value: systemSettings.maxConcurrentProcessing.toString(), desc: 'Maximum concurrent referral processing operations' }
      ];

      for (const setting of systemSettingsToSave) {
        const { error } = await supabase
          .from('admin_settings')
          .upsert({
            setting_key: setting.key,
            setting_value: setting.value,
            description: setting.desc,
            category: setting.key.includes('wallet') ? 'wallet_system' : 'referral_system',
            updated_at: new Date().toISOString()
          });

        if (error) throw error;
      }

      toast.success('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const totalPossibleDistribution = levelBonuses.reduce((sum, bonus) => sum + bonus.amount, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <TrendingUp className="h-6 w-6 text-blue-600" />
            Multi-Level Referral System
          </h1>
          <p className="text-gray-600 mt-1">
            Configure the 10-level referral bonus system and settings
          </p>
        </div>
        <button
          onClick={saveSettings}
          disabled={saving}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50"
        >
          {saving ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          Save Settings
        </button>
      </div>

      {/* System Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="h-5 w-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">System Status</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm font-medium text-gray-700">System Status</span>
            <div className="flex items-center gap-2">
              {systemSettings.multiLevelEnabled ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">Enabled</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-600">Disabled</span>
                </>
              )}
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm font-medium text-gray-700">Max Levels</span>
            <span className="text-sm font-bold text-gray-900">{systemSettings.maxLevels}</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm font-medium text-gray-700">Total Distribution</span>
            <span className="text-sm font-bold text-green-600">₹{totalPossibleDistribution.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* System Settings */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center gap-3 mb-6">
          <Settings className="h-5 w-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">System Settings</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Enable/Disable System */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Multi-Level Referral System</label>
            <button
              onClick={() => setSystemSettings(prev => ({ ...prev, multiLevelEnabled: !prev.multiLevelEnabled }))}
              className="flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 transition-colors"
            >
              {systemSettings.multiLevelEnabled ? (
                <ToggleRight className="h-6 w-6 text-green-600" />
              ) : (
                <ToggleLeft className="h-6 w-6 text-gray-400" />
              )}
              <span className={`text-sm font-medium ${systemSettings.multiLevelEnabled ? 'text-green-600' : 'text-gray-500'}`}>
                {systemSettings.multiLevelEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </button>
          </div>

          {/* Max Levels */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Maximum Referral Levels</label>
            <input
              type="number"
              min="1"
              max="20"
              value={systemSettings.maxLevels}
              onChange={(e) => setSystemSettings(prev => ({ ...prev, maxLevels: parseInt(e.target.value) || 10 }))}
              className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* E-wallet Threshold */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">E-wallet Unlock Threshold</label>
            <input
              type="number"
              min="1"
              value={systemSettings.ewalletThreshold}
              onChange={(e) => setSystemSettings(prev => ({ ...prev, ewalletThreshold: parseInt(e.target.value) || 3 }))}
              className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500">Number of premium referrals required to unlock e-wallet</p>
          </div>

          {/* Wallet Unlock Bonus */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Wallet Unlock Bonus (₹)</label>
            <input
              type="number"
              min="0"
              step="0.01"
              value={systemSettings.walletUnlockBonus}
              onChange={(e) => setSystemSettings(prev => ({ ...prev, walletUnlockBonus: parseFloat(e.target.value) || 500 }))}
              className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Level Bonuses */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center gap-3 mb-6">
          <DollarSign className="h-5 w-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Referral Level Bonuses</h2>
          <span className="text-sm text-gray-500">
            Total: ₹{totalPossibleDistribution.toFixed(2)}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {levelBonuses.map((bonus) => (
            <div key={bonus.level} className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Level {bonus.level}
                {bonus.level === 1 && <span className="text-blue-600"> (Direct)</span>}
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={bonus.amount}
                  onChange={(e) => updateLevelBonus(bonus.level, parseFloat(e.target.value) || 0)}
                  className="w-full pl-8 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="text-xs text-gray-500">
                {((bonus.amount / totalPossibleDistribution) * 100).toFixed(1)}% of total
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-blue-900">Important Notes</h3>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Bonuses are only distributed when referred users become premium</li>
                <li>• All users in the referral chain must be premium to receive bonuses</li>
                <li>• Level 1 is the direct referrer, Level 2 is their referrer, and so on</li>
                <li>• Changes take effect immediately for new referrals</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminMultiLevelReferralPage;
