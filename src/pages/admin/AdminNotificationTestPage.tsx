import React, { useState } from 'react';
import { Bell, Plus, TestTube, Zap, AlertTriangle } from 'lucide-react';
import { useAdminNotifications } from '../../hooks/useAdminNotifications';
import { AdminNotification } from '../../services/adminNotificationService';

const AdminNotificationTestPage: React.FC = () => {
  const { createNotification, notifications, unreadCount } = useAdminNotifications();
  const [selectedType, setSelectedType] = useState<AdminNotification['type']>('order');
  const [selectedPriority, setSelectedPriority] = useState<AdminNotification['priority']>('medium');
  const [customTitle, setCustomTitle] = useState('');
  const [customMessage, setCustomMessage] = useState('');

  const notificationTypes: { value: AdminNotification['type']; label: string; description: string }[] = [
    { value: 'order', label: 'Order', description: 'New order notifications' },
    { value: 'user', label: 'User', description: 'User registration notifications' },
    { value: 'kyc', label: 'KYC', description: 'KYC submission notifications' },
    { value: 'wallet', label: 'Wallet', description: 'Wallet transaction notifications' },
    { value: 'product', label: 'Product', description: 'Product-related notifications' },
    { value: 'referral', label: 'Referral', description: 'Referral system notifications' },
    { value: 'alert', label: 'Alert', description: 'System alerts and warnings' }
  ];

  const priorityLevels: { value: AdminNotification['priority']; label: string; color: string }[] = [
    { value: 'low', label: 'Low', color: 'text-gray-600' },
    { value: 'medium', label: 'Medium', color: 'text-blue-600' },
    { value: 'high', label: 'High', color: 'text-orange-600' },
    { value: 'urgent', label: 'Urgent', color: 'text-red-600' }
  ];

  const sampleNotifications = {
    order: [
      { title: 'New Order Received', message: 'Order #1234 for ₹2,500 from <EMAIL>' },
      { title: 'Large Order Alert', message: 'Order #1235 for ₹15,000 from premium customer' },
      { title: 'Order Cancelled', message: 'Order #1236 was cancelled by customer' }
    ],
    user: [
      { title: 'New User Registration', message: 'Sarah Smith just registered' },
      { title: 'Premium Upgrade', message: 'John Doe upgraded to premium account' },
      { title: 'User Verification', message: 'Mike Johnson completed email verification' }
    ],
    kyc: [
      { title: 'KYC Submission', message: 'Emma Wilson submitted KYC documents for review' },
      { title: 'KYC Approved', message: 'David Brown\'s KYC has been approved' },
      { title: 'KYC Rejected', message: 'Lisa Davis\'s KYC was rejected - missing documents' }
    ],
    wallet: [
      { title: 'Large Transaction', message: '₹10,000 credit transaction processed' },
      { title: 'Wallet Unlock', message: 'User wallet unlocked after 3 referrals' },
      { title: 'Low Balance Alert', message: 'System wallet balance is running low' }
    ],
    product: [
      { title: 'New Product Added', message: 'Green Tea Extract added to inventory' },
      { title: 'Product Updated', message: 'Turmeric Capsules price updated' },
      { title: 'Product Discontinued', message: 'Old formula vitamins discontinued' }
    ],
    referral: [
      { title: 'New Referral', message: 'Alex Johnson referred by premium user' },
      { title: 'Referral Milestone', message: 'User reached 10 successful referrals' },
      { title: 'Referral Bonus Paid', message: '₹500 referral bonus distributed' }
    ],
    alert: [
      { title: 'Low Stock Alert', message: 'Vitamin D3 is running low (2 left)' },
      { title: 'System Maintenance', message: 'Scheduled maintenance in 1 hour' },
      { title: 'Security Alert', message: 'Multiple failed login attempts detected' }
    ]
  };

  const handleCreateCustomNotification = () => {
    if (!customTitle.trim() || !customMessage.trim()) {
      alert('Please fill in both title and message');
      return;
    }

    createNotification({
      type: selectedType,
      title: customTitle,
      message: customMessage,
      priority: selectedPriority,
      data: { custom: true, timestamp: new Date().toISOString() }
    });

    setCustomTitle('');
    setCustomMessage('');
  };

  const handleCreateSampleNotification = (sample: { title: string; message: string }) => {
    createNotification({
      type: selectedType,
      title: sample.title,
      message: sample.message,
      priority: selectedPriority,
      data: { sample: true, timestamp: new Date().toISOString() }
    });
  };

  const createBulkTestNotifications = () => {
    const bulkNotifications = [
      { type: 'order' as const, title: 'Bulk Test Order', message: 'Test order notification 1', priority: 'medium' as const },
      { type: 'user' as const, title: 'Bulk Test User', message: 'Test user notification 2', priority: 'low' as const },
      { type: 'kyc' as const, title: 'Bulk Test KYC', message: 'Test KYC notification 3', priority: 'high' as const },
      { type: 'alert' as const, title: 'Bulk Test Alert', message: 'Test alert notification 4', priority: 'urgent' as const },
      { type: 'wallet' as const, title: 'Bulk Test Wallet', message: 'Test wallet notification 5', priority: 'medium' as const }
    ];

    bulkNotifications.forEach((notification, index) => {
      setTimeout(() => {
        createNotification(notification);
      }, index * 500); // Stagger notifications by 500ms
    });
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          <TestTube className="h-8 w-8 text-green-600" />
          Notification Testing Center
        </h1>
        <p className="text-gray-600 mt-2">
          Test and preview admin notifications. Current notifications: {notifications.length} (Unread: {unreadCount})
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Custom Notification Creator */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create Custom Notification
          </h2>

          <div className="space-y-4">
            {/* Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value as AdminNotification['type'])}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {notificationTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label} - {type.description}
                  </option>
                ))}
              </select>
            </div>

            {/* Priority Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value as AdminNotification['priority'])}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {priorityLevels.map((priority) => (
                  <option key={priority.value} value={priority.value}>
                    {priority.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Title Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
              <input
                type="text"
                value={customTitle}
                onChange={(e) => setCustomTitle(e.target.value)}
                placeholder="Enter notification title"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Message Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
              <textarea
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                placeholder="Enter notification message"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <button
              onClick={handleCreateCustomNotification}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              Create Notification
            </button>
          </div>
        </div>

        {/* Sample Notifications */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Sample Notifications
          </h2>

          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Quick samples for {notificationTypes.find(t => t.value === selectedType)?.label} notifications:
            </p>

            <div className="space-y-2">
              {sampleNotifications[selectedType]?.map((sample, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-3">
                  <h4 className="font-medium text-gray-900 text-sm">{sample.title}</h4>
                  <p className="text-gray-600 text-sm">{sample.message}</p>
                  <button
                    onClick={() => handleCreateSampleNotification(sample)}
                    className="mt-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 transition-colors"
                  >
                    Create This
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Bulk Testing
        </h2>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={createBulkTestNotifications}
            className="bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            Create 5 Test Notifications
          </button>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <AlertTriangle className="h-4 w-4" />
            This will create multiple notifications with different types and priorities
          </div>
        </div>
      </div>

      {/* Current Stats */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{notifications.length}</p>
            <p className="text-sm text-gray-600">Total Notifications</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">{unreadCount}</p>
            <p className="text-sm text-gray-600">Unread</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{notifications.length - unreadCount}</p>
            <p className="text-sm text-gray-600">Read</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {notifications.filter(n => n.priority === 'urgent').length}
            </p>
            <p className="text-sm text-gray-600">Urgent</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminNotificationTestPage;
