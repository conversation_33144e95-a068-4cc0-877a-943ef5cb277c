import React, { useState, useEffect } from 'react';
import { 
  Shield, Search, Eye, Check, X, Download, Filter,
  Calendar, User, FileText, AlertCircle, CheckCircle,
  Clock, RefreshCw
} from 'lucide-react';
import { adminSupabase } from '../../lib/adminSupabase';
import toast from 'react-hot-toast';

interface KYCApplication {
  id: string;
  full_name: string;
  email: string;
  mobile_number: string;
  kyc_status: 'pending' | 'under_review' | 'approved' | 'rejected';
  kyc_submitted_at: string;
  kyc_reviewed_at?: string;
  kyc_comments?: string;
  aadhaar_front_url?: string;
  aadhaar_back_url?: string;
  pan_card_url?: string;
  account_number_encrypted?: string;
  ifsc_code?: string;
  account_holder_name?: string;
  bank_name?: string;
}

const AdminKYCPage: React.FC = () => {
  const [applications, setApplications] = useState<KYCApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedApplication, setSelectedApplication] = useState<KYCApplication | null>(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<string>('');
  const [reviewComments, setReviewComments] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    under_review: 0,
    approved: 0,
    rejected: 0
  });

  useEffect(() => {
    fetchKYCApplications();
  }, []);

  const fetchKYCApplications = async () => {
    try {
      setLoading(true);
      const { data, error } = await adminSupabase
        .from('users')
        .select(`
          id,
          full_name,
          email,
          mobile_number,
          kyc_status,
          kyc_submitted_at,
          kyc_reviewed_at,
          kyc_comments,
          aadhaar_front_url,
          aadhaar_back_url,
          pan_card_url,
          account_number_encrypted,
          ifsc_code,
          account_holder_name,
          bank_name
        `)
        .not('kyc_submitted_at', 'is', null)
        .order('kyc_submitted_at', { ascending: false });

      if (error) throw error;
      const applications = data || [];
      setApplications(applications);

      // Calculate statistics
      const newStats = {
        total: applications.length,
        pending: applications.filter(app => app.kyc_status === 'pending').length,
        under_review: applications.filter(app => app.kyc_status === 'under_review').length,
        approved: applications.filter(app => app.kyc_status === 'approved').length,
        rejected: applications.filter(app => app.kyc_status === 'rejected').length
      };
      setStats(newStats);
    } catch (error) {
      console.error('Error fetching KYC applications:', error);
      toast.error('Failed to fetch KYC applications');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (userId: string, newStatus: 'approved' | 'rejected') => {
    if (!reviewComments.trim()) {
      toast.error('Please add review comments');
      return;
    }

    // Confirmation dialog
    const confirmMessage = newStatus === 'approved'
      ? 'Are you sure you want to approve this KYC application?'
      : 'Are you sure you want to reject this KYC application?';

    if (!window.confirm(confirmMessage)) {
      return;
    }

    setIsProcessing(true);
    try {
      console.log('Updating KYC status for user:', userId, 'to:', newStatus);

      // Update user KYC status using admin client
      const { error: updateError } = await adminSupabase
        .from('users')
        .update({
          kyc_status: newStatus,
          kyc_reviewed_at: new Date().toISOString(),
          kyc_comments: reviewComments
        })
        .eq('id', userId);

      if (updateError) {
        console.error('Error updating user KYC status:', updateError);
        throw updateError;
      }

      console.log('KYC status updated successfully');

      // If approved, create welcome bonus transaction
      if (newStatus === 'approved') {
        const { error: bonusError } = await adminSupabase
          .from('wallet_transactions')
          .insert({
            user_id: userId,
            type: 'credit',
            amount: 50.00,
            description: 'KYC Approval Bonus',
            reference_type: 'kyc_bonus',
            reference_id: `KYC_BONUS_${Date.now()}`
          });

        if (bonusError) {
          console.error('Error creating KYC bonus:', bonusError);
          // Don't fail the KYC approval if bonus creation fails
        }

        // Update wallet balance - first get current balance, then update
        const { data: currentUser, error: fetchError } = await adminSupabase
          .from('users')
          .select('wallet_balance')
          .eq('id', userId)
          .single();

        if (!fetchError && currentUser) {
          const newBalance = (currentUser.wallet_balance || 0) + 50;
          const { error: walletError } = await adminSupabase
            .from('users')
            .update({ wallet_balance: newBalance })
            .eq('id', userId);

          if (walletError) {
            console.error('Error updating wallet balance:', walletError);
          }
        }
      }

      const successMessage = newStatus === 'approved'
        ? 'KYC approved successfully! User has been notified and received a ₹50 bonus.'
        : 'KYC rejected successfully. User has been notified.';

      toast.success(successMessage);
      setSelectedApplication(null);
      setReviewComments('');
      fetchKYCApplications();
    } catch (error) {
      console.error('Error updating KYC status:', error);
      toast.error('Failed to update KYC status');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      under_review: { color: 'bg-blue-100 text-blue-800', icon: RefreshCw },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', icon: AlertCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const filteredApplications = applications.filter(app => {
    const matchesSearch = (app.full_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (app.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (app.mobile_number || '').includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || app.kyc_status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const openDocumentModal = (documentUrl: string) => {
    setSelectedDocument(documentUrl);
    setShowDocumentModal(true);
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 flex items-center">
            <Shield className="w-8 h-8 text-green-600 mr-3" />
            KYC Management
          </h1>
          <p className="text-gray-600 mt-1">Review and manage user verification applications</p>
        </div>
        <button
          onClick={fetchKYCApplications}
          className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors touch-target"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Applications</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <RefreshCw className="w-5 h-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Under Review</p>
              <p className="text-2xl font-bold text-blue-600">{stats.under_review}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-5 h-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Rejected</p>
              <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name, email, or mobile..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Applications Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 text-green-600 animate-spin" />
          </div>
        ) : filteredApplications.length === 0 ? (
          <div className="text-center py-12">
            <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No KYC Applications</h3>
            <p className="text-gray-500">No applications match your current filters</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Documents
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Submitted
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredApplications.map((application) => (
                  <tr key={application.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="w-5 h-5 text-gray-500" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {application.full_name || 'No name provided'}
                          </div>
                          <div className="text-sm text-gray-500">{application.email || 'No email'}</div>
                          <div className="text-sm text-gray-500">{application.mobile_number || 'No mobile'}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(application.kyc_status)}
                      {application.kyc_reviewed_at && (
                        <div className="text-xs text-gray-500 mt-1">
                          Reviewed: {new Date(application.kyc_reviewed_at).toLocaleDateString()}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-1">
                        {application.aadhaar_front_url && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Aadhaar F
                          </span>
                        )}
                        {application.aadhaar_back_url && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Aadhaar B
                          </span>
                        )}
                        {application.pan_card_url && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            PAN
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {[application.aadhaar_front_url, application.aadhaar_back_url, application.pan_card_url].filter(Boolean).length}/3 docs
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{new Date(application.kyc_submitted_at).toLocaleDateString()}</div>
                      <div className="text-xs text-gray-400">
                        {new Date(application.kyc_submitted_at).toLocaleTimeString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedApplication(application)}
                          className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                          title="Review Application"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        {application.kyc_status === 'pending' && (
                          <>
                            <button
                              onClick={() => {
                                setSelectedApplication(application);
                                setReviewComments('Quick approval - all documents verified');
                                setTimeout(() => handleStatusUpdate(application.id, 'approved'), 100);
                              }}
                              className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                              title="Quick Approve"
                            >
                              <Check className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {
                                setSelectedApplication(application);
                                setReviewComments('Quick rejection - documents need review');
                                setTimeout(() => handleStatusUpdate(application.id, 'rejected'), 100);
                              }}
                              className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                              title="Quick Reject"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Application Detail Modal */}
      {selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  KYC Application - {selectedApplication.full_name}
                </h2>
                <button
                  onClick={() => setSelectedApplication(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Personal Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <User className="w-5 h-5 mr-2" />
                    Personal Information
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Full Name</label>
                      <p className="text-gray-900">{selectedApplication.full_name || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Email</label>
                      <p className="text-gray-900">{selectedApplication.email || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mobile Number</label>
                      <p className="text-gray-900">{selectedApplication.mobile_number || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Current Status</label>
                      <div className="mt-1">{getStatusBadge(selectedApplication.kyc_status)}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Submitted At</label>
                      <p className="text-gray-900">
                        {new Date(selectedApplication.kyc_submitted_at).toLocaleString()}
                      </p>
                    </div>
                    {selectedApplication.kyc_reviewed_at && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Reviewed At</label>
                        <p className="text-gray-900">
                          {new Date(selectedApplication.kyc_reviewed_at).toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Banking Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    Banking Information
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Account Holder Name</label>
                      <p className="text-gray-900">{selectedApplication.account_holder_name || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Bank Name</label>
                      <p className="text-gray-900">{selectedApplication.bank_name || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">IFSC Code</label>
                      <p className="text-gray-900">{selectedApplication.ifsc_code || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Account Number</label>
                      <p className="text-gray-900 font-mono">
                        {selectedApplication.account_number_encrypted ? '****' + selectedApplication.account_number_encrypted.slice(-4) : 'Not provided'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Documents Section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Uploaded Documents
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Aadhaar Front */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Aadhaar Card (Front)</h4>
                    {selectedApplication.aadhaar_front_url ? (
                      <div className="space-y-2">
                        <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
                          <FileText className="w-8 h-8 text-gray-400" />
                        </div>
                        <button
                          onClick={() => openDocumentModal(selectedApplication.aadhaar_front_url!)}
                          className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 flex items-center justify-center"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View Document
                        </button>
                      </div>
                    ) : (
                      <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
                        <p className="text-gray-500 text-sm">Not uploaded</p>
                      </div>
                    )}
                  </div>

                  {/* Aadhaar Back */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Aadhaar Card (Back)</h4>
                    {selectedApplication.aadhaar_back_url ? (
                      <div className="space-y-2">
                        <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
                          <FileText className="w-8 h-8 text-gray-400" />
                        </div>
                        <button
                          onClick={() => openDocumentModal(selectedApplication.aadhaar_back_url!)}
                          className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 flex items-center justify-center"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View Document
                        </button>
                      </div>
                    ) : (
                      <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
                        <p className="text-gray-500 text-sm">Not uploaded</p>
                      </div>
                    )}
                  </div>

                  {/* PAN Card */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">PAN Card</h4>
                    {selectedApplication.pan_card_url ? (
                      <div className="space-y-2">
                        <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
                          <FileText className="w-8 h-8 text-gray-400" />
                        </div>
                        <button
                          onClick={() => openDocumentModal(selectedApplication.pan_card_url!)}
                          className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 flex items-center justify-center"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View Document
                        </button>
                      </div>
                    ) : (
                      <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
                        <p className="text-gray-500 text-sm">Not uploaded</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Previous Comments */}
              {selectedApplication.kyc_comments && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Previous Review Comments</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700">{selectedApplication.kyc_comments}</p>
                  </div>
                </div>
              )}

              {/* Review Section */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Application</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Review Comments *
                    </label>
                    <textarea
                      value={reviewComments}
                      onChange={(e) => setReviewComments(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      rows={4}
                      placeholder="Add your review comments (required for approval/rejection)..."
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    <button
                      onClick={() => handleStatusUpdate(selectedApplication.id, 'approved')}
                      disabled={isProcessing || !reviewComments.trim()}
                      className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                    >
                      {isProcessing ? (
                        <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                      ) : (
                        <Check className="w-4 h-4 mr-2" />
                      )}
                      Approve KYC
                    </button>
                    <button
                      onClick={() => handleStatusUpdate(selectedApplication.id, 'rejected')}
                      disabled={isProcessing || !reviewComments.trim()}
                      className="flex-1 bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                    >
                      {isProcessing ? (
                        <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                      ) : (
                        <X className="w-4 h-4 mr-2" />
                      )}
                      Reject KYC
                    </button>
                    <button
                      onClick={() => {
                        setSelectedApplication(null);
                        setReviewComments('');
                      }}
                      className="flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Document Viewer Modal */}
      {showDocumentModal && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Document Viewer</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => window.open(selectedDocument, '_blank')}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center"
                >
                  <Download className="w-4 h-4 mr-1" />
                  Download
                </button>
                <button
                  onClick={() => setShowDocumentModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>
            <div className="p-4 max-h-[calc(90vh-120px)] overflow-auto">
              {selectedDocument.toLowerCase().includes('.pdf') ? (
                <iframe
                  src={selectedDocument}
                  className="w-full h-96 border border-gray-300 rounded"
                  title="Document Viewer"
                />
              ) : (
                <img
                  src={selectedDocument}
                  alt="Document"
                  className="max-w-full h-auto mx-auto rounded"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'text-center py-8 text-gray-500';
                    errorDiv.innerHTML = `
                      <div class="flex flex-col items-center">
                        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p>Unable to display document</p>
                        <p class="text-sm">Click download to view the file</p>
                      </div>
                    `;
                    target.parentNode?.appendChild(errorDiv);
                  }}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminKYCPage;
