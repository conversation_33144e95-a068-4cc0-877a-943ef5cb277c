import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Users,
  Package,
  ShoppingCart,
  Wallet,
  Gift,
  ArrowUp,
  ArrowDown,
  DollarSign,
  Percent,
  BarChart2,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Download,
  Filter,
  Calendar,
  MinusCircle
} from 'lucide-react';
import { AdminStats } from '../../types';
import SimpleLineChart from '../../components/charts/SimpleLineChart';
import SimplePieChart from '../../components/charts/SimplePieChart';
import SimpleBarChart from '../../components/charts/SimpleBarChart';
import { chartDataService, RevenueDataPoint, UserDistributionData } from '../../services/chartDataService';

interface AdminStatsPageProps {
  stats: AdminStats;
}

export default function AdminStatsPage({ stats }: AdminStatsPageProps) {
  const [timeRange, setTimeRange] = useState('7d');
  const [chartType, setChartType] = useState('line');
  const [loading, setLoading] = useState(false);
  const [revenueData, setRevenueData] = useState<RevenueDataPoint[]>([]);
  const [userDistributionData, setUserDistributionData] = useState<UserDistributionData[]>([]);
  const [chartsLoading, setChartsLoading] = useState(true);

  const refreshStats = () => {
    setLoading(true);
    // Trigger parent component to refresh
    setTimeout(() => {
      setLoading(false);
      window.location.reload();
    }, 1000);
  };

  const loadChartData = async () => {
    try {
      setChartsLoading(true);
      const [revenue, userDist] = await Promise.all([
        chartDataService.getRevenueTrendData(),
        chartDataService.getUserDistributionData()
      ]);

      setRevenueData(revenue);
      setUserDistributionData(userDist);
    } catch (error) {
      console.error('Error loading chart data:', error);
    } finally {
      setChartsLoading(false);
    }
  };

  useEffect(() => {
    loadChartData();
  }, []);

  const statCards = [
    {
      title: 'Total Revenue',
      value: `₹${stats.total_revenue}`,
      icon: TrendingUp,
      color: 'green',
      details: []
    },
    {
      title: 'Total Users',
      value: stats.total_users,
      icon: Users,
      color: 'blue',
      details: []
    },
    {
      title: 'Premium Users',
      value: stats.premium_users,
      icon: Package,
      color: 'yellow',
      details: []
    },
    {
      title: 'Total Orders',
      value: stats.total_orders,
      icon: ShoppingCart,
      color: 'purple',
      details: []
    },
    {
      title: 'Wallet Transactions',
      value: `₹${stats.wallet_transactions_total}`,
      icon: Wallet,
      color: 'indigo',
      details: []
    },
    {
      title: 'Total Withdrawals',
      value: `₹${stats.total_withdrawals}`,
      icon: MinusCircle,
      color: 'red',
      details: []
    },
    {
      title: 'Referral Bonuses',
      value: `₹${stats.referral_bonuses_paid}`,
      icon: Gift,
      color: 'pink',
      details: []
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      green: 'bg-green-50 text-green-600 border-green-200',
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200',
      indigo: 'bg-indigo-50 text-indigo-600 border-indigo-200',
      red: 'bg-red-50 text-red-600 border-red-200',
      pink: 'bg-pink-50 text-pink-600 border-pink-200'
    };
    return colors[color as keyof typeof colors] || colors.green;
  };

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? (
      <ArrowUp className="h-4 w-4 text-green-500" />
    ) : (
      <ArrowDown className="h-4 w-4 text-red-500" />
    );
  };

  return (
    <div>
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Dashboard Overview</h1>
            <p className="text-gray-600">Welcome to the HerbalStore admin dashboard</p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={refreshStats}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Download className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh Stats'}
            </button>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="flex-1 sm:flex-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">This year</option>
              </select>
            </div>
            <button className="flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <Download className="h-5 w-5" />
              <span className="hidden sm:inline">Export Report</span>
              <span className="sm:hidden">Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center mb-3 sm:mb-4">
              <div className={`p-2 sm:p-3 rounded-lg ${getColorClasses(stat.color)}`}>
                <stat.icon className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
            </div>
            <div>
              <p className="text-xs sm:text-sm text-gray-500 mb-1">{stat.title}</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0 mb-4 sm:mb-6">
            <h2 className="text-base sm:text-lg font-semibold text-gray-900">Revenue Trends</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setChartType('line')}
                className={`p-2 rounded-lg ${chartType === 'line' ? 'bg-green-100 text-green-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <LineChart className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
              <button
                onClick={() => setChartType('bar')}
                className={`p-2 rounded-lg ${chartType === 'bar' ? 'bg-green-100 text-green-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <BarChart2 className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            </div>
          </div>
          <div className="h-48 sm:h-64">
            {chartsLoading ? (
              <div className="h-full bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading chart...</p>
                </div>
              </div>
            ) : chartType === 'line' ? (
              <SimpleLineChart
                data={revenueData}
                color="#10B981"
                height={200}
              />
            ) : (
              <SimpleBarChart
                data={revenueData}
                color="#10B981"
                height={200}
              />
            )}
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0 mb-4 sm:mb-6">
            <h2 className="text-base sm:text-lg font-semibold text-gray-900">User Distribution</h2>
            <button className="text-sm text-green-600 hover:text-green-700 self-start sm:self-auto">View Details</button>
          </div>
          <div className="h-48 sm:h-64">
            {chartsLoading ? (
              <div className="h-full bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading chart...</p>
                </div>
              </div>
            ) : (
              <SimplePieChart
                data={userDistributionData}
                size={200}
              />
            )}
          </div>
        </div>
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
        {/* Revenue Overview */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">Revenue Overview</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-green-600" />
                <span className="text-gray-600">Average Order Value</span>
              </div>
              <span className="font-medium text-gray-900">₹{(stats.total_revenue / stats.total_orders).toFixed(2)}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Percent className="h-5 w-5 text-blue-600" />
                <span className="text-gray-600">Premium User Ratio</span>
              </div>
              <span className="font-medium text-gray-900">
                {((stats.premium_users / stats.total_users) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Wallet className="h-5 w-5 text-purple-600" />
                <span className="text-gray-600">Average Wallet Balance</span>
              </div>
              <span className="font-medium text-gray-900">
                ₹{(stats.wallet_transactions_total / stats.total_users).toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors touch-target">
              <div className="flex items-center gap-3">
                <Package className="h-5 w-5 text-green-600" />
                <span className="font-medium text-sm sm:text-base">Add New Product</span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors touch-target">
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-sm sm:text-base">Manage Users</span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors touch-target">
              <div className="flex items-center gap-3">
                <ShoppingCart className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-sm sm:text-base">View Orders</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">System Status</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <div className="p-3 sm:p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="font-medium text-gray-900 text-sm sm:text-base">Database</span>
            </div>
            <p className="text-xs sm:text-sm text-gray-600">All systems operational</p>
          </div>
          <div className="p-3 sm:p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="font-medium text-gray-900 text-sm sm:text-base">Payment Gateway</span>
            </div>
            <p className="text-xs sm:text-sm text-gray-600">Processing transactions normally</p>
          </div>
          <div className="p-3 sm:p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="font-medium text-gray-900 text-sm sm:text-base">Email Service</span>
            </div>
            <p className="text-xs sm:text-sm text-gray-600">Delivering emails successfully</p>
          </div>
        </div>
      </div>
    </div>
  );
}