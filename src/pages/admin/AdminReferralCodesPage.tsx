import React, { useState, useEffect } from 'react';
import { Gift, Users, Code, RefreshCw, Download, Eye, Plus, Search } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { referralCodeGenerator } from '../../services/referralCodeGenerator';
import toast from 'react-hot-toast';

interface UserWithReferralCode {
  id: string;
  email: string;
  full_name: string;
  is_premium: boolean;
  referral_code: string | null;
  created_at: string;
  premium_purchased_at: string | null;
}

export default function AdminReferralCodesPage() {
  const [users, setUsers] = useState<UserWithReferralCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'premium' | 'no_code' | 'with_code'>('all');
  const [stats, setStats] = useState<any>(null);
  const [bulkGenerating, setBulkGenerating] = useState(false);

  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('users')
        .select('id, email, full_name, is_premium, referral_code, created_at, premium_purchased_at')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const stats = await referralCodeGenerator.getReferralCodeStats();
      setStats(stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const generateCodeForUser = async (userId: string, userEmail: string) => {
    try {
      const result = await referralCodeGenerator.generateAndAssignForPremiumUser(userId, userEmail);
      
      if (result.success) {
        toast.success(`Referral code generated: ${result.referralCode}`);
        await fetchUsers();
        await fetchStats();
      } else {
        toast.error(result.error || 'Failed to generate referral code');
      }
    } catch (error) {
      console.error('Error generating code:', error);
      toast.error('Failed to generate referral code');
    }
  };

  const bulkGenerateForPremiumUsers = async () => {
    try {
      setBulkGenerating(true);
      const result = await referralCodeGenerator.generateCodesForExistingPremiumUsers();
      
      toast.success(`Processed ${result.processed} users. Generated ${result.successful} codes.`);
      
      if (result.errors.length > 0) {
        console.error('Bulk generation errors:', result.errors);
        toast.error(`${result.errors.length} errors occurred. Check console for details.`);
      }
      
      await fetchUsers();
      await fetchStats();
    } catch (error) {
      console.error('Error in bulk generation:', error);
      toast.error('Failed to bulk generate codes');
    } finally {
      setBulkGenerating(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.referral_code && user.referral_code.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesFilter = (() => {
      switch (filter) {
        case 'premium': return user.is_premium;
        case 'no_code': return user.is_premium && !user.referral_code;
        case 'with_code': return user.referral_code !== null;
        default: return true;
      }
    })();

    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Gift className="h-6 w-6 text-green-600" />
            Referral Code Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage and generate referral codes for premium users
          </p>
        </div>
        <button
          onClick={bulkGenerateForPremiumUsers}
          disabled={bulkGenerating}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 disabled:opacity-50"
        >
          {bulkGenerating ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Plus className="h-4 w-4" />
          )}
          Bulk Generate for Premium Users
        </button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2">
              <Code className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-600">Total Codes</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{stats.total_codes}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-gray-600">Premium Codes</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{stats.premium_user_codes}</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2">
              <Gift className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-gray-600">Coverage</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">{stats.code_coverage}%</p>
          </div>
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-600">Missing Codes</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-1">
              {filteredUsers.filter(u => u.is_premium && !u.referral_code).length}
            </p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search by email, name, or referral code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="all">All Users</option>
            <option value="premium">Premium Users</option>
            <option value="no_code">Premium Without Code</option>
            <option value="with_code">Users With Code</option>
          </select>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Referral Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Premium Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.is_premium
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.is_premium ? 'Premium' : 'Regular'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {user.referral_code ? (
                      <div className="flex items-center gap-2">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                          {user.referral_code}
                        </code>
                        <button
                          onClick={() => navigator.clipboard.writeText(user.referral_code!)}
                          className="text-gray-400 hover:text-gray-600"
                          title="Copy code"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">No code</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.premium_purchased_at 
                      ? new Date(user.premium_purchased_at).toLocaleDateString()
                      : '-'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {user.is_premium && !user.referral_code && (
                      <button
                        onClick={() => generateCodeForUser(user.id, user.email)}
                        className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors"
                      >
                        Generate Code
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8">
            <Gift className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
