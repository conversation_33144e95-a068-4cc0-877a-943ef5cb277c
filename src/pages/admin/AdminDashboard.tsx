import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation, Navigate, useNavigate } from 'react-router-dom';
import { useAdminNotifications } from '../../hooks/useAdminNotifications';
import {
  BarChart3,
  Users,
  Wallet,
  Settings,
  LogOut,
  TrendingUp,
  Bell,
  Search,
  ChevronDown,
  Calendar,
  AlertCircle,
  Activity,
  Clock,
  Filter,
  Download,
  RefreshCw,
  Menu,
  X,
  DollarSign,
  Network
} from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { AdminStats } from '../../types';
import { supabase } from '../../lib/supabase';
import AdminStatsPage from './AdminStatsPage';
import AdminUsersPage from './AdminUsersPage';
import AdminWalletPage from './AdminWalletPage';
import AdminSettingsPage from './AdminSettingsPage';
import AdminMLMSettingsPage from './AdminMLMSettingsPage';
import AdminReferralTreePage from './AdminReferralTreePage';

export default function AdminDashboard() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, signOut } = useAuthStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [stats, setStats] = useState<AdminStats>({
    total_revenue: 0,
    total_users: 0,
    premium_users: 0,
    total_orders: 0,
    wallet_transactions_total: 0,
    total_withdrawals: 0,
    referral_bonuses_paid: 0
  });

  // Use real notification system
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    createNotification
  } = useAdminNotifications();

  const [showNotifications, setShowNotifications] = useState(false);

  // Admin logout function that redirects to admin login
  const handleAdminLogout = async () => {
    try {
      await signOut();
      navigate('/admin');
    } catch (error) {
      console.error('Logout error:', error);
      navigate('/admin');
    }
  };

  // Admin access is now protected by AdminGuard component

  useEffect(() => {
    // if (isAdmin) {
      fetchStats();
    // }
  }, []); // Removed isAdmin dependency to prevent re-fetching on user change

  const fetchStats = async () => {
    try {
      console.log('Fetching admin dashboard stats...');

      // Use the efficient database function
      const { data, error } = await supabase.rpc('get_admin_dashboard_stats');

      if (error) {
        console.error('Database function error:', error);
        throw error;
      }

      if (data) {
        console.log('Admin stats loaded:', data);
        setStats({
          total_revenue: data.total_revenue || 0,
          total_users: data.total_users || 0,
          premium_users: data.premium_users || 0,
          total_orders: data.total_orders || 0,
          wallet_transactions_total: data.wallet_transactions_total || 0,
          total_withdrawals: data.total_withdrawals || 0,
          referral_bonuses_paid: data.referral_bonuses_paid || 0
        });
      } else {
        console.warn('No data returned from admin stats function');
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error);
      // Set default values on error
      setStats({
        total_revenue: 0,
        total_users: 0,
        premium_users: 0,
        total_orders: 0,
        wallet_transactions_total: 0,
        total_withdrawals: 0,
        referral_bonuses_paid: 0
      });
    }
  };

  // Helper functions for notifications
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return { border: 'border-red-500', bg: 'bg-red-50' };
      case 'high': return { border: 'border-orange-500', bg: 'bg-orange-50' };
      case 'medium': return { border: 'border-blue-500', bg: 'bg-blue-50' };
      case 'low': return { border: 'border-gray-500', bg: 'bg-gray-50' };
      default: return { border: 'border-gray-300', bg: 'bg-gray-50' };
    }
  };

  const getNotificationIconBg = (type: string) => {
    switch (type) {
      case 'order': return 'bg-green-100';
      case 'user': return 'bg-blue-100';
      case 'kyc': return 'bg-purple-100';
      case 'wallet': return 'bg-yellow-100';
      case 'product': return 'bg-indigo-100';
      case 'referral': return 'bg-pink-100';
      case 'alert': return 'bg-red-100';
      default: return 'bg-gray-100';
    }
  };

  const getNotificationIcon = (type: string) => {
    const iconClass = "h-4 w-4";
    switch (type) {
      case 'order': return <ShoppingCart className={`${iconClass} text-green-600`} />;
      case 'user': return <Users className={`${iconClass} text-blue-600`} />;
      case 'kyc': return <Shield className={`${iconClass} text-purple-600`} />;
      case 'wallet': return <Wallet className={`${iconClass} text-yellow-600`} />;
      case 'product': return <Package className={`${iconClass} text-indigo-600`} />;
      case 'referral': return <Gift className={`${iconClass} text-pink-600`} />;
      case 'alert': return <AlertCircle className={`${iconClass} text-red-600`} />;
      default: return <Bell className={`${iconClass} text-gray-600`} />;
    }
  };

  // Admin access validation is handled by AdminGuard wrapper

  const navigation = [
    { name: 'Dashboard', href: '/admin/dashboard', icon: BarChart3, current: location.pathname === '/admin/dashboard' },
    { name: 'Users', href: '/admin/dashboard/users', icon: Users, current: location.pathname === '/admin/dashboard/users' },
    { name: 'Referral Tree', href: '/admin/dashboard/referral-tree', icon: Network, current: location.pathname === '/admin/dashboard/referral-tree' },
    { name: 'Wallet', href: '/admin/dashboard/wallet', icon: Wallet, current: location.pathname === '/admin/dashboard/wallet' },
    { name: 'MLM Settings', href: '/admin/dashboard/mlm-settings', icon: DollarSign, current: location.pathname === '/admin/dashboard/mlm-settings' },
    { name: 'System Settings', href: '/admin/dashboard/settings', icon: Settings, current: location.pathname === '/admin/dashboard/settings' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-30">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              {/* Mobile menu button */}
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                {sidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg overflow-hidden bg-white shadow-sm">
                  <img
                    src="/logo.png"
                    alt="Start Juicce Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
                <span className="text-lg font-semibold text-gray-900 hidden sm:block">Admin Panel</span>
                <span className="text-lg font-semibold text-gray-900 sm:hidden">Admin</span>
              </div>
            </div>

            <div className="flex items-center gap-2 sm:gap-4">
              {/* Search - Hidden on mobile */}
              <div className="relative hidden md:block">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-48 lg:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>

              {/* Notifications */}
              <div className="relative">
                <button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="relative p-2 text-gray-600 hover:text-green-600 transition-colors"
                >
                  <Bell className="h-5 w-5 sm:h-6 sm:w-6" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </span>
                  )}
                </button>

                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 sm:w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                    {/* Header */}
                    <div className="px-4 py-3 border-b border-gray-100 flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">
                        Notifications {unreadCount > 0 && <span className="text-red-500">({unreadCount})</span>}
                      </h3>
                      <div className="flex items-center gap-2">
                        {unreadCount > 0 && (
                          <button
                            onClick={markAllAsRead}
                            className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                          >
                            Mark all read
                          </button>
                        )}
                        {notifications.length > 0 && (
                          <button
                            onClick={clearAllNotifications}
                            className="text-xs text-red-600 hover:text-red-700 font-medium"
                          >
                            Clear all
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Notifications List */}
                    <div className="max-h-96 overflow-y-auto">
                      {notifications.length === 0 ? (
                        <div className="px-4 py-8 text-center text-gray-500">
                          <Bell className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                          <p className="text-sm">No notifications</p>
                        </div>
                      ) : (
                        notifications.map((notification) => {
                          const timeAgo = getTimeAgo(notification.created_at);
                          const priorityColor = getPriorityColor(notification.priority);

                          return (
                            <div
                              key={notification.id}
                              className={`px-4 py-3 hover:bg-gray-50 transition-colors border-l-4 ${
                                notification.read ? 'bg-gray-50' : 'bg-white'
                              } ${priorityColor.border}`}
                            >
                              <div className="flex items-start gap-3">
                                <div className={`p-2 rounded-full ${getNotificationIconBg(notification.type)}`}>
                                  {getNotificationIcon(notification.type)}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                      <p className={`text-sm font-medium ${notification.read ? 'text-gray-600' : 'text-gray-900'}`}>
                                        {notification.title}
                                      </p>
                                      <p className={`text-sm ${notification.read ? 'text-gray-500' : 'text-gray-700'}`}>
                                        {notification.message}
                                      </p>
                                      <p className="text-xs text-gray-400 mt-1">{timeAgo}</p>
                                    </div>
                                    <div className="flex items-center gap-1 ml-2">
                                      {!notification.read && (
                                        <button
                                          onClick={() => markAsRead(notification.id)}
                                          className="p-1 text-blue-600 hover:text-blue-700"
                                          title="Mark as read"
                                        >
                                          <CheckCircle className="h-4 w-4" />
                                        </button>
                                      )}
                                      <button
                                        onClick={() => clearNotification(notification.id)}
                                        className="p-1 text-red-600 hover:text-red-700"
                                        title="Remove notification"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })
                      )}
                    </div>

                    {/* Footer */}
                    {notifications.length > 0 && (
                      <div className="px-4 py-2 border-t border-gray-100 text-center">
                        <button
                          onClick={() => {
                            // Add test notification for demo
                            createNotification({
                              type: 'order',
                              title: 'Test Notification',
                              message: 'This is a test notification to demonstrate the system',
                              priority: 'medium'
                            });
                          }}
                          className="text-xs text-green-600 hover:text-green-700 font-medium"
                        >
                          Add Test Notification
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* User Menu */}
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-green-600">
                    {user?.email?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="hidden md:block">
                  <p className="text-sm font-medium text-gray-900">{user?.email}</p>
                  <p className="text-xs text-gray-500">Administrator</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex relative">
        {/* Sidebar */}
        <div className={`${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white shadow-sm lg:shadow-none min-h-[calc(100vh-4rem)] transition-transform duration-300 ease-in-out lg:transition-none`}>
          <div className="p-4 lg:p-6">
            {/* Quick Stats - Hidden on mobile */}
            <div className="hidden lg:grid grid-cols-2 gap-3 mb-8">
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-xs text-green-600 font-medium">Revenue</p>
                <p className="text-lg font-bold text-green-700">₹{stats.total_revenue}</p>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-xs text-blue-600 font-medium">Users</p>
                <p className="text-lg font-bold text-blue-700">{stats.total_users}</p>
              </div>
            </div>

            {/* Date Filter - Hidden on mobile */}
            <div className="hidden lg:block mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <div className="relative">
                <select className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none">
                  <option>Last 7 days</option>
                  <option>Last 30 days</option>
                  <option>Last 90 days</option>
                  <option>This year</option>
                </select>
                <Calendar className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* Navigation */}
            <nav className="space-y-1 lg:space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium transition-colors touch-target ${
                    item.current
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  {item.name}
                </Link>
              ))}

              <button
                onClick={() => {
                  handleAdminLogout();
                  setSidebarOpen(false);
                }}
                className="w-full flex items-center gap-3 px-3 py-3 lg:py-2 rounded-lg text-sm font-medium text-red-600 hover:bg-red-50 transition-colors touch-target"
              >
                <LogOut className="h-5 w-5" />
                Logout
              </button>
            </nav>
          </div>
        </div>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <div className="flex-1 p-4 sm:p-6 lg:p-8">
          <Routes>
            <Route path="/" element={<AdminStatsPage stats={stats} />} />
            <Route path="/users" element={<AdminUsersPage />} />
            <Route path="/referral-tree" element={<AdminReferralTreePage />} />
            <Route path="/wallet" element={<AdminWalletPage />} />
            <Route path="/mlm-settings" element={<AdminMLMSettingsPage />} />
            <Route path="/settings" element={<AdminSettingsPage />} />
          </Routes>
        </div>
      </div>
    </div>
  );
}