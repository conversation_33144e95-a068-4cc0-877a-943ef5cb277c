import React, { useState } from 'react';
import { mlmReferralService, MLM_CONFIG } from '../services/mlmReferralService';
import toast from 'react-hot-toast';

interface TestScenario {
  name: string;
  description: string;
  referrerName: string;
  existingReferrals: number;
  newReferralName: string;
  expectedRecipient: string;
  expectedReason: string;
}

const MLMTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testScenarios: TestScenario[] = [
    {
      name: "First Referral",
      description: "<PERSON>'s first referral (<PERSON>)",
      referrerName: "<PERSON>",
      existingReferrals: 0,
      newReferralName: "<PERSON>",
      expectedRecipient: "<PERSON>",
      expectedReason: "Direct referral #1 - bonus to direct referrer"
    },
    {
      name: "Second Referral", 
      description: "<PERSON>'s second referral (<PERSON>)",
      referrerName: "<PERSON>",
      existingReferrals: 1,
      newReferralName: "<PERSON>",
      expectedRecipient: "<PERSON>",
      expectedReason: "Direct referral #2 - bonus to direct referrer"
    },
    {
      name: "Third Referral",
      description: "<PERSON>'s third referral (Peter) - goes to grandparent",
      referrerName: "Rachel",
      existingReferrals: 2,
      newReferralName: "Peter",
      expectedRecipient: "You (Rachel's referrer)",
      expectedReason: "3rd referral - bonus to grandparent"
    },
    {
      name: "Fourth Referral",
      description: "Rachel's fourth referral - back to Rachel",
      referrerName: "Rachel",
      existingReferrals: 3,
      newReferralName: "Sarah",
      expectedRecipient: "Rachel",
      expectedReason: "4th referral - bonus back to direct referrer"
    }
  ];

  const runMLMLogicTest = () => {
    setLoading(true);
    const results: any[] = [];

    testScenarios.forEach((scenario, index) => {
      const newReferralPosition = scenario.existingReferrals + 1;
      
      // Simulate the MLM logic
      let recipient: string;
      let reason: string;

      if (newReferralPosition <= MLM_CONFIG.DIRECT_REFERRAL_LIMIT) {
        recipient = scenario.referrerName;
        reason = `Direct referral #${newReferralPosition} - bonus to direct referrer`;
      } else if (newReferralPosition === MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION) {
        recipient = `${scenario.referrerName}'s referrer`;
        reason = `3rd referral - bonus to grandparent`;
      } else {
        recipient = scenario.referrerName;
        reason = `${newReferralPosition}th referral - bonus back to direct referrer`;
      }

      const result = {
        scenario: scenario.name,
        description: scenario.description,
        referrerName: scenario.referrerName,
        newReferralName: scenario.newReferralName,
        referralPosition: newReferralPosition,
        bonusRecipient: recipient,
        bonusAmount: MLM_CONFIG.BONUS_AMOUNT,
        reason: reason,
        isCorrect: recipient.includes(scenario.expectedRecipient.split(' ')[0])
      };

      results.push(result);
    });

    setTestResults(results);
    setLoading(false);
    toast.success('MLM logic test completed!');
  };

  const ResultCard: React.FC<{ result: any }> = ({ result }) => (
    <div className={`p-4 rounded-lg border ${result.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold text-lg">{result.scenario}</h3>
        <span className={`px-2 py-1 rounded text-sm ${result.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {result.isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}
        </span>
      </div>
      
      <p className="text-gray-600 mb-3">{result.description}</p>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <p><strong>Referrer:</strong> {result.referrerName}</p>
          <p><strong>New Referral:</strong> {result.newReferralName}</p>
          <p><strong>Position:</strong> #{result.referralPosition}</p>
        </div>
        <div>
          <p><strong>Bonus Recipient:</strong> {result.bonusRecipient}</p>
          <p><strong>Bonus Amount:</strong> ₹{result.bonusAmount}</p>
          <p><strong>Reason:</strong> {result.reason}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">🎯 MLM Referral Logic Test</h1>
            <p className="text-gray-600">
              Test the new MLM referral bonus distribution logic
            </p>
          </div>

          {/* MLM Rules Summary */}
          <div className="bg-blue-50 p-6 rounded-lg mb-8">
            <h2 className="text-xl font-semibold text-blue-800 mb-4">📋 MLM Rules</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-green-600 mb-2">✅ 1st & 2nd Referrals</h3>
                <p className="text-sm text-gray-700">
                  Bonus goes to the <strong>direct referrer</strong>
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Amount: ₹{MLM_CONFIG.BONUS_AMOUNT} each
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-blue-600 mb-2">⬆️ 3rd+ Referrals</h3>
                <p className="text-sm text-gray-700">
                  Bonus goes to the <strong>referrer's referrer</strong> (grandparent)
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Amount: ₹{MLM_CONFIG.BONUS_AMOUNT} each
                </p>
              </div>
            </div>
          </div>

          {/* Test Button */}
          <div className="text-center mb-8">
            <button
              onClick={runMLMLogicTest}
              disabled={loading}
              className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-semibold"
            >
              {loading ? 'Running Test...' : 'Run MLM Logic Test'}
            </button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-800">Test Results</h2>
              
              {/* Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{testResults.length}</div>
                    <div className="text-sm text-gray-600">Total Tests</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {testResults.filter(r => r.isCorrect).length}
                    </div>
                    <div className="text-sm text-gray-600">Passed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">
                      {testResults.filter(r => !r.isCorrect).length}
                    </div>
                    <div className="text-sm text-gray-600">Failed</div>
                  </div>
                </div>
              </div>

              {/* Individual Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {testResults.map((result, index) => (
                  <ResultCard key={index} result={result} />
                ))}
              </div>
            </div>
          )}

          {/* Example Tree Visualization */}
          <div className="mt-12 bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">🌳 Example MLM Tree</h2>
            <div className="text-center">
              <div className="inline-block">
                <div className="bg-blue-600 text-white px-4 py-2 rounded-lg mb-4">
                  You (Grandparent)
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-400"></div>
                </div>
                <div className="bg-green-600 text-white px-4 py-2 rounded-lg mb-4">
                  Rachel (Direct Referrer)
                </div>
                <div className="flex justify-center gap-8">
                  <div className="text-center">
                    <div className="w-px h-8 bg-gray-400 mx-auto"></div>
                    <div className="bg-green-100 text-green-800 px-3 py-2 rounded text-sm mb-2">
                      Chris (#1) → Rachel ✅
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="w-px h-8 bg-gray-400 mx-auto"></div>
                    <div className="bg-green-100 text-green-800 px-3 py-2 rounded text-sm mb-2">
                      Maria (#2) → Rachel ✅
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="w-px h-8 bg-gray-400 mx-auto"></div>
                    <div className="bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm mb-2">
                      Peter (#3) → You ⬆️
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MLMTestPage;
