import React, { useState, useEffect } from 'react';
import { Wallet, Plus, Minus, History, CreditCard, Gift, X, Check, AlertCircle } from 'lucide-react';
import { useAuthStore } from '../stores/authStore';
import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';

interface Transaction {
  id: string;
  user_id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_type: string;
  reference_id?: string;
  created_at: string;
}

const WalletPage: React.FC = () => {
  const { user, refreshUser } = useAuthStore();
  const [showAddMoneyModal, setShowAddMoneyModal] = useState(false);
  const [addAmount, setAddAmount] = useState('');
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [paymentMethod, setPaymentMethod] = useState('upi');
  const [isProcessing, setIsProcessing] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);

  // Predefined amounts for quick selection
  const quickAmounts = [100, 250, 500, 1000, 2000, 5000];

  // Payment methods
  const paymentMethods = [
    { id: 'upi', name: 'UPI', icon: '📱', description: 'Pay using UPI apps' },
    { id: 'card', name: 'Credit/Debit Card', icon: '💳', description: 'Visa, Mastercard, RuPay' },
    { id: 'netbanking', name: 'Net Banking', icon: '🏦', description: 'All major banks' },
    { id: 'wallet', name: 'Digital Wallet', icon: '📲', description: 'Paytm, PhonePe, etc.' }
  ];

  // Fetch wallet transactions
  useEffect(() => {
    if (user?.id) {
      fetchTransactions();
    }
  }, [user?.id]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      setTransactions(data || []);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  };

  // Handle add money
  const handleAddMoney = async () => {
    if (!user?.id) {
      toast.error('Please log in to add money');
      return;
    }

    const amount = selectedAmount || parseFloat(addAmount);
    if (!amount || amount < 10) {
      toast.error('Minimum amount is ₹10');
      return;
    }

    if (amount > 50000) {
      toast.error('Maximum amount is ₹50,000 per transaction');
      return;
    }

    setIsProcessing(true);
    try {
      // Simulate payment processing
      await simulatePayment(amount, paymentMethod);

      // Add transaction to database
      const { error: transactionError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: user.id,
          type: 'credit',
          amount: amount,
          description: `Money added via ${paymentMethods.find(p => p.id === paymentMethod)?.name}`,
          reference_type: 'payment',
          reference_id: `PAY_${Date.now()}`
        });

      if (transactionError) throw transactionError;

      // Update user wallet balance
      const newBalance = (user.wallet_balance || 0) + amount;
      const { error: updateError } = await supabase
        .from('users')
        .update({ wallet_balance: newBalance })
        .eq('id', user.id);

      if (updateError) throw updateError;

      // Refresh user data and transactions
      await refreshUser();
      await fetchTransactions();

      toast.success(`₹${amount} added to your wallet successfully!`);
      setShowAddMoneyModal(false);
      setAddAmount('');
      setSelectedAmount(null);
    } catch (error) {
      console.error('Error adding money:', error);
      toast.error('Failed to add money. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Simulate payment processing (replace with actual payment gateway)
  const simulatePayment = async (amount: number, method: string): Promise<void> => {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // In production, integrate with payment gateways like:
    // - Razorpay
    // - Stripe
    // - PayU
    // - CCAvenue
    // - Paytm

    console.log(`Processing payment: ₹${amount} via ${method}`);

    // For demo, always succeed
    return Promise.resolve();
  };

  const handleQuickAmount = (amount: number) => {
    setSelectedAmount(amount);
    setAddAmount(amount.toString());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            My Wallet
          </h1>
          <p className="text-lg text-gray-600">
            Manage your Start Juicce wallet and transaction history
          </p>
        </div>

        {/* Wallet Balance Card */}
        <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg p-6 sm:p-8 text-white mb-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <Wallet className="w-8 h-8" />
                <h2 className="text-2xl font-bold">Wallet Balance</h2>
              </div>
              <p className="text-3xl sm:text-4xl font-bold">₹{(user?.wallet_balance || 0).toFixed(2)}</p>
              <p className="text-green-100 mt-1">Available for purchases</p>
            </div>
            <div className="text-right">
              <div className="bg-white/20 rounded-lg p-4">
                <CreditCard className="w-12 h-12 mx-auto mb-2" />
                <p className="text-sm">Start Juicce</p>
                <p className="text-xs opacity-75">Wallet</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h3>
              
              <div className="space-y-4">
                <button
                  onClick={() => setShowAddMoneyModal(true)}
                  className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 touch-target"
                >
                  <Plus className="w-5 h-5" />
                  <span>Add Money</span>
                </button>

                <button
                  onClick={() => toast.info('Withdraw feature coming soon!')}
                  className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2 touch-target"
                >
                  <Minus className="w-5 h-5" />
                  <span>Withdraw</span>
                </button>

                <button
                  onClick={() => toast.info('Referral program details coming soon!')}
                  className="w-full bg-blue-100 text-blue-700 py-3 px-4 rounded-lg font-medium hover:bg-blue-200 transition-colors flex items-center justify-center space-x-2 touch-target"
                >
                  <Gift className="w-5 h-5" />
                  <span>Refer & Earn</span>
                </button>
              </div>

              {/* Wallet Features */}
              <div className="mt-8">
                <h4 className="font-semibold text-gray-900 mb-4">Wallet Features</h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Instant payments</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Secure transactions</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Referral bonuses</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Cashback rewards</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Transaction History */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Transaction History</h3>
                <button className="text-green-600 hover:text-green-700 font-medium flex items-center space-x-1">
                  <History className="w-4 h-4" />
                  <span>View All</span>
                </button>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading transactions...</p>
                </div>
              ) : (
                <>
                  <div className="space-y-4">
                    {transactions.map((transaction) => (
                      <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex items-center space-x-4">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            transaction.type === 'credit'
                              ? 'bg-green-100 text-green-600'
                              : 'bg-red-100 text-red-600'
                          }`}>
                            {transaction.type === 'credit' ? (
                              <Plus className="w-5 h-5" />
                            ) : (
                              <Minus className="w-5 h-5" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{transaction.description}</p>
                            <p className="text-sm text-gray-500">{formatDate(transaction.created_at)}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`font-semibold ${
                            transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.type === 'credit' ? '+' : '-'}₹{transaction.amount.toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500 capitalize">{transaction.reference_type}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {transactions.length === 0 && (
                    <div className="text-center py-8">
                      <History className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h4 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h4>
                      <p className="text-gray-500">Your transaction history will appear here</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

        {/* Wallet Info */}
        <div className="mt-8 bg-blue-50 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">💡 Wallet Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-1">Earn More</h4>
              <p>Refer friends to earn bonus credits in your wallet</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Secure Payments</h4>
              <p>Your wallet uses bank-grade security for all transactions</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Instant Checkout</h4>
              <p>Pay for orders instantly using your wallet balance</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">No Expiry</h4>
              <p>Your wallet balance never expires and is always available</p>
            </div>
          </div>
        </div>

        {/* Add Money Modal */}
        {showAddMoneyModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                {/* Modal Header */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">Add Money to Wallet</h2>
                  <button
                    onClick={() => setShowAddMoneyModal(false)}
                    className="text-gray-400 hover:text-gray-600 p-1"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                {/* Quick Amount Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Quick Select Amount
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {quickAmounts.map((amount) => (
                      <button
                        key={amount}
                        onClick={() => handleQuickAmount(amount)}
                        className={`p-3 border rounded-lg text-center font-medium transition-colors ${
                          selectedAmount === amount
                            ? 'border-green-600 bg-green-50 text-green-700'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        ₹{amount}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Custom Amount */}
                <div className="mb-6">
                  <label htmlFor="customAmount" className="block text-sm font-medium text-gray-700 mb-2">
                    Or Enter Custom Amount
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                    <input
                      id="customAmount"
                      type="number"
                      value={addAmount}
                      onChange={(e) => {
                        setAddAmount(e.target.value);
                        setSelectedAmount(null);
                      }}
                      className="w-full pl-8 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Enter amount"
                      min="10"
                      max="50000"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Minimum: ₹10, Maximum: ₹50,000</p>
                </div>

                {/* Payment Method Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Select Payment Method
                  </label>
                  <div className="space-y-3">
                    {paymentMethods.map((method) => (
                      <label
                        key={method.id}
                        className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                          paymentMethod === method.id
                            ? 'border-green-600 bg-green-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <input
                          type="radio"
                          name="paymentMethod"
                          value={method.id}
                          checked={paymentMethod === method.id}
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          className="sr-only"
                        />
                        <div className="flex items-center space-x-3 flex-1">
                          <span className="text-2xl">{method.icon}</span>
                          <div>
                            <p className="font-medium text-gray-900">{method.name}</p>
                            <p className="text-sm text-gray-500">{method.description}</p>
                          </div>
                        </div>
                        {paymentMethod === method.id && (
                          <Check className="w-5 h-5 text-green-600" />
                        )}
                      </label>
                    ))}
                  </div>
                </div>

                {/* Security Notice */}
                <div className="mb-6 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">Secure Payment</p>
                      <p>Your payment is processed securely using industry-standard encryption.</p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowAddMoneyModal(false)}
                    className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddMoney}
                    disabled={isProcessing || !addAmount || parseFloat(addAmount) < 10}
                    className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Processing...</span>
                      </>
                    ) : (
                      <>
                        <Plus className="w-4 h-4" />
                        <span>Add ₹{addAmount || '0'}</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WalletPage;
