import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, Star, Award, Gift, Gem, ShieldCheck } from 'lucide-react';

const PremiumPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-yellow-100 py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto bg-white p-10 rounded-xl shadow-2xl">
        <h1 className="text-5xl font-extrabold text-yellow-800 mb-6 text-center leading-tight">
          Unlock <span className="text-yellow-600">Premium</span> Benefits
        </h1>
        <p className="text-xl text-gray-700 mb-10 text-center">
          Elevate your wellness journey with exclusive features, deeper insights, and special rewards.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-12">
          <div className="bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200">
            <div className="flex items-center text-yellow-700 mb-4">
              <CheckCircle className="h-6 w-6 mr-3" />
              <h2 className="text-2xl font-semibold">Exclusive Products</h2>
            </div>
            <p className="text-gray-600">
              Gain access to limited-edition herbal formulations and unique products available only to premium members.
            </p>
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200">
            <div className="flex items-center text-yellow-700 mb-4">
              <Award className="h-6 w-6 mr-3" />
              <h2 className="text-2xl font-semibold">Special Discounts</h2>
            </div>
            <p className="text-gray-600">
              Enjoy up to 25% off on all purchases, plus access to exclusive member-only sales and promotions.
            </p>
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200">
            <div className="flex items-center text-yellow-700 mb-4">
              <Gift className="h-6 w-6 mr-3" />
              <h2 className="text-2xl font-semibold">Referral Rewards</h2>
            </div>
            <p className="text-gray-600">
              Earn significant wallet credits for every friend you refer who joins our premium community.
            </p>
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg shadow-md border border-yellow-200">
            <div className="flex items-center text-yellow-700 mb-4">
              <ShieldCheck className="h-6 w-6 mr-3" />
              <h2 className="text-2xl font-semibold">Priority Support</h2>
            </div>
            <p className="text-gray-600">
              Get expedited customer service and personalized consultations with our herbal experts.
            </p>
          </div>
        </div>

        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Choose Your Plan</h2>
          <div className="flex flex-col md:flex-row justify-center items-center space-y-6 md:space-y-0 md:space-x-8">
            <div className="bg-white p-8 rounded-lg shadow-lg border-2 border-yellow-400 transform hover:scale-105 transition-transform duration-300 w-full md:w-auto">
              <h3 className="text-3xl font-bold text-yellow-600 mb-4">Monthly</h3>
              <p className="text-4xl font-extrabold text-gray-900 mb-2">₹299<span className="text-xl font-medium text-gray-600">/month</span></p>
              <p className="text-gray-500 mb-6">Billed monthly</p>
              <button className="w-full bg-yellow-500 text-white font-bold py-3 rounded-full hover:bg-yellow-600 transition-colors shadow-md">
                Subscribe Monthly
              </button>
            </div>

            <div className="bg-yellow-500 text-white p-8 rounded-lg shadow-xl transform hover:scale-105 transition-transform duration-300 w-full md:w-auto relative overflow-hidden">
              <span className="absolute top-0 right-0 bg-yellow-700 text-xs font-bold px-3 py-1 rounded-bl-lg">BEST VALUE</span>
              <h3 className="text-3xl font-bold mb-4">Yearly</h3>
              <p className="text-4xl font-extrabold mb-2">₹2999<span className="text-xl font-medium">/year</span></p>
              <p className="text-yellow-100 mb-6">Billed annually (Save 17%)</p>
              <button className="w-full bg-white text-yellow-700 font-bold py-3 rounded-full hover:bg-gray-100 transition-colors shadow-md">
                Subscribe Yearly
              </button>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-600">
            Have questions about Premium? Visit our <Link to="/faq" className="text-yellow-600 hover:underline">FAQ page</Link>.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PremiumPage; 