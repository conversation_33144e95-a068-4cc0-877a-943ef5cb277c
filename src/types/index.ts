export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  address?: string;
  username?: string;
  is_premium: boolean;
  premium_lifetime_access?: boolean;
  premium_purchase_confirmed?: boolean;
  premium_no_refund_accepted?: boolean;
  premium_purchased_at?: string;
  premium_payment_id?: string;
  premium_refund_eligible?: boolean;
  wallet_balance: number;
  referral_code?: string;
  referred_by?: string;
  premium_referral_count?: number;
  ewallet_unlocked?: boolean;
  wallet_unlocked?: boolean;
  referral_special_status?: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  images?: string[];
  category: string;
  ingredients: string[];
  benefits?: string[];
  dosage?: string;
  warnings?: string;
  rating: number;
  reviews_count: number;
  stock_quantity: number;
  is_premium_only: boolean;
  is_featured?: boolean;
  original_price?: number;
  created_at: string;
  updated_at: string;
}

export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
}

export interface Order {
  id: string;
  user_id: string;
  items?: OrderItem[];
  total_amount: number;
  wallet_used: number;
  payment_method: 'wallet' | 'card' | 'mixed';
  delivery_address: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  created_at: string;
  updated_at: string;
  user?: User;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  product: Product;
  quantity: number;
  price: number;
  created_at: string;
}

export interface WalletTransaction {
  id: string;
  user_id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_type?: 'order' | 'referral' | 'admin' | 'payment';
  reference_id?: string;
  created_at: string;
  user?: User;
}

export interface Referral {
  id: string;
  referrer_id: string;
  referred_user_id: string;
  bonus_amount: number;
  status: 'pending' | 'completed';
  created_at: string;
  referred_user?: User;
}

export interface Review {
  id: string;
  product_id: string;
  user_id: string;
  user: User;
  rating: number;
  comment: string;
  created_at: string;
}

export interface AdminStats {
  total_revenue: number;
  total_users: number;
  premium_users: number;
  total_orders: number;
  wallet_transactions_total: number;
  total_withdrawals: number;
  referral_bonuses_paid: number;
}

export interface ReferralSettings {
  id?: string;
  referrer_bonus: number;
  referred_bonus: number;
  max_referrals_per_user: number;
  updated_at?: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  parent_id?: string;
  subcategories?: Category[];
  created_at: string;
  updated_at: string;
}