import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { useAuthStore } from './stores/authStore';
import { useProductStore } from './stores/productStore';
import { initializeAuth } from './utils/authUtils';
import { automaticReferralMonitor } from './services/automaticReferralMonitor';
import { walletBalanceMonitor } from './services/walletBalanceMonitor';

import './styles/responsive.css';

// Layout Components
import AppLayout from './components/layout/AppLayout';
import CartSlideout from './components/cart/CartSlideout';
import CursorAnimation from './components/CursorAnimation';

// Security Components
import AdminGuard from './components/auth/AdminGuard';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Pages
import HomePage from './pages/HomePage';
import ProductsPage from './pages/ProductsPage';
import ProductDetailPage from './pages/ProductDetailPage';
import CartPage from './pages/CartPage';
import CheckoutPage from './pages/CheckoutPage';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';
import ResetPasswordPage from './pages/auth/ResetPasswordPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminLogin from './components/admin/AdminLogin';
import AboutPage from './pages/AboutPage';
import PremiumPage from './pages/PremiumPage';
import ContactPage from './pages/ContactPage';
import DatabaseTest from './pages/DatabaseTest';
import MLMTestPage from './pages/MLMTestPage';
import ReferralSystemTestPage from './pages/ReferralSystemTestPage';
import ReferralEarningsGuide from './pages/ReferralEarningsGuide';

function App() {
  const { fetchProducts } = useProductStore();
  const { checkUser } = useAuthStore();

  useEffect(() => {
    // Initialize authentication and stores
    const initApp = async () => {
      try {
        // Initialize auth first
        await initializeAuth();

        // Check current user session (this will handle missing sessions gracefully)
        // Temporarily disabled to prevent session clearing issues
        // await checkUser();

        // Then initialize stores
        fetchProducts();

        // Start automatic referral monitoring (disabled for now to prevent console errors)
        // automaticReferralMonitor.startMonitoring();
        // console.log('Automatic referral monitoring started');

        // Start automatic wallet balance monitoring
        walletBalanceMonitor.startMonitoring();
        console.log('Automatic wallet balance monitoring started');

        // Note: Auth service has limitations in current environment
        // Continue testing with admin panel for user management
      } catch (error) {
        console.error('App initialization error:', error);
        // Continue with store initialization even if auth fails
        fetchProducts();
      }
    };

    initApp();
  }, [fetchProducts, checkUser]);

  return (
    <Router
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <CursorAnimation />
      <AppLayout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/products" element={<ProductsPage />} />
          <Route path="/products/:id" element={<ProductDetailPage />} />
          <Route path="/cart" element={<CartPage />} />
          <Route path="/checkout" element={<CheckoutPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/reset-password" element={<ResetPasswordPage />} />
          <Route path="/dashboard/*" element={
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          } />
          <Route path="/admin" element={<AdminLogin />} />
          <Route path="/admin/dashboard/*" element={
            <AdminGuard>
              <AdminDashboard />
            </AdminGuard>
          } />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/premium" element={<PremiumPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/db-test" element={<DatabaseTest />} />
          <Route path="/mlm-test" element={<MLMTestPage />} />
          <Route path="/referral-test" element={<ReferralSystemTestPage />} />
          <Route path="/earnings-guide" element={<ReferralEarningsGuide />} />

          {/* Redirect routes for direct access attempts */}
          <Route path="/wallet" element={<Navigate to="/dashboard/wallet" replace />} />
          <Route path="/referrals" element={<Navigate to="/dashboard/referrals" replace />} />
          <Route path="/profile" element={<Navigate to="/dashboard/profile" replace />} />
          <Route path="/orders" element={<Navigate to="/dashboard/orders" replace />} />
          <Route path="/kyc" element={<Navigate to="/dashboard/kyc" replace />} />
        </Routes>
      </AppLayout>

      <CartSlideout />

      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            style: {
              background: '#10B981',
            },
          },
          error: {
            style: {
              background: '#EF4444',
            },
          },
        }}
      />
    </Router>
  );
}

export default App;