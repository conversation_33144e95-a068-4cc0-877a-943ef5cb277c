import { useEffect, useState, useRef } from 'react';
import styled from '@emotion/styled';

const CursorDot = styled.div`
  width: 8px;
  height: 8px;
  background-color: #4CAF50;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  will-change: transform;
  mix-blend-mode: difference;
`;

const CursorRing = styled.div`
  width: 40px;
  height: 40px;
  border: 2px solid #4CAF50;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  z-index: 9998;
  will-change: transform, width, height;
  mix-blend-mode: difference;
`;

const CursorTrail = styled.div`
  width: 4px;
  height: 4px;
  background-color: #4CAF50;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  z-index: 9997;
  opacity: 0.3;
  will-change: transform, opacity;
  mix-blend-mode: difference;
`;

const Particle = styled.div`
  position: fixed;
  pointer-events: none;
  z-index: 9996;
  will-change: transform, opacity;
  mix-blend-mode: difference;
  transform-origin: center;
`;

const TextReveal = styled.div`
  position: fixed;
  pointer-events: none;
  z-index: 9995;
  color: #4CAF50;
  font-size: 12px;
  font-weight: 600;
  opacity: 0;
  transform: translateY(10px) scale(0.95);
  will-change: transform, opacity;
  mix-blend-mode: difference;
  text-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
  white-space: nowrap;
  transition: all 0.15s ease-out;
  padding: 2px 6px;
  border-radius: 4px;
  letter-spacing: 0.5px;
`;

interface ParticleData {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  color: string;
  scale: number;
  rotation: number;
}

const CursorAnimation = () => {
  const [isPointer, setIsPointer] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [hoverText, setHoverText] = useState('');
  const [showText, setShowText] = useState(false);
  const [particles, setParticles] = useState<ParticleData[]>([]);
  const textTimeoutRef = useRef<number>();
  const lastHoverTargetRef = useRef<HTMLElement | null>(null);
  const isHoveringRef = useRef(false);
  const lastPositionRef = useRef({ x: 0, y: 0 });
  
  const cursorRef = useRef({
    x: 0,
    y: 0,
    targetX: 0,
    targetY: 0,
    trail: [] as Array<{ x: number; y: number }>,
    lastUpdate: 0
  });

  const animationFrameRef = useRef<number>();
  const lastTimeRef = useRef(0);

  const createParticle = (x: number, y: number) => ({
    x,
    y,
    vx: (Math.random() - 0.5) * 3,
    vy: (Math.random() - 0.5) * 3,
    life: 1,
    color: `hsl(${Math.random() * 40 + 120}, 70%, 50%)`,
    scale: Math.random() * 0.5 + 0.5,
    rotation: Math.random() * 360
  });

  const updateParticles = (delta: number) => {
    setParticles(prev => 
      prev
        .map(p => ({
          ...p,
          x: p.x + p.vx * (delta / 16),
          y: p.y + p.vy * (delta / 16),
          life: p.life - (0.015 * (delta / 16)),
          vy: p.vy + 0.08 * (delta / 16),
          rotation: p.rotation + 2 * (delta / 16)
        }))
        .filter(p => p.life > 0)
    );
  };

  useEffect(() => {
    const updateCursor = (time: number) => {
      const delta = time - lastTimeRef.current;
      lastTimeRef.current = time;

      const ease = 0.15;
      cursorRef.current.x += (cursorRef.current.targetX - cursorRef.current.x) * ease;
      cursorRef.current.y += (cursorRef.current.targetY - cursorRef.current.y) * ease;

      if (delta > 16) {
        cursorRef.current.trail = [
          { x: cursorRef.current.x, y: cursorRef.current.y },
          ...cursorRef.current.trail.slice(0, 7)
        ];
      }

      updateParticles(delta);
      setPosition({ x: cursorRef.current.x, y: cursorRef.current.y });

      animationFrameRef.current = requestAnimationFrame(updateCursor);
    };

    const handleMouseMove = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isInteractive = target.closest('button, a, [role="button"], input, select, textarea');
      
      // Update position immediately
      lastPositionRef.current = { x: e.clientX, y: e.clientY };
      
      if (isInteractive) {
        const interactiveElement = target.closest('button, a, [role="button"], input, select, textarea') as HTMLElement;
        
        if (lastHoverTargetRef.current !== interactiveElement) {
          // Clear any existing timeout
          if (textTimeoutRef.current) {
            clearTimeout(textTimeoutRef.current);
            textTimeoutRef.current = undefined;
          }

          lastHoverTargetRef.current = interactiveElement;
          isHoveringRef.current = true;
          
          const text = interactiveElement.getAttribute('data-cursor-text') || 
                      (interactiveElement instanceof HTMLAnchorElement ? 'Link' : 
                       interactiveElement instanceof HTMLButtonElement ? 'Click' : 'Interactive');
          
          setHoverText(text);
          setShowText(true);
        }
      } else {
        if (isHoveringRef.current) {
          isHoveringRef.current = false;
          lastHoverTargetRef.current = null;
          
          if (textTimeoutRef.current) {
            clearTimeout(textTimeoutRef.current);
          }
          
          textTimeoutRef.current = window.setTimeout(() => {
            if (!isHoveringRef.current) {
              setShowText(false);
              setHoverText('');
            }
          }, 50);
        }
      }

      if (isInteractive) {
        const rect = target.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const distanceX = e.clientX - centerX;
        const distanceY = e.clientY - centerY;
        const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);
        const maxDistance = Math.max(rect.width, rect.height) * 0.5;
        
        if (distance < maxDistance) {
          const strength = 1 - (distance / maxDistance);
          cursorRef.current.targetX = centerX + distanceX * strength * 0.3;
          cursorRef.current.targetY = centerY + distanceY * strength * 0.3;
        } else {
          cursorRef.current.targetX = e.clientX;
          cursorRef.current.targetY = e.clientY;
        }
      } else {
        cursorRef.current.targetX = e.clientX;
        cursorRef.current.targetY = e.clientY;
      }

      setIsPointer(window.getComputedStyle(target).cursor === 'pointer');
    };

    const handleMouseDown = () => {
      setIsClicking(true);
      setParticles(prev => [
        ...prev,
        ...Array(8).fill(null).map(() => createParticle(cursorRef.current.x, cursorRef.current.y))
      ]);
    };

    const handleMouseUp = () => setIsClicking(false);

    animationFrameRef.current = requestAnimationFrame(updateCursor);

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (textTimeoutRef.current) {
        clearTimeout(textTimeoutRef.current);
      }
      setShowText(false);
      setHoverText('');
      isHoveringRef.current = false;
      lastHoverTargetRef.current = null;
    };
  }, []);

  const [position, setPosition] = useState({ x: 0, y: 0 });

  return (
    <>
      {particles.map((particle, index) => (
        <Particle
          key={index}
          style={{
            transform: `translate(${particle.x - 2}px, ${particle.y - 2}px) rotate(${particle.rotation}deg) scale(${particle.scale})`,
            opacity: particle.life,
            backgroundColor: particle.color,
            width: '4px',
            height: '4px',
            borderRadius: '50%',
            boxShadow: `0 0 ${particle.life * 4}px ${particle.color}`
          }}
        />
      ))}
      {cursorRef.current.trail.map((pos, index) => (
        <CursorTrail
          key={index}
          style={{
            transform: `translate(${pos.x - 2}px, ${pos.y - 2}px)`,
            opacity: 0.3 * (1 - index / cursorRef.current.trail.length)
          }}
        />
      ))}
      {showText && hoverText && (
        <TextReveal
          style={{
            transform: `translate(${lastPositionRef.current.x + 20}px, ${lastPositionRef.current.y - 20}px) translateY(${showText ? 0 : 10}px) scale(${showText ? 1 : 0.95})`,
            opacity: showText ? 1 : 0,
          }}
        >
          {hoverText}
        </TextReveal>
      )}
      <CursorDot
        style={{
          transform: `translate(${position.x - 4}px, ${position.y - 4}px) scale(${isClicking ? 0.8 : isPointer ? 1.5 : 1})`,
          opacity: isPointer ? 0.5 : 1,
        }}
      />
      <CursorRing
        style={{
          transform: `translate(${position.x - 20}px, ${position.y - 20}px) scale(${isClicking ? 0.8 : isPointer ? 1.5 : 1})`,
          opacity: isPointer ? 0.5 : 0.3,
          width: isClicking ? '30px' : '40px',
          height: isClicking ? '30px' : '40px',
        }}
      />
    </>
  );
};

export default CursorAnimation; 