import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';

interface WalletTransaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_type: string;
  created_at: string;
  status?: string;
}

interface WalletStats {
  balance: number;
  totalCredits: number;
  totalDebits: number;
  transactionCount: number;
  unlocked: boolean;
}

export const WalletDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [stats, setStats] = useState<WalletStats>({
    balance: 0,
    totalCredits: 0,
    totalDebits: 0,
    transactionCount: 0,
    unlocked: false
  });
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const isPremium = user?.is_premium && user?.premium_lifetime_access;
  // Wallet is always unlocked for premium users - no lock functionality
  const isWalletUnlocked = isPremium;

  useEffect(() => {
    if (user?.id) {
      loadWalletData();
    }
  }, [user?.id]);

  const loadWalletData = async () => {
    try {
      setLoading(true);

      // Get user's current wallet info - no lock checks needed
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('wallet_balance, is_premium, premium_lifetime_access')
        .eq('id', user?.id)
        .single();

      if (userError) throw userError;

      // Get wallet transactions with real-time data
      const { data: transactionData, error: transactionError } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (transactionError) throw transactionError;

      setTransactions(transactionData || []);

      // Calculate stats
      const totalCredits = transactionData
        ?.filter(t => t.type === 'credit')
        .reduce((sum, t) => sum + t.amount, 0) || 0;

      const totalDebits = transactionData
        ?.filter(t => t.type === 'debit')
        .reduce((sum, t) => sum + t.amount, 0) || 0;

      setStats({
        balance: userData?.wallet_balance || 0,
        totalCredits,
        totalDebits,
        transactionCount: transactionData?.length || 0,
        unlocked: true // Always unlocked for premium users
      });

    } catch (error) {
      console.error('Error loading wallet data:', error);
      toast.error('Failed to load wallet data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type: string, referenceType: string) => {
    if (type === 'credit') {
      if (referenceType === 'referral') return '👥';
      if (referenceType === 'wallet_unlock') return '🔓';
      if (referenceType === 'admin_credit') return '👨‍💼';
      return '💰';
    } else {
      if (referenceType === 'purchase') return '🛒';
      if (referenceType === 'withdrawal') return '💸';
      return '💳';
    }
  };

  const handleUpgradeClick = () => {
    setShowUpgradeModal(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading wallet...</span>
      </div>
    );
  }

  // Blur content for non-premium users
  const contentClass = !isPremium ? 'filter blur-sm pointer-events-none' : '';

  return (
    <div className="space-y-6 relative">
      {/* Premium Upgrade Overlay */}
      {!isPremium && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-90">
          <div className="text-center p-8 bg-white rounded-lg shadow-lg border-2 border-blue-500">
            <div className="text-6xl mb-4">🔒</div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Premium Feature</h3>
            <p className="text-gray-600 mb-4">
              Wallet features are available only for premium users.
            </p>
            <button
              onClick={handleUpgradeClick}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-semibold"
            >
              Upgrade to Premium
            </button>
          </div>
        </div>
      )}

      {/* Wallet Content */}
      <div className={contentClass}>
        {/* Wallet Balance Card */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium opacity-90">Wallet Balance</h2>
              <p className="text-3xl font-bold">{formatCurrency(stats.balance)}</p>
              <span className="inline-flex items-center px-2 py-1 bg-green-500 text-white text-xs rounded-full mt-2">
                ✅ Premium Wallet
              </span>
            </div>
            <div className="text-right">
              <div className="text-sm opacity-90">Status</div>
              <div className="text-lg font-semibold">
                {isPremium ? '✅ Premium' : '⭐ Standard'}
              </div>
            </div>
          </div>
        </div>

        {/* Wallet Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-green-600 text-xl">💰</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-gray-600">Total Credits</p>
                <p className="text-lg font-semibold text-green-600">
                  {formatCurrency(stats.totalCredits)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-red-600 text-xl">💸</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-gray-600">Total Debits</p>
                <p className="text-lg font-semibold text-red-600">
                  {formatCurrency(stats.totalDebits)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-blue-600 text-xl">📊</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-gray-600">Transactions</p>
                <p className="text-lg font-semibold text-blue-600">
                  {stats.transactionCount}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Transaction History */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b">
            <h3 className="text-lg font-semibold">Transaction History</h3>
            <p className="text-sm text-gray-600">Recent wallet transactions</p>
          </div>

          <div className="divide-y">
            {transactions.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No transactions yet
              </div>
            ) : (
              transactions.map((transaction) => (
                <div key={transaction.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">
                        {getTransactionIcon(transaction.type, transaction.reference_type)}
                      </span>
                      <div>
                        <p className="font-medium text-gray-900">
                          {transaction.description}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(transaction.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${
                        transaction.type === 'credit' 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {transaction.type === 'credit' ? '+' : '-'}
                        {formatCurrency(transaction.amount)}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">
                        {transaction.reference_type.replace('_', ' ')}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Premium Wallet Info */}
        {isPremium && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-2">💎 Premium Wallet Access</h4>
            <p className="text-green-800 text-sm">
              You have full access to all premium wallet features including transactions, referral bonuses, and withdrawals.
            </p>
          </div>
        )}
      </div>

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-bold mb-4">Upgrade to Premium</h3>
            <p className="text-gray-600 mb-4">
              Get access to wallet features, referral system, and exclusive benefits.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowUpgradeModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowUpgradeModal(false);
                  // Navigate to premium upgrade page
                  window.location.href = '/premium';
                }}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Upgrade Now
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WalletDashboard;
