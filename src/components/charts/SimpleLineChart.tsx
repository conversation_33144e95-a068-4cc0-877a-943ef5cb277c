import React from 'react';

interface DataPoint {
  label: string;
  value: number;
}

interface SimpleLineChartProps {
  data: DataPoint[];
  title?: string;
  color?: string;
  height?: number;
}

const SimpleLineChart: React.FC<SimpleLineChartProps> = ({ 
  data, 
  title, 
  color = '#10B981', 
  height = 200 
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;
  
  const width = 400;
  const padding = 40;
  const chartWidth = width - (padding * 2);
  const chartHeight = height - (padding * 2);

  // Generate path for the line
  const pathData = data.map((point, index) => {
    const x = padding + (index * chartWidth) / (data.length - 1);
    const y = padding + chartHeight - ((point.value - minValue) / range) * chartHeight;
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  // Generate points for dots
  const points = data.map((point, index) => {
    const x = padding + (index * chartWidth) / (data.length - 1);
    const y = padding + chartHeight - ((point.value - minValue) / range) * chartHeight;
    return { x, y, value: point.value, label: point.label };
  });

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      
      <div className="relative">
        <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`} className="overflow-visible">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Y-axis labels */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
            const y = padding + chartHeight - (ratio * chartHeight);
            const value = minValue + (ratio * range);
            return (
              <g key={index}>
                <line 
                  x1={padding - 5} 
                  y1={y} 
                  x2={padding} 
                  y2={y} 
                  stroke="#6b7280" 
                  strokeWidth="1"
                />
                <text 
                  x={padding - 10} 
                  y={y + 4} 
                  textAnchor="end" 
                  className="text-xs fill-gray-600"
                >
                  {value.toFixed(0)}
                </text>
              </g>
            );
          })}
          
          {/* X-axis labels */}
          {data.map((point, index) => {
            const x = padding + (index * chartWidth) / (data.length - 1);
            return (
              <g key={index}>
                <line 
                  x1={x} 
                  y1={padding + chartHeight} 
                  x2={x} 
                  y2={padding + chartHeight + 5} 
                  stroke="#6b7280" 
                  strokeWidth="1"
                />
                <text 
                  x={x} 
                  y={padding + chartHeight + 20} 
                  textAnchor="middle" 
                  className="text-xs fill-gray-600"
                >
                  {point.label}
                </text>
              </g>
            );
          })}
          
          {/* Area under the line */}
          <path
            d={`${pathData} L ${padding + chartWidth} ${padding + chartHeight} L ${padding} ${padding + chartHeight} Z`}
            fill={color}
            fillOpacity="0.1"
          />
          
          {/* Main line */}
          <path
            d={pathData}
            fill="none"
            stroke={color}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {points.map((point, index) => (
            <g key={index}>
              <circle
                cx={point.x}
                cy={point.y}
                r="4"
                fill="white"
                stroke={color}
                strokeWidth="3"
                className="hover:r-6 transition-all cursor-pointer"
              />
              {/* Tooltip on hover */}
              <circle
                cx={point.x}
                cy={point.y}
                r="12"
                fill="transparent"
                className="hover:fill-gray-100 hover:fill-opacity-20"
              >
                <title>{`${point.label}: ${point.value}`}</title>
              </circle>
            </g>
          ))}
        </svg>
      </div>
      
      {/* Legend */}
      <div className="flex items-center justify-center mt-4 space-x-6">
        <div className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: color }}
          ></div>
          <span className="text-sm text-gray-600">Revenue Trend</span>
        </div>
      </div>
    </div>
  );
};

export default SimpleLineChart;
