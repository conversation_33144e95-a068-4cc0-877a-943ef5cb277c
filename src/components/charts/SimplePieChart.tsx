import React from 'react';

interface PieDataPoint {
  label: string;
  value: number;
  color: string;
}

interface SimplePieChartProps {
  data: PieDataPoint[];
  title?: string;
  size?: number;
}

const SimplePieChart: React.FC<SimplePieChartProps> = ({ 
  data, 
  title, 
  size = 200 
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const center = size / 2;
  const radius = size / 2 - 20;

  let currentAngle = 0;
  const slices = data.map((item) => {
    const percentage = (item.value / total) * 100;
    const angle = (item.value / total) * 360;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    
    currentAngle += angle;

    // Calculate path for the slice
    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;
    
    const x1 = center + radius * Math.cos(startAngleRad);
    const y1 = center + radius * Math.sin(startAngleRad);
    const x2 = center + radius * Math.cos(endAngleRad);
    const y2 = center + radius * Math.sin(endAngleRad);
    
    const largeArcFlag = angle > 180 ? 1 : 0;
    
    const pathData = [
      `M ${center} ${center}`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ');

    return {
      ...item,
      pathData,
      percentage,
      startAngle,
      endAngle
    };
  });

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      
      <div className="flex flex-col lg:flex-row items-center gap-6">
        {/* Pie Chart */}
        <div className="relative">
          <svg width={size} height={size} className="transform -rotate-90">
            {slices.map((slice, index) => (
              <g key={index}>
                <path
                  d={slice.pathData}
                  fill={slice.color}
                  stroke="white"
                  strokeWidth="2"
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                >
                  <title>{`${slice.label}: ${slice.value} (${slice.percentage.toFixed(1)}%)`}</title>
                </path>
              </g>
            ))}
            
            {/* Center circle for donut effect */}
            <circle
              cx={center}
              cy={center}
              r={radius * 0.4}
              fill="white"
              stroke="#f3f4f6"
              strokeWidth="2"
            />
            
            {/* Center text */}
            <text
              x={center}
              y={center - 5}
              textAnchor="middle"
              className="text-sm font-semibold fill-gray-900 transform rotate-90"
              style={{ transformOrigin: `${center}px ${center}px` }}
            >
              Total
            </text>
            <text
              x={center}
              y={center + 10}
              textAnchor="middle"
              className="text-xs fill-gray-600 transform rotate-90"
              style={{ transformOrigin: `${center}px ${center}px` }}
            >
              {total}
            </text>
          </svg>
        </div>
        
        {/* Legend */}
        <div className="flex-1 min-w-0">
          <div className="space-y-3">
            {slices.map((slice, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full flex-shrink-0" 
                    style={{ backgroundColor: slice.color }}
                  ></div>
                  <span className="text-sm text-gray-700 truncate">{slice.label}</span>
                </div>
                <div className="text-right flex-shrink-0">
                  <div className="text-sm font-semibold text-gray-900">{slice.value}</div>
                  <div className="text-xs text-gray-500">{slice.percentage.toFixed(1)}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplePieChart;
