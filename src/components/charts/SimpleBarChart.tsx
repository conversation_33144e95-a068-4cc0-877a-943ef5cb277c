import React from 'react';

interface DataPoint {
  label: string;
  value: number;
}

interface SimpleBarChartProps {
  data: DataPoint[];
  title?: string;
  color?: string;
  height?: number;
}

const SimpleBarChart: React.FC<SimpleBarChartProps> = ({ 
  data, 
  title, 
  color = '#10B981', 
  height = 200 
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = 0; // Start from 0 for bar charts
  const range = maxValue - minValue || 1;
  
  const width = 400;
  const padding = 40;
  const chartWidth = width - (padding * 2);
  const chartHeight = height - (padding * 2);
  const barWidth = chartWidth / data.length * 0.7; // 70% of available space
  const barSpacing = chartWidth / data.length * 0.3; // 30% for spacing

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      
      <div className="relative">
        <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`} className="overflow-visible">
          {/* Grid lines */}
          <defs>
            <pattern id="grid-bar" width="40" height="20" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
            <linearGradient id="barGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style={{ stopColor: color, stopOpacity: 0.8 }} />
              <stop offset="100%" style={{ stopColor: color, stopOpacity: 0.6 }} />
            </linearGradient>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid-bar)" />
          
          {/* Y-axis labels */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
            const y = padding + chartHeight - (ratio * chartHeight);
            const value = minValue + (ratio * range);
            return (
              <g key={index}>
                <line 
                  x1={padding - 5} 
                  y1={y} 
                  x2={padding} 
                  y2={y} 
                  stroke="#6b7280" 
                  strokeWidth="1"
                />
                <text 
                  x={padding - 10} 
                  y={y + 4} 
                  textAnchor="end" 
                  className="text-xs fill-gray-600"
                >
                  {value.toFixed(0)}
                </text>
              </g>
            );
          })}
          
          {/* X-axis */}
          <line 
            x1={padding} 
            y1={padding + chartHeight} 
            x2={padding + chartWidth} 
            y2={padding + chartHeight} 
            stroke="#6b7280" 
            strokeWidth="1"
          />
          
          {/* Y-axis */}
          <line 
            x1={padding} 
            y1={padding} 
            x2={padding} 
            y2={padding + chartHeight} 
            stroke="#6b7280" 
            strokeWidth="1"
          />
          
          {/* Bars */}
          {data.map((point, index) => {
            const barHeight = ((point.value - minValue) / range) * chartHeight;
            const x = padding + (index * chartWidth) / data.length + (barSpacing / 2);
            const y = padding + chartHeight - barHeight;
            
            return (
              <g key={index}>
                {/* Bar */}
                <rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill="url(#barGradient)"
                  stroke={color}
                  strokeWidth="1"
                  rx="2"
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                >
                  <title>{`${point.label}: ${point.value}`}</title>
                </rect>
                
                {/* Value label on top of bar */}
                {point.value > 0 && (
                  <text
                    x={x + barWidth / 2}
                    y={y - 5}
                    textAnchor="middle"
                    className="text-xs fill-gray-700 font-medium"
                  >
                    {point.value}
                  </text>
                )}
                
                {/* X-axis label */}
                <text 
                  x={x + barWidth / 2} 
                  y={padding + chartHeight + 20} 
                  textAnchor="middle" 
                  className="text-xs fill-gray-600"
                >
                  {point.label}
                </text>
              </g>
            );
          })}
        </svg>
      </div>
      
      {/* Legend */}
      <div className="flex items-center justify-center mt-4 space-x-6">
        <div className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded" 
            style={{ backgroundColor: color }}
          ></div>
          <span className="text-sm text-gray-600">Revenue by Day</span>
        </div>
      </div>
    </div>
  );
};

export default SimpleBarChart;
