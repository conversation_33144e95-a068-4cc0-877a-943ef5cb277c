import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';

const AuthDebugger: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testSignIn = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('🔍 Testing sign in with:', { email });
      
      // Step 1: Test auth sign in
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) {
        setResult({ 
          step: 'auth', 
          success: false, 
          error: authError.message,
          details: authError 
        });
        return;
      }

      console.log('✅ Auth successful:', authData.user?.email);

      // Step 2: Test profile fetch
      try {
        const { data: profileData, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', authData.user?.id)
          .single();

        if (profileError) {
          setResult({
            step: 'profile',
            success: false,
            error: profileError.message,
            details: profileError,
            authSuccess: true,
            user: authData.user
          });
          return;
        }

        setResult({
          step: 'complete',
          success: true,
          user: authData.user,
          profile: profileData
        });

      } catch (profileError: any) {
        setResult({
          step: 'profile',
          success: false,
          error: profileError.message,
          details: profileError,
          authSuccess: true,
          user: authData.user
        });
      }

    } catch (error: any) {
      setResult({ 
        step: 'unknown', 
        success: false, 
        error: error.message,
        details: error 
      });
    } finally {
      setLoading(false);
    }
  };

  const testUsersTable = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, email, full_name')
        .limit(5);

      setResult({
        step: 'table_test',
        success: !error,
        data,
        error: error?.message,
        details: error
      });
    } catch (error: any) {
      setResult({
        step: 'table_test',
        success: false,
        error: error.message,
        details: error
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h3 className="text-lg font-bold mb-4">🔍 Auth Debugger</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Email:</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="Enter email to test"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Password:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="Enter password"
          />
        </div>

        <div className="flex gap-2">
          <button
            onClick={testSignIn}
            disabled={loading || !email || !password}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Sign In'}
          </button>
          
          <button
            onClick={testUsersTable}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
          >
            Test Users Table
          </button>
        </div>

        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h4 className="font-bold mb-2">
              {result.success ? '✅ Success' : '❌ Failed'} - Step: {result.step}
            </h4>
            
            {result.error && (
              <p className="text-red-600 mb-2">Error: {result.error}</p>
            )}
            
            <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthDebugger;
