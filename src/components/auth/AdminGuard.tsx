import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import { Shield, AlertTriangle, Lock } from 'lucide-react';

interface AdminGuardProps {
  children: React.ReactNode;
}

interface AdminValidation {
  isAdmin: boolean;
  isLoading: boolean;
  error?: string;
}

export const AdminGuard: React.FC<AdminGuardProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuthStore();
  const location = useLocation();
  const [adminValidation, setAdminValidation] = useState<AdminValidation>({
    isAdmin: false,
    isLoading: true
  });

  // Temporary bypass for debugging - remove in production
  const BYPASS_ADMIN_CHECK = process.env.NODE_ENV === 'development';

  useEffect(() => {
    validateAdminAccess();
  }, [user, isAuthenticated]);

  const validateAdminAccess = async () => {
    try {
      setAdminValidation({ isAdmin: false, isLoading: true });

      // Development bypass
      if (BYPASS_ADMIN_CHECK && user?.email === '<EMAIL>') {
        console.log('AdminGuard: Development bypass <NAME_EMAIL>');
        setAdminValidation({
          isAdmin: true,
          isLoading: false
        });
        return;
      }

      // First check if user is authenticated
      if (!isAuthenticated || !user) {
        console.log('AdminGuard: User not authenticated', { isAuthenticated, user: !!user });
        setAdminValidation({
          isAdmin: false,
          isLoading: false,
          error: 'Authentication required'
        });
        return;
      }

      console.log('AdminGuard: User authenticated', { userId: user.id, email: user.email });

      // Quick check for hardcoded admin emails first
      if (isHardcodedAdmin(user.email)) {
        console.log('AdminGuard: Hardcoded admin detected, granting access');
        setAdminValidation({
          isAdmin: true,
          isLoading: false
        });
        return;
      }

      // Check if user exists in database and has admin privileges
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, email, is_admin, admin_role, is_premium, created_at')
        .eq('id', user.id)
        .single();

      if (userError) {
        console.error('Admin validation error:', userError);
        setAdminValidation({
          isAdmin: false,
          isLoading: false,
          error: 'Database validation failed'
        });
        return;
      }

      if (!userData) {
        console.log('AdminGuard: User not found in database');
        setAdminValidation({
          isAdmin: false,
          isLoading: false,
          error: 'User not found in database'
        });
        return;
      }

      console.log('AdminGuard: User data retrieved', userData);

      // Check admin status - multiple validation methods
      const isAdminUser =
        userData.is_admin === true ||
        userData.admin_role === 'super_admin' ||
        userData.admin_role === 'admin' ||
        isHardcodedAdmin(userData.email);

      console.log('AdminGuard: Admin check', {
        is_admin: userData.is_admin,
        admin_role: userData.admin_role,
        isHardcoded: isHardcodedAdmin(userData.email),
        isAdminUser
      });

      if (!isAdminUser) {
        setAdminValidation({
          isAdmin: false,
          isLoading: false,
          error: 'Insufficient privileges - Admin access required'
        });
        return;
      }

      // Skip additional security checks for hardcoded admins
      if (isHardcodedAdmin(userData.email)) {
        console.log('AdminGuard: Skipping security checks for hardcoded admin');
      } else {
        // Additional security checks for non-hardcoded admins
        const securityChecks = await performSecurityChecks(userData);

        if (!securityChecks.passed) {
          setAdminValidation({
            isAdmin: false,
            isLoading: false,
            error: securityChecks.reason
          });
          return;
        }
      }

      // Log admin access
      await logAdminAccess(userData);

      setAdminValidation({ 
        isAdmin: true, 
        isLoading: false 
      });

    } catch (error) {
      console.error('Admin guard error:', error);
      setAdminValidation({ 
        isAdmin: false, 
        isLoading: false, 
        error: 'Security validation failed' 
      });
    }
  };

  const isHardcodedAdmin = (email: string): boolean => {
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ];
    return adminEmails.includes(email.toLowerCase());
  };

  const performSecurityChecks = async (userData: any) => {
    // Check account age (prevent newly created admin accounts)
    const accountAge = Date.now() - new Date(userData.created_at).getTime();
    const minAccountAge = 24 * 60 * 60 * 1000; // 24 hours

    if (accountAge < minAccountAge && !isHardcodedAdmin(userData.email)) {
      return { 
        passed: false, 
        reason: 'Account too new for admin access' 
      };
    }

    // Check if account is premium (additional security layer)
    if (!userData.is_premium && !isHardcodedAdmin(userData.email)) {
      return { 
        passed: false, 
        reason: 'Premium account required for admin access' 
      };
    }

    // Check for suspicious activity (could be expanded)
    const { data: recentLogins } = await supabase
      .from('login_logs')
      .select('*')
      .eq('user_id', userData.id)
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
      .order('created_at', { ascending: false });

    if (recentLogins && recentLogins.length > 10) {
      return { 
        passed: false, 
        reason: 'Too many recent login attempts - security lock' 
      };
    }

    return { passed: true };
  };

  const logAdminAccess = async (userData: any) => {
    try {
      // Log admin access to console for now (admin_access_logs table was removed)
      console.log('Admin access:', {
        admin_user_id: userData.id,
        admin_email: userData.email,
        access_path: location.pathname,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log admin access:', error);
      // Don't block access for logging failures
    }
  };

  // Loading state
  if (adminValidation.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-sm border max-w-md w-full mx-4">
          <div className="text-center">
            <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-pulse" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Validating Admin Access
            </h2>
            <p className="text-gray-600 mb-4">
              Performing security checks...
            </p>
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Not authenticated - redirect to admin login
  if (!isAuthenticated) {
    return <Navigate to="/admin" state={{ from: location }} replace />;
  }

  // Not admin - show access denied
  if (!adminValidation.isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-sm border max-w-md w-full mx-4">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Access Denied
            </h2>
            <p className="text-gray-600 mb-4">
              {adminValidation.error || 'You do not have permission to access the admin panel.'}
            </p>
            {/* Debug info */}
            <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded mb-4">
              <strong>Debug Info:</strong><br/>
              User: {user?.email || 'Not logged in'}<br/>
              Auth: {isAuthenticated ? 'Yes' : 'No'}<br/>
              Error: {adminValidation.error || 'No specific error'}
            </div>
            <div className="space-y-3">
              {/* Development bypass button */}
              {process.env.NODE_ENV === 'development' && user?.email === '<EMAIL>' && (
                <button
                  onClick={() => {
                    console.log('Manual admin bypass activated');
                    setAdminValidation({ isAdmin: true, isLoading: false });
                  }}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  🔓 Bypass Admin Check (Dev)
                </button>
              )}
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Dashboard
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Go to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Admin access granted
  return (
    <div>
      {/* Security Header */}
      <div className="bg-red-50 border-b border-red-200 px-4 py-2">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-red-800">
              Admin Panel - Secure Access
            </span>
          </div>
          <div className="text-xs text-red-600">
            User: {user?.email} | Session: Active
          </div>
        </div>
      </div>
      {children}
    </div>
  );
};

export default AdminGuard;
