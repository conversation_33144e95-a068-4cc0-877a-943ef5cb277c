import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, ShoppingCart, User, Menu, X, Crown, Wallet } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { useCartStore } from '../../stores/cartStore';
import { useProductStore } from '../../stores/productStore';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, isAuthenticated, logout } = useAuthStore();
  const { items, toggleCart, getTotalItems } = useCartStore();
  const { searchQuery, setSearchQuery } = useProductStore();
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleLogout = async () => {
    await logout();
    setIsUserMenuOpen(false);
    navigate('/');
  };

  return (
    <header className="bg-white shadow-lg border-b border-green-100 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 flex-shrink-0">
            <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden bg-white shadow-sm">
              <img
                src="/logo.png"
                alt="Start Juicce Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-900">Start Juicce</span>
          </Link>

          {/* Search Bar - Desktop */}
          <form onSubmit={handleSearch} className="flex-1 max-w-lg mx-4 lg:mx-8 hidden md:block">
            <div className="relative">
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </form>

          {/* Navigation Icons */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Cart */}
            <button
              onClick={toggleCart}
              className="relative p-2 text-gray-600 hover:text-green-600 transition-colors touch-target"
            >
              <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6" />
              {getTotalItems() > 0 && (
                <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
                  {getTotalItems()}
                </span>
              )}
            </button>

            {/* User Menu */}
            {isAuthenticated ? (
              <div className="relative hidden sm:block">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 p-2 text-gray-600 hover:text-green-600 transition-colors touch-target"
                >
                  <User className="h-5 w-5 sm:h-6 sm:w-6" />
                  {user?.is_premium && <Crown className="h-4 w-4 text-yellow-500" />}
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="font-medium text-gray-900 flex items-center">
                        {user?.full_name}
                        {user?.is_premium && <Crown className="h-4 w-4 text-yellow-500 ml-2" />}
                      </p>
                      <p className="text-sm text-gray-600">{user?.email}</p>
                      <div className="flex items-center mt-1 text-sm text-green-600">
                        <Wallet className="h-4 w-4 mr-1" />
                        ₹{user?.wallet_balance?.toFixed(2) || '0.00'}
                      </div>
                    </div>
                    <Link
                      to="/dashboard"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/orders"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Orders
                    </Link>
                    <Link
                      to="/wallet"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Wallet
                    </Link>
                    {user?.is_premium && (
                      <Link
                        to="/referrals"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        Referrals
                      </Link>
                    )}
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 touch-target"
                    >
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="hidden sm:flex items-center space-x-2">
                <Link
                  to="/login"
                  className="px-3 py-2 text-green-600 hover:text-green-700 font-medium text-sm touch-target"
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="px-3 py-2 bg-green-600 text-white rounded-full hover:bg-green-700 transition-colors text-sm touch-target"
                >
                  Sign Up
                </Link>
              </div>
            )}

            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="sm:hidden p-2 text-gray-600 hover:text-green-600 touch-target"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="sm:hidden py-4 border-t border-gray-200 bg-white">
            {/* Mobile Search */}
            <form onSubmit={handleSearch} className="mb-6 px-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-base"
                />
                <Search className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            </form>

            {/* Mobile Navigation */}
            <nav className="space-y-1 px-4">
              <Link
                to="/products"
                className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Products
              </Link>
              <Link
                to="/about"
                className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                About
              </Link>
              <Link
                to="/premium"
                className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                onClick={() => setIsMenuOpen(false)}
              >
                Premium
              </Link>
            </nav>

            {/* Mobile User Section */}
            {isAuthenticated ? (
              <div className="mt-6 pt-6 border-t border-gray-200 px-4">
                <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                  <p className="font-medium text-gray-900 flex items-center">
                    {user?.full_name}
                    {user?.is_premium && <Crown className="h-4 w-4 text-yellow-500 ml-2" />}
                  </p>
                  <p className="text-sm text-gray-600">{user?.email}</p>
                  <div className="flex items-center mt-2 text-sm text-green-600">
                    <Wallet className="h-4 w-4 mr-1" />
                    ₹{user?.wallet_balance?.toFixed(2) || '0.00'}
                  </div>
                </div>
                <nav className="space-y-1">
                  <Link
                    to="/dashboard"
                    className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <Link
                    to="/dashboard/orders"
                    className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Orders
                  </Link>
                  <Link
                    to="/dashboard/wallet"
                    className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Wallet
                  </Link>
                  {user?.is_premium && (
                    <Link
                      to="/dashboard/referrals"
                      className="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg font-medium touch-target"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Referrals
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg font-medium touch-target"
                  >
                    Logout
                  </button>
                </nav>
              </div>
            ) : (
              <div className="mt-6 pt-6 border-t border-gray-200 px-4 space-y-3">
                <Link
                  to="/login"
                  className="block w-full px-4 py-3 text-center text-green-600 border border-green-600 rounded-lg font-medium hover:bg-green-50 transition-colors touch-target"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="block w-full px-4 py-3 text-center bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors touch-target"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;