import React from 'react';
import { useLocation } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation();
  
  // Hide header and footer on dashboard and admin routes
  const isDashboardRoute = location.pathname.startsWith('/dashboard');
  const isAdminRoute = location.pathname.startsWith('/admin');
  const hideHeaderFooter = isDashboardRoute || isAdminRoute;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {!hideHeaderFooter && <Header />}
      
      <main className={hideHeaderFooter ? "flex-1" : "flex-1"}>
        {children}
      </main>

      {!hideHeaderFooter && <Footer />}
    </div>
  );
};

export default AppLayout;
