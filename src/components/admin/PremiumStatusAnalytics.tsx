import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';

interface PremiumAnalytics {
  totalUsers: number;
  premiumUsers: number;
  premiumPercentage: number;
  lifetimeAccessUsers: number;
  walletUnlockedUsers: number;
  totalReferralBonuses: number;
  premiumReferralBonuses: number;
  recentPremiumUpgrades: Array<{
    id: string;
    user_name: string;
    user_email: string;
    upgraded_at: string;
    referrer_name?: string;
    referrer_bonus?: number;
  }>;
}

interface User {
  id: string;
  full_name: string;
  email: string;
  is_premium: boolean;
  premium_lifetime_access: boolean;
  wallet_unlocked: boolean;
  referred_by_id?: string;
}

export const PremiumStatusAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<PremiumAnalytics>({
    totalUsers: 0,
    premiumUsers: 0,
    premiumPercentage: 0,
    lifetimeAccessUsers: 0,
    walletUnlockedUsers: 0,
    totalReferralBonuses: 0,
    premiumReferralBonuses: 0,
    recentPremiumUpgrades: []
  });
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      // Get user statistics
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, full_name, email, is_premium, premium_lifetime_access, wallet_unlocked, referred_by_id, created_at');

      if (usersError) throw usersError;

      // Get referral bonus statistics
      const { data: bonuses, error: bonusError } = await supabase
        .from('wallet_transactions')
        .select('amount, reference_type')
        .in('reference_type', ['referral', 'premium_referral']);

      if (bonusError) throw bonusError;

      // Admin audit logs table was removed during cleanup
      // For now, show empty audit logs
      const auditLogs: any[] = [];

      // Calculate analytics
      const totalUsers = users?.length || 0;
      const premiumUsers = users?.filter(u => u.is_premium).length || 0;
      const lifetimeAccessUsers = users?.filter(u => u.premium_lifetime_access).length || 0;
      const walletUnlockedUsers = users?.filter(u => u.wallet_unlocked).length || 0;

      const totalReferralBonuses = bonuses
        ?.filter(b => b.reference_type === 'referral')
        .reduce((sum, b) => sum + b.amount, 0) || 0;

      const premiumReferralBonuses = bonuses
        ?.filter(b => b.reference_type === 'premium_referral')
        .reduce((sum, b) => sum + b.amount, 0) || 0;

      const recentUpgrades = auditLogs?.map(log => ({
        id: log.id,
        user_name: log.users?.full_name || 'Unknown',
        user_email: log.users?.email || 'Unknown',
        upgraded_at: log.created_at,
        referrer_name: log.details?.referrer_name,
        referrer_bonus: log.details?.premium_bonus_added
      })) || [];

      setAnalytics({
        totalUsers,
        premiumUsers,
        premiumPercentage: totalUsers > 0 ? (premiumUsers / totalUsers) * 100 : 0,
        lifetimeAccessUsers,
        walletUnlockedUsers,
        totalReferralBonuses,
        premiumReferralBonuses,
        recentPremiumUpgrades: recentUpgrades
      });

    } catch (error) {
      console.error('Error loading analytics:', error);
      toast.error('Failed to load premium analytics');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkPremiumUpgrade = async (userIds: string[]) => {
    try {
      const results = [];
      
      for (const userId of userIds) {
        const { data: result, error } = await supabase.rpc('sync_premium_status_change', {
          p_user_id: userId,
          p_new_premium_status: true,
          p_new_lifetime_access: true,
          p_admin_user_id: null,
          p_change_reason: 'bulk_admin_upgrade'
        });

        if (error) {
          console.error(`Error upgrading user ${userId}:`, error);
          results.push({ userId, success: false, error: error.message });
        } else {
          results.push({ userId, success: true, result });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      toast.success(`Bulk upgrade completed: ${successCount} successful, ${failCount} failed`);
      loadAnalytics();
    } catch (error) {
      console.error('Error in bulk upgrade:', error);
      toast.error('Failed to perform bulk upgrade');
    }
  };

  const formatCurrency = (amount: number) => `₹${amount.toFixed(2)}`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-IN');
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Premium Status Analytics</h2>
        
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-blue-600 text-xl">👥</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-600">Total Users</p>
                <p className="text-2xl font-bold text-blue-900">{analytics.totalUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-green-600 text-xl">⭐</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-600">Premium Users</p>
                <p className="text-2xl font-bold text-green-900">
                  {analytics.premiumUsers} ({formatPercentage(analytics.premiumPercentage)})
                </p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-purple-600 text-xl">🔓</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-purple-600">Wallet Unlocked</p>
                <p className="text-2xl font-bold text-purple-900">{analytics.walletUnlockedUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <span className="text-yellow-600 text-xl">💰</span>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-600">Premium Bonuses</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {formatCurrency(analytics.premiumReferralBonuses)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bonus Comparison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Referral Bonuses</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Standard Referrals:</span>
                <span className="font-medium">{formatCurrency(analytics.totalReferralBonuses)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Premium Referrals:</span>
                <span className="font-medium text-green-600">
                  {formatCurrency(analytics.premiumReferralBonuses)}
                </span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="font-semibold">Total Paid:</span>
                <span className="font-bold">
                  {formatCurrency(analytics.totalReferralBonuses + analytics.premiumReferralBonuses)}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Premium Status Distribution</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Lifetime Access:</span>
                <span className="font-medium">{analytics.lifetimeAccessUsers}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Standard Users:</span>
                <span className="font-medium">{analytics.totalUsers - analytics.premiumUsers}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Conversion Rate:</span>
                <span className="font-medium text-blue-600">
                  {formatPercentage(analytics.premiumPercentage)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Premium Upgrades */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">Recent Premium Upgrades</h3>
          <p className="text-sm text-gray-600">Latest premium status changes and their referral impact</p>
        </div>

        <div className="divide-y">
          {analytics.recentPremiumUpgrades.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No recent premium upgrades
            </div>
          ) : (
            analytics.recentPremiumUpgrades.map((upgrade) => (
              <div key={upgrade.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">{upgrade.user_name}</p>
                    <p className="text-sm text-gray-500">{upgrade.user_email}</p>
                    <p className="text-xs text-gray-400">{formatDate(upgrade.upgraded_at)}</p>
                  </div>
                  <div className="text-right">
                    {upgrade.referrer_name && (
                      <div>
                        <p className="text-sm text-green-600">
                          Referrer: {upgrade.referrer_name}
                        </p>
                        {upgrade.referrer_bonus && (
                          <p className="text-sm font-medium text-green-700">
                            Bonus: {formatCurrency(upgrade.referrer_bonus)}
                          </p>
                        )}
                      </div>
                    )}
                    <span className="inline-flex px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                      ✅ Premium
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <button
          onClick={loadAnalytics}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          🔄 Refresh Analytics
        </button>
      </div>
    </div>
  );
};

export default PremiumStatusAnalytics;
