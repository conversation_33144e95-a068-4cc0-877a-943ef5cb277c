import React, { useState, useEffect } from 'react';
import { Shield, Database, Users, DollarSign, Activity, Lock, Settings, Terminal, Download, Upload } from 'lucide-react';
import { superAdminService } from '../../services/superAdminService';
import { useAuthStore } from '../../stores/authStore';
import toast from 'react-hot-toast';

interface SuperAdminStats {
  users: {
    total: number;
    active: number;
    premium: number;
    admin: number;
    kyc_pending: number;
    kyc_approved: number;
  };
  financial: {
    total_wallet_balance: number;
    total_transactions: number;
    total_transaction_volume: number;
    referral_bonuses_paid: number;
    pending_withdrawals: number;
  };
  products: {
    total: number;
    active: number;
    out_of_stock: number;
  };
  orders: {
    total: number;
    pending: number;
    completed: number;
    cancelled: number;
    total_revenue: number;
  };
  referrals: {
    total: number;
    completed: number;
    pending: number;
    premium_referrals: number;
  };
  system: {
    database_size: string;
    total_tables: number;
    admin_queries_today: number;
    last_backup: string;
  };
  performance: {
    avg_response_time: string;
    uptime: string;
    active_sessions: number;
  };
  security: {
    failed_logins_today: number;
    admin_actions_today: number;
    super_admins: number;
  };
}

const SuperAdminDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = useState<SuperAdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [customQuery, setCustomQuery] = useState('');
  const [queryResult, setQueryResult] = useState<any>(null);
  const [queryLoading, setQueryLoading] = useState(false);

  useEffect(() => {
    if (user?.id) {
      checkSuperAdminStatus();
      loadStats();
    }
  }, [user?.id]);

  const checkSuperAdminStatus = async () => {
    try {
      const isSuper = await superAdminService.isSuperAdmin(user?.id || '');
      setIsSuperAdmin(isSuper);
      if (!isSuper) {
        toast.error('Super Admin access required');
      }
    } catch (error) {
      console.error('Error checking super admin status:', error);
      setIsSuperAdmin(false);
    }
  };

  const loadStats = async () => {
    try {
      setLoading(true);
      const data = await superAdminService.getSuperAdminStats();
      setStats(data);
    } catch (error) {
      console.error('Error loading super admin stats:', error);
      toast.error('Failed to load super admin stats');
    } finally {
      setLoading(false);
    }
  };

  const executeCustomQuery = async () => {
    if (!customQuery.trim()) {
      toast.error('Please enter a query');
      return;
    }

    try {
      setQueryLoading(true);
      const result = await superAdminService.executeSuperAdminQuery(customQuery);
      setQueryResult(result);
      toast.success('Query executed successfully');
    } catch (error: any) {
      console.error('Query execution error:', error);
      toast.error(`Query failed: ${error.message}`);
      setQueryResult({ error: error.message });
    } finally {
      setQueryLoading(false);
    }
  };

  if (!isSuperAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-sm border max-w-md w-full mx-4">
          <div className="text-center">
            <Shield className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Super Admin Access Required
            </h2>
            <p className="text-gray-600">
              You need super admin privileges to access this dashboard.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Super Admin Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-600 to-red-800 rounded-lg p-6 mb-8 text-white">
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8" />
            <div>
              <h1 className="text-2xl font-bold">Super Admin Dashboard</h1>
              <p className="text-red-100">Full system access and control</p>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Users Stats */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <Users className="h-6 w-6 text-blue-600" />
                <h3 className="font-semibold">Users</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total:</span>
                  <span className="font-medium">{stats.users.total}</span>
                </div>
                <div className="flex justify-between">
                  <span>Premium:</span>
                  <span className="font-medium text-green-600">{stats.users.premium}</span>
                </div>
                <div className="flex justify-between">
                  <span>Admins:</span>
                  <span className="font-medium text-purple-600">{stats.users.admin}</span>
                </div>
                <div className="flex justify-between">
                  <span>KYC Pending:</span>
                  <span className="font-medium text-orange-600">{stats.users.kyc_pending}</span>
                </div>
              </div>
            </div>

            {/* Financial Stats */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <DollarSign className="h-6 w-6 text-green-600" />
                <h3 className="font-semibold">Financial</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Balance:</span>
                  <span className="font-medium">₹{stats.financial.total_wallet_balance}</span>
                </div>
                <div className="flex justify-between">
                  <span>Transactions:</span>
                  <span className="font-medium">{stats.financial.total_transactions}</span>
                </div>
                <div className="flex justify-between">
                  <span>Volume:</span>
                  <span className="font-medium">₹{stats.financial.total_transaction_volume}</span>
                </div>
                <div className="flex justify-between">
                  <span>Referral Bonuses:</span>
                  <span className="font-medium text-blue-600">₹{stats.financial.referral_bonuses_paid}</span>
                </div>
              </div>
            </div>

            {/* System Stats */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <Database className="h-6 w-6 text-purple-600" />
                <h3 className="font-semibold">System</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>DB Size:</span>
                  <span className="font-medium">{stats.system.database_size}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tables:</span>
                  <span className="font-medium">{stats.system.total_tables}</span>
                </div>
                <div className="flex justify-between">
                  <span>Queries Today:</span>
                  <span className="font-medium">{stats.system.admin_queries_today}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Sessions:</span>
                  <span className="font-medium text-green-600">{stats.performance.active_sessions}</span>
                </div>
              </div>
            </div>

            {/* Security Stats */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex items-center gap-3 mb-4">
                <Lock className="h-6 w-6 text-red-600" />
                <h3 className="font-semibold">Security</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Super Admins:</span>
                  <span className="font-medium text-red-600">{stats.security.super_admins}</span>
                </div>
                <div className="flex justify-between">
                  <span>Admin Actions:</span>
                  <span className="font-medium">{stats.security.admin_actions_today}</span>
                </div>
                <div className="flex justify-between">
                  <span>Uptime:</span>
                  <span className="font-medium text-green-600">{stats.performance.uptime}</span>
                </div>
                <div className="flex justify-between">
                  <span>Response Time:</span>
                  <span className="font-medium text-green-600">{stats.performance.avg_response_time}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Custom Query Interface */}
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center gap-3 mb-4">
            <Terminal className="h-6 w-6 text-gray-600" />
            <h3 className="font-semibold">Custom Database Query</h3>
          </div>
          
          <div className="space-y-4">
            <textarea
              value={customQuery}
              onChange={(e) => setCustomQuery(e.target.value)}
              placeholder="Enter your SQL query here... (e.g., SELECT * FROM users LIMIT 10)"
              className="w-full h-32 p-3 border border-gray-300 rounded-lg font-mono text-sm"
            />
            
            <div className="flex gap-3">
              <button
                onClick={executeCustomQuery}
                disabled={queryLoading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {queryLoading ? 'Executing...' : 'Execute Query'}
              </button>
              
              <button
                onClick={() => {
                  setCustomQuery('');
                  setQueryResult(null);
                }}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
              >
                Clear
              </button>
            </div>

            {queryResult && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Query Result:</h4>
                <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-96">
                  {JSON.stringify(queryResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
