import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';
import { Shield, AlertTriangle, Eye, Clock, User, Globe, Smartphone } from 'lucide-react';

interface AdminAccessLog {
  id: string;
  admin_email: string;
  access_path: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
}

interface SecurityAlert {
  id: string;
  type: 'suspicious_login' | 'multiple_attempts' | 'new_admin' | 'unusual_activity';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  created_at: string;
  resolved: boolean;
}

export const SecurityMonitoring: React.FC = () => {
  const [accessLogs, setAccessLogs] = useState<AdminAccessLog[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');

  useEffect(() => {
    loadSecurityData();
  }, [timeRange]);

  const loadSecurityData = async () => {
    try {
      setLoading(true);

      // Calculate time range
      const now = new Date();
      const timeRangeHours = timeRange === '1h' ? 1 : timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 720;
      const startTime = new Date(now.getTime() - timeRangeHours * 60 * 60 * 1000);

      // Admin access logs table was removed during cleanup
      // For now, show empty logs
      const logs: any[] = [];
      setAccessLogs(logs);

      // Generate security alerts based on patterns
      const alerts = await generateSecurityAlerts(logs || []);
      setSecurityAlerts(alerts);

    } catch (error) {
      console.error('Error loading security data:', error);
      toast.error('Failed to load security monitoring data');
    } finally {
      setLoading(false);
    }
  };

  const generateSecurityAlerts = async (logs: AdminAccessLog[]): Promise<SecurityAlert[]> => {
    const alerts: SecurityAlert[] = [];

    // Check for multiple rapid logins from same IP
    const ipCounts = logs.reduce((acc, log) => {
      const ip = log.ip_address || 'unknown';
      acc[ip] = (acc[ip] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(ipCounts).forEach(([ip, count]) => {
      if (count > 10 && ip !== 'unknown') {
        alerts.push({
          id: `rapid-${ip}`,
          type: 'multiple_attempts',
          message: `${count} admin access attempts from IP ${ip} in the last ${timeRange}`,
          severity: count > 20 ? 'critical' : count > 15 ? 'high' : 'medium',
          created_at: new Date().toISOString(),
          resolved: false
        });
      }
    });

    // Check for unusual access patterns
    const uniqueEmails = new Set(logs.map(log => log.admin_email));
    if (uniqueEmails.size > 3) {
      alerts.push({
        id: 'multiple-admins',
        type: 'unusual_activity',
        message: `${uniqueEmails.size} different admin accounts accessed the panel in the last ${timeRange}`,
        severity: 'medium',
        created_at: new Date().toISOString(),
        resolved: false
      });
    }

    // Check for new admin accounts
    const { data: recentAdmins } = await supabase
      .from('users')
      .select('email, created_at')
      .eq('is_admin', true)
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

    if (recentAdmins && recentAdmins.length > 0) {
      recentAdmins.forEach(admin => {
        alerts.push({
          id: `new-admin-${admin.email}`,
          type: 'new_admin',
          message: `New admin account created: ${admin.email}`,
          severity: 'high',
          created_at: admin.created_at,
          resolved: false
        });
      });
    }

    return alerts;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-800 bg-red-100 border-red-200';
      case 'high': return 'text-orange-800 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-800 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-blue-800 bg-blue-100 border-blue-200';
      default: return 'text-gray-800 bg-gray-100 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN');
  };

  const getDeviceInfo = (userAgent: string) => {
    if (!userAgent) return { device: 'Unknown', browser: 'Unknown' };
    
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
    const isTablet = /iPad|Tablet/.test(userAgent);
    const device = isMobile ? (isTablet ? 'Tablet' : 'Mobile') : 'Desktop';
    
    let browser = 'Unknown';
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';
    
    return { device, browser };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading security data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 to-orange-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Security Monitoring</h2>
            <p className="opacity-90">Monitor admin access and security alerts</p>
          </div>
          <Shield className="h-12 w-12 opacity-80" />
        </div>
      </div>

      {/* Time Range Selector */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Time Range</h3>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {/* Security Alerts */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h3 className="text-lg font-semibold text-gray-900">Security Alerts</h3>
            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
              {securityAlerts.filter(a => !a.resolved).length}
            </span>
          </div>
        </div>

        <div className="divide-y">
          {securityAlerts.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Shield className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              <p>No security alerts in the selected time range</p>
            </div>
          ) : (
            securityAlerts.map((alert) => (
              <div key={alert.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg border ${getSeverityColor(alert.severity)}`}>
                      <AlertTriangle className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{alert.message}</p>
                      <p className="text-sm text-gray-500">{formatDate(alert.created_at)}</p>
                      <span className={`inline-block mt-1 px-2 py-1 text-xs rounded-full ${getSeverityColor(alert.severity)}`}>
                        {alert.severity.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  {!alert.resolved && (
                    <button className="text-sm text-blue-600 hover:text-blue-800">
                      Mark Resolved
                    </button>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Admin Access Logs */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Admin Access Logs</h3>
            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
              {accessLogs.length}
            </span>
          </div>
        </div>

        <div className="divide-y max-h-96 overflow-y-auto">
          {accessLogs.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Clock className="h-12 w-12 text-gray-300 mx-auto mb-2" />
              <p>No admin access logs in the selected time range</p>
            </div>
          ) : (
            accessLogs.map((log) => {
              const deviceInfo = getDeviceInfo(log.user_agent);
              return (
                <div key={log.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{log.admin_email}</p>
                        <p className="text-sm text-gray-500">{log.access_path}</p>
                        <div className="flex items-center gap-4 mt-1">
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Globe className="h-3 w-3" />
                            {log.ip_address || 'Unknown IP'}
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Smartphone className="h-3 w-3" />
                            {deviceInfo.device} • {deviceInfo.browser}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-900">{formatDate(log.created_at)}</p>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Security Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Access Attempts</p>
              <p className="text-2xl font-bold text-gray-900">{accessLogs.length}</p>
            </div>
            <Eye className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Unique Admin Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {new Set(accessLogs.map(log => log.admin_email)).size}
              </p>
            </div>
            <User className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Security Alerts</p>
              <p className="text-2xl font-bold text-red-600">
                {securityAlerts.filter(a => !a.resolved).length}
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityMonitoring;
