import React, { useState } from 'react';
import { 
  Users, 
  Crown, 
  Wallet, 
  ChevronDown, 
  ChevronRight, 
  User,
  TrendingUp,
  DollarSign,
  Calendar,
  Award,
  Network
} from 'lucide-react';
import { ReferralTreeNode, ReferralTreeStats } from '../../services/referralTreeService';

interface ReferralTreeVisualizationProps {
  tree: ReferralTreeNode;
  stats: ReferralTreeStats;
  onUserSelect?: (userId: string) => void;
}

const ReferralTreeVisualization: React.FC<ReferralTreeVisualizationProps> = ({
  tree,
  stats,
  onUserSelect
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set([tree.id]));

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const formatCurrency = (amount: number) => `₹${amount.toFixed(2)}`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-IN');

  const getNodeColor = (node: ReferralTreeNode) => {
    if (node.level === 0) return 'bg-purple-100 border-purple-300 text-purple-900';
    if (node.is_premium) return 'bg-green-100 border-green-300 text-green-900';
    return 'bg-blue-100 border-blue-300 text-blue-900';
  };

  const getNodeIcon = (node: ReferralTreeNode) => {
    if (node.level === 0) return <Crown className="w-4 h-4" />;
    if (node.is_premium) return <Award className="w-4 h-4" />;
    return <User className="w-4 h-4" />;
  };

  const renderTreeNode = (node: ReferralTreeNode, isLast: boolean = false) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;
    const indentLevel = node.level * 24;

    return (
      <div key={node.id} className="relative">
        {/* Connection Lines */}
        {node.level > 0 && (
          <>
            {/* Vertical line from parent */}
            <div 
              className="absolute border-l-2 border-gray-300"
              style={{
                left: `${indentLevel - 12}px`,
                top: '-12px',
                height: '24px'
              }}
            />
            {/* Horizontal line to node */}
            <div 
              className="absolute border-t-2 border-gray-300"
              style={{
                left: `${indentLevel - 12}px`,
                top: '12px',
                width: '12px'
              }}
            />
          </>
        )}

        {/* Node */}
        <div 
          className="flex items-center mb-2 relative"
          style={{ marginLeft: `${indentLevel}px` }}
        >
          {/* Expand/Collapse Button */}
          {hasChildren && (
            <button
              onClick={() => toggleNode(node.id)}
              className="mr-2 p-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              )}
            </button>
          )}
          
          {!hasChildren && <div className="w-6 mr-2" />}

          {/* User Card */}
          <div 
            className={`flex-1 p-3 rounded-lg border-2 cursor-pointer hover:shadow-md transition-all ${getNodeColor(node)}`}
            onClick={() => onUserSelect?.(node.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getNodeIcon(node)}
                <div>
                  <div className="font-semibold text-sm">{node.full_name}</div>
                  <div className="text-xs opacity-75">{node.email}</div>
                  {node.referral_code && (
                    <div className="text-xs font-mono bg-white/50 px-2 py-1 rounded mt-1">
                      {node.referral_code}
                    </div>
                  )}
                </div>
              </div>

              <div className="text-right text-xs space-y-1">
                <div className="flex items-center space-x-1">
                  <Users className="w-3 h-3" />
                  <span>{node.direct_referrals}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Wallet className="w-3 h-3" />
                  <span>{formatCurrency(node.wallet_balance)}</span>
                </div>
                {node.ewallet_unlocked && (
                  <div className="text-green-600 font-semibold">🔓 Unlocked</div>
                )}
              </div>
            </div>

            {/* Additional Info */}
            <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="opacity-75">Level:</span> {node.level + 1}
              </div>
              <div>
                <span className="opacity-75">Network:</span> {node.total_network_size + 1}
              </div>
              <div>
                <span className="opacity-75">Earnings:</span> {formatCurrency(node.total_earnings)}
              </div>
              <div>
                <span className="opacity-75">Joined:</span> {formatDate(node.created_at)}
              </div>
            </div>

            {node.referral_order && (
              <div className="mt-1 text-xs">
                <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded">
                  Referral #{node.referral_order}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="relative">
            {node.children.map((child, index) => (
              <div key={child.id}>
                {renderTreeNode(child, index === node.children.length - 1)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5 text-blue-600" />
            <div>
              <div className="text-sm text-gray-600">Total Users</div>
              <div className="text-xl font-bold">{stats.total_users}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center space-x-2">
            <Crown className="w-5 h-5 text-green-600" />
            <div>
              <div className="text-sm text-gray-600">Premium Users</div>
              <div className="text-xl font-bold">{stats.total_premium_users}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5 text-purple-600" />
            <div>
              <div className="text-sm text-gray-600">Total Earnings</div>
              <div className="text-xl font-bold">{formatCurrency(stats.total_earnings)}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-orange-600" />
            <div>
              <div className="text-sm text-gray-600">Max Depth</div>
              <div className="text-xl font-bold">{stats.max_depth}</div>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center space-x-2">
            <Network className="w-5 h-5 text-indigo-600" />
            <div>
              <div className="text-sm text-gray-600">Network Value</div>
              <div className="text-xl font-bold">{formatCurrency(stats.total_network_value)}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tree Visualization */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Referral Tree</h3>
          <div className="flex space-x-2 text-xs">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-purple-200 rounded"></div>
              <span>Root User</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-green-200 rounded"></div>
              <span>Premium</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-blue-200 rounded"></div>
              <span>Standard</span>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="min-w-full">
            {renderTreeNode(tree)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralTreeVisualization;
