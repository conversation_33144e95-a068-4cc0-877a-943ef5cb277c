import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';

interface ExportOptions {
  includeUsers: boolean;
  includeTransactions: boolean;
  includeReferrals: boolean;
  includeLoginLogs: boolean;
  dateRange: {
    start: string;
    end: string;
  };
  userFilters: {
    premiumOnly: boolean;
    activeOnly: boolean;
  };
}

export const PDFExport: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [options, setOptions] = useState<ExportOptions>({
    includeUsers: true,
    includeTransactions: false,
    includeReferrals: false,
    includeLoginLogs: false,
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
      end: new Date().toISOString().split('T')[0] // today
    },
    userFilters: {
      premiumOnly: false,
      activeOnly: false
    }
  });

  const generatePDF = async () => {
    try {
      setExporting(true);
      
      // Collect data based on options
      const reportData: any = {
        generatedAt: new Date().toISOString(),
        dateRange: options.dateRange,
        sections: []
      };

      // Users data
      if (options.includeUsers) {
        let query = supabase
          .from('users')
          .select('*')
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end + 'T23:59:59');

        if (options.userFilters.premiumOnly) {
          query = query.eq('is_premium', true);
        }

        const { data: users, error: usersError } = await query;
        if (usersError) throw usersError;

        reportData.sections.push({
          title: 'Users Report',
          data: users,
          count: users?.length || 0
        });
      }

      // Transactions data
      if (options.includeTransactions) {
        const { data: transactions, error: transError } = await supabase
          .from('wallet_transactions')
          .select(`
            *,
            user:users(full_name, email)
          `)
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end + 'T23:59:59')
          .order('created_at', { ascending: false });

        if (transError) throw transError;

        reportData.sections.push({
          title: 'Wallet Transactions',
          data: transactions,
          count: transactions?.length || 0
        });
      }

      // Referrals data
      if (options.includeReferrals) {
        const { data: referrals, error: refError } = await supabase
          .from('referrals')
          .select(`
            *,
            referrer:users!referrals_referrer_id_fkey(full_name, email),
            referred:users!referrals_referred_user_id_fkey(full_name, email)
          `)
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end + 'T23:59:59')
          .order('created_at', { ascending: false });

        if (refError) throw refError;

        reportData.sections.push({
          title: 'Referrals Report',
          data: referrals,
          count: referrals?.length || 0
        });
      }

      // Login logs data
      if (options.includeLoginLogs) {
        const { data: logs, error: logsError } = await supabase
          .from('login_logs')
          .select(`
            *,
            user:users(full_name, email)
          `)
          .gte('login_time', options.dateRange.start)
          .lte('login_time', options.dateRange.end + 'T23:59:59')
          .order('login_time', { ascending: false });

        if (logsError && logsError.code !== '42P01') { // Ignore table doesn't exist
          throw logsError;
        }

        reportData.sections.push({
          title: 'Login Logs',
          data: logs || [],
          count: logs?.length || 0
        });
      }

      // Generate HTML for PDF
      const htmlContent = generateHTMLReport(reportData);
      
      // Create and download PDF
      await downloadPDF(htmlContent, `admin-report-${new Date().toISOString().split('T')[0]}.pdf`);
      
      toast.success('PDF report generated successfully!');
      setShowModal(false);
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF report');
    } finally {
      setExporting(false);
    }
  };

  const generateHTMLReport = (data: any) => {
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleString('en-IN');
    };

    const formatCurrency = (amount: number) => {
      return `₹${amount.toFixed(2)}`;
    };

    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Admin Report - Start Juicce</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            color: #333;
            line-height: 1.6;
          }
          .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 3px solid #3B82F6;
            padding-bottom: 20px;
          }
          .header h1 { 
            color: #3B82F6; 
            margin: 0;
            font-size: 28px;
          }
          .header p { 
            color: #666; 
            margin: 5px 0;
          }
          .section { 
            margin-bottom: 40px; 
            page-break-inside: avoid;
          }
          .section h2 { 
            color: #1F2937; 
            border-left: 4px solid #10B981;
            padding-left: 15px;
            background: #F3F4F6;
            padding: 10px 15px;
            margin: 0 0 20px 0;
          }
          table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          th, td { 
            border: 1px solid #E5E7EB; 
            padding: 12px 8px; 
            text-align: left;
            font-size: 12px;
          }
          th { 
            background-color: #F9FAFB; 
            font-weight: bold;
            color: #374151;
          }
          tr:nth-child(even) { 
            background-color: #F9FAFB; 
          }
          .premium { 
            color: #10B981; 
            font-weight: bold;
          }
          .standard { 
            color: #6B7280; 
          }
          .credit { 
            color: #10B981; 
          }
          .debit { 
            color: #EF4444; 
          }
          .summary { 
            background: #EFF6FF; 
            padding: 15px; 
            border-radius: 8px;
            margin-bottom: 20px;
          }
          .summary h3 { 
            margin: 0 0 10px 0; 
            color: #1E40AF;
          }
          .footer { 
            text-align: center; 
            margin-top: 40px; 
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            color: #6B7280;
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🚀 Start Juicce - Admin Report</h1>
          <p>Generated on: ${formatDate(data.generatedAt)}</p>
          <p>Date Range: ${data.dateRange.start} to ${data.dateRange.end}</p>
        </div>
    `;

    // Add sections
    data.sections.forEach((section: any) => {
      html += `
        <div class="section">
          <h2>${section.title} (${section.count} records)</h2>
      `;

      if (section.title === 'Users Report') {
        const users = section.data;
        const premiumCount = users.filter((u: any) => u.is_premium).length;
        const totalWallet = users.reduce((sum: number, u: any) => sum + (u.wallet_balance || 0), 0);

        html += `
          <div class="summary">
            <h3>Summary</h3>
            <p>Total Users: ${users.length} | Premium Users: ${premiumCount} | Total Wallet Balance: ${formatCurrency(totalWallet)}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Status</th>
                <th>Wallet Balance</th>
                <th>Referral Code</th>
                <th>Joined Date</th>
              </tr>
            </thead>
            <tbody>
        `;

        users.forEach((user: any) => {
          html += `
            <tr>
              <td>${user.full_name}</td>
              <td>${user.email}</td>
              <td class="${user.is_premium ? 'premium' : 'standard'}">
                ${user.is_premium ? '✅ Premium' : '⭐ Standard'}
              </td>
              <td>${formatCurrency(user.wallet_balance || 0)}</td>
              <td>${user.referral_code || 'N/A'}</td>
              <td>${formatDate(user.created_at)}</td>
            </tr>
          `;
        });

        html += '</tbody></table>';
      }

      if (section.title === 'Wallet Transactions') {
        const transactions = section.data;
        const totalCredits = transactions.filter((t: any) => t.type === 'credit').reduce((sum: number, t: any) => sum + t.amount, 0);
        const totalDebits = transactions.filter((t: any) => t.type === 'debit').reduce((sum: number, t: any) => sum + t.amount, 0);

        html += `
          <div class="summary">
            <h3>Summary</h3>
            <p>Total Credits: ${formatCurrency(totalCredits)} | Total Debits: ${formatCurrency(totalDebits)} | Net: ${formatCurrency(totalCredits - totalDebits)}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>User</th>
                <th>Type</th>
                <th>Amount</th>
                <th>Description</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
        `;

        transactions.forEach((transaction: any) => {
          html += `
            <tr>
              <td>${transaction.user?.full_name || 'Unknown'}</td>
              <td class="${transaction.type}">${transaction.type.toUpperCase()}</td>
              <td class="${transaction.type}">${formatCurrency(transaction.amount)}</td>
              <td>${transaction.description}</td>
              <td>${formatDate(transaction.created_at)}</td>
            </tr>
          `;
        });

        html += '</tbody></table>';
      }

      if (section.title === 'Referrals Report') {
        const referrals = section.data;
        const totalBonuses = referrals.reduce((sum: number, r: any) => sum + (r.bonus_amount || 0), 0);

        html += `
          <div class="summary">
            <h3>Summary</h3>
            <p>Total Referrals: ${referrals.length} | Total Bonuses Paid: ${formatCurrency(totalBonuses)}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Referrer</th>
                <th>Referred User</th>
                <th>Bonus Amount</th>
                <th>Status</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
        `;

        referrals.forEach((referral: any) => {
          html += `
            <tr>
              <td>${referral.referrer?.full_name || 'Unknown'}</td>
              <td>${referral.referred?.full_name || 'Unknown'}</td>
              <td class="credit">${formatCurrency(referral.bonus_amount || 0)}</td>
              <td>${referral.status}</td>
              <td>${formatDate(referral.created_at)}</td>
            </tr>
          `;
        });

        html += '</tbody></table>';
      }

      if (section.title === 'Login Logs') {
        html += `
          <table>
            <thead>
              <tr>
                <th>User</th>
                <th>Login Time</th>
                <th>IP Address</th>
                <th>User Agent</th>
              </tr>
            </thead>
            <tbody>
        `;

        section.data.forEach((log: any) => {
          html += `
            <tr>
              <td>${log.user?.full_name || 'Unknown'}</td>
              <td>${formatDate(log.login_time)}</td>
              <td>${log.ip_address || 'N/A'}</td>
              <td>${log.user_agent ? log.user_agent.substring(0, 50) + '...' : 'N/A'}</td>
            </tr>
          `;
        });

        html += '</tbody></table>';
      }

      html += '</div>';
    });

    html += `
        <div class="footer">
          <p>This report was generated automatically by Start Juicce Admin Panel</p>
          <p>© ${new Date().getFullYear()} Start Juicce. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;

    return html;
  };

  const downloadPDF = async (htmlContent: string, filename: string) => {
    // Create a blob with the HTML content
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    // Create a temporary link and click it to download
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace('.pdf', '.html'); // Download as HTML for now
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
      >
        <span>📄</span>
        <span>Export PDF Report</span>
      </button>

      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 className="text-xl font-bold mb-4">📊 Export PDF Report</h3>
            
            <div className="space-y-4">
              {/* Data Sections */}
              <div>
                <h4 className="font-semibold mb-2">Include Sections:</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={options.includeUsers}
                      onChange={(e) => setOptions({...options, includeUsers: e.target.checked})}
                      className="mr-2"
                    />
                    Users Report
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={options.includeTransactions}
                      onChange={(e) => setOptions({...options, includeTransactions: e.target.checked})}
                      className="mr-2"
                    />
                    Wallet Transactions
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={options.includeReferrals}
                      onChange={(e) => setOptions({...options, includeReferrals: e.target.checked})}
                      className="mr-2"
                    />
                    Referrals Report
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={options.includeLoginLogs}
                      onChange={(e) => setOptions({...options, includeLoginLogs: e.target.checked})}
                      className="mr-2"
                    />
                    Login Logs
                  </label>
                </div>
              </div>

              {/* Date Range */}
              <div>
                <h4 className="font-semibold mb-2">Date Range:</h4>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-sm text-gray-600">Start Date</label>
                    <input
                      type="date"
                      value={options.dateRange.start}
                      onChange={(e) => setOptions({
                        ...options,
                        dateRange: {...options.dateRange, start: e.target.value}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-600">End Date</label>
                    <input
                      type="date"
                      value={options.dateRange.end}
                      onChange={(e) => setOptions({
                        ...options,
                        dateRange: {...options.dateRange, end: e.target.value}
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                    />
                  </div>
                </div>
              </div>

              {/* User Filters */}
              {options.includeUsers && (
                <div>
                  <h4 className="font-semibold mb-2">User Filters:</h4>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={options.userFilters.premiumOnly}
                        onChange={(e) => setOptions({
                          ...options,
                          userFilters: {...options.userFilters, premiumOnly: e.target.checked}
                        })}
                        className="mr-2"
                      />
                      Premium Users Only
                    </label>
                  </div>
                </div>
              )}
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={exporting}
              >
                Cancel
              </button>
              <button
                onClick={generatePDF}
                disabled={exporting}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {exporting ? 'Generating...' : 'Generate PDF'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default PDFExport;
