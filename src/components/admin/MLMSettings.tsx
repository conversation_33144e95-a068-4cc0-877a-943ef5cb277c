import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface MLMSetting {
  id: string;
  setting_key: string;
  setting_value: string;
  description: string;
  setting_type: string;
}

const MLMSettings: React.FC = () => {
  const [settings, setSettings] = useState<MLMSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    loadMLMSettings();
  }, []);

  const loadMLMSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('admin_settings')
        .select('*')
        .in('setting_key', [
          'mlm_system_enabled',
          'mlm_bonus_amount'
        ])
        .order('setting_key');

      if (error) throw error;

      setSettings(data || []);
      
      // Initialize edit values
      const initialValues: { [key: string]: string } = {};
      data?.forEach(setting => {
        initialValues[setting.setting_key] = setting.setting_value;
      });
      setEditValues(initialValues);

    } catch (error) {
      console.error('Error loading MLM settings:', error);
      toast.error('Failed to load MLM settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (settingKey: string, newValue: string) => {
    setSaving(settingKey);
    try {
      const { error } = await supabase
        .from('admin_settings')
        .update({
          setting_value: newValue,
          updated_at: new Date().toISOString()
        })
        .eq('setting_key', settingKey);

      if (error) throw error;

      // Update local state
      setSettings(prev => prev.map(setting => 
        setting.setting_key === settingKey 
          ? { ...setting, setting_value: newValue }
          : setting
      ));

      toast.success(`${getSettingDisplayName(settingKey)} updated successfully!`);
    } catch (error) {
      console.error('Error updating setting:', error);
      toast.error('Failed to update setting');
      // Reset edit value on error
      setEditValues(prev => ({
        ...prev,
        [settingKey]: settings.find(s => s.setting_key === settingKey)?.setting_value || ''
      }));
    } finally {
      setSaving(null);
    }
  };

  const handleInputChange = (settingKey: string, value: string) => {
    setEditValues(prev => ({
      ...prev,
      [settingKey]: value
    }));
  };

  const handleSave = (settingKey: string) => {
    const newValue = editValues[settingKey];
    const currentValue = settings.find(s => s.setting_key === settingKey)?.setting_value;
    
    if (newValue !== currentValue) {
      updateSetting(settingKey, newValue);
    }
  };

  const getSettingDisplayName = (key: string) => {
    switch (key) {
      case 'mlm_system_enabled':
        return 'MLM System Status';
      case 'mlm_bonus_amount':
        return 'MLM Referral Bonus Amount';
      default:
        return key;
    }
  };

  const getSettingIcon = (key: string) => {
    switch (key) {
      case 'mlm_system_enabled':
        return '🎯';
      case 'mlm_bonus_amount':
        return '💰';
      default:
        return '⚙️';
    }
  };

  const formatCurrency = (value: string) => {
    const num = parseFloat(value);
    return isNaN(num) ? value : `₹${num.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">🎯 MLM System Settings</h2>
        <p className="text-blue-100">
          Configure your MLM referral system settings and bonus amounts
        </p>
      </div>

      {/* Current MLM Logic Info */}
      <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 mb-3">📋 Current MLM Logic</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center p-3 bg-green-100 rounded">
            <div className="font-bold text-green-800">1st Referral</div>
            <div className="text-green-600">→ Direct Referrer</div>
          </div>
          <div className="text-center p-3 bg-green-100 rounded">
            <div className="font-bold text-green-800">2nd Referral</div>
            <div className="text-green-600">→ Direct Referrer</div>
          </div>
          <div className="text-center p-3 bg-blue-100 rounded">
            <div className="font-bold text-blue-800">3rd Referral</div>
            <div className="text-blue-600">→ Grandparent</div>
          </div>
          <div className="text-center p-3 bg-green-100 rounded">
            <div className="font-bold text-green-800">4th+ Referrals</div>
            <div className="text-green-600">→ Direct Referrer</div>
          </div>
        </div>
      </div>

      {/* Settings Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {settings.map((setting) => (
          <div key={setting.id} className="bg-white p-6 rounded-lg shadow-md border">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">{getSettingIcon(setting.setting_key)}</span>
              <div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {getSettingDisplayName(setting.setting_key)}
                </h3>
                <p className="text-sm text-gray-600">{setting.description}</p>
              </div>
            </div>

            <div className="space-y-3">
              {setting.setting_key === 'mlm_system_enabled' ? (
                <div className="flex items-center gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={setting.setting_key}
                      value="true"
                      checked={editValues[setting.setting_key] === 'true'}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      className="mr-2"
                    />
                    <span className="text-green-600 font-medium">Enabled</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={setting.setting_key}
                      value="false"
                      checked={editValues[setting.setting_key] === 'false'}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      className="mr-2"
                    />
                    <span className="text-red-600 font-medium">Disabled</span>
                  </label>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <span className="text-gray-600">₹</span>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={editValues[setting.setting_key] || ''}
                    onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter amount"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Current: {setting.setting_key === 'mlm_bonus_amount' 
                    ? formatCurrency(setting.setting_value)
                    : setting.setting_value === 'true' ? '✅ Enabled' : '❌ Disabled'
                  }
                </div>
                <button
                  onClick={() => handleSave(setting.setting_key)}
                  disabled={
                    saving === setting.setting_key || 
                    editValues[setting.setting_key] === setting.setting_value
                  }
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving === setting.setting_key ? 'Saving...' : 'Save'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Impact Information */}
      <div className="bg-blue-50 border border-blue-200 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">💡 Impact of Changes</h3>
        <div className="space-y-2 text-blue-700 text-sm">
          <p>
            <strong>MLM Bonus Amount:</strong> This amount will be distributed for each referral according to the MLM logic above.
          </p>
          <p>
            <strong>System Status:</strong> When disabled, no MLM bonuses will be distributed, but referral tracking continues.
          </p>
          <p>
            <strong>Effect:</strong> Changes apply immediately to all new referrals. Existing referrals are not affected.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MLMSettings;
