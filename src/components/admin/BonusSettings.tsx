import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';

interface BonusSetting {
  setting_key: string;
  setting_value: string;
  description: string;
  updated_at: string;
}

export const BonusSettings: React.FC = () => {
  const [settings, setSettings] = useState<BonusSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editedValues, setEditedValues] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('admin_settings')
        .select('*')
        .in('setting_key', [
          'referral_bonus_amount',
          'premium_referral_bonus_amount',
          'mlm_bonus_amount'
        ])
        .order('setting_key');

      if (error) throw error;

      setSettings(data || []);
      
      // Initialize edited values
      const initialValues: { [key: string]: string } = {};
      data?.forEach(setting => {
        initialValues[setting.setting_key] = setting.setting_value;
      });
      setEditedValues(initialValues);

    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load bonus settings');
    } finally {
      setLoading(false);
    }
  };

  const handleValueChange = (key: string, value: string) => {
    setEditedValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);

      // Validate all values are positive numbers
      for (const [key, value] of Object.entries(editedValues)) {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue < 0) {
          throw new Error(`Invalid value for ${key}: must be a positive number`);
        }
      }

      // Update each setting
      for (const [key, value] of Object.entries(editedValues)) {
        const { error } = await supabase
          .from('admin_settings')
          .update({
            setting_value: value,
            updated_at: new Date().toISOString()
          })
          .eq('setting_key', key);

        if (error) throw error;
      }

      toast.success('Bonus settings updated successfully!');
      loadSettings(); // Reload to get updated timestamps

    } catch (error: any) {
      console.error('Error saving settings:', error);
      toast.error(error.message || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const hasChanges = () => {
    return settings.some(setting => 
      editedValues[setting.setting_key] !== setting.setting_value
    );
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    return isNaN(num) ? '₹0.00' : `₹${num.toFixed(2)}`;
  };

  const getSettingDisplayName = (key: string) => {
    switch (key) {
      case 'referral_bonus_amount':
        return 'Standard Referral Bonus';
      case 'premium_referral_bonus_amount':
        return 'Premium Referral Bonus';
      case 'mlm_bonus_amount':
        return 'MLM Referral Bonus';
      case 'wallet_unlock_bonus_amount':
        return 'E-wallet Unlock Bonus';
      default:
        return key;
    }
  };

  const getSettingIcon = (key: string) => {
    switch (key) {
      case 'referral_bonus_amount':
        return '👥';
      case 'premium_referral_bonus_amount':
        return '⭐';
      case 'mlm_bonus_amount':
        return '🎯';
      case 'wallet_unlock_bonus_amount':
        return '🔓';
      default:
        return '⚙️';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading bonus settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Bonus Settings</h2>
        <p className="text-gray-600">
          Configure referral and wallet bonus amounts. Changes apply immediately to new transactions.
        </p>
      </div>

      {/* Settings Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {settings.map((setting) => (
          <div key={setting.setting_key} className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl mr-3">{getSettingIcon(setting.setting_key)}</span>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {getSettingDisplayName(setting.setting_key)}
                </h3>
                <p className="text-sm text-gray-600">{setting.description}</p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount (₹)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={editedValues[setting.setting_key] || ''}
                  onChange={(e) => handleValueChange(setting.setting_key, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.00"
                />
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Current Value:</span>
                  <span className="font-medium text-gray-900">
                    {formatCurrency(setting.setting_value)}
                  </span>
                </div>
                <div className="flex justify-between items-center mt-1">
                  <span className="text-sm text-gray-600">New Value:</span>
                  <span className="font-medium text-blue-600">
                    {formatCurrency(editedValues[setting.setting_key] || '0')}
                  </span>
                </div>
              </div>

              <div className="text-xs text-gray-500">
                Last updated: {new Date(setting.updated_at).toLocaleString('en-IN')}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Save Changes</h3>
            <p className="text-sm text-gray-600">
              {hasChanges() 
                ? 'You have unsaved changes. Click save to apply them.'
                : 'No changes to save.'
              }
            </p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={loadSettings}
              disabled={saving}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
            >
              🔄 Reset
            </button>
            
            <button
              onClick={handleSaveSettings}
              disabled={!hasChanges() || saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? 'Saving...' : '💾 Save Settings'}
            </button>
          </div>
        </div>
      </div>

      {/* Impact Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 How These Settings Work</h3>
        <div className="space-y-2 text-blue-800">
          <p>
            <strong>Standard Referral Bonus:</strong> Amount given to referrer when someone joins using their code
          </p>
          <p>
            <strong>Premium Referral Bonus:</strong> Additional amount when a referred user upgrades to premium
          </p>
          <p>
            <strong>MLM Referral Bonus:</strong> Fixed amount for each referral in the MLM system (1st, 2nd, 4th+ to referrer; 3rd to grandparent)
          </p>
          <p>
            <strong>E-wallet Unlock Bonus:</strong> One-time bonus when user unlocks e-wallet (after 3 premium referrals)
          </p>
        </div>
        
        <div className="mt-4 p-3 bg-blue-100 rounded-lg">
          <p className="text-sm text-blue-700">
            <strong>Note:</strong> Changes apply immediately to new transactions. Existing transactions are not affected.
          </p>
        </div>
      </div>
    </div>
  );
};

export default BonusSettings;
