import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';
import { 
  Package, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  Edit,
  Search,
  Filter,
  Plus,
  Minus,
  BarChart3,
  RefreshCw
} from 'lucide-react';

interface Product {
  id: string;
  name: string;
  sku: string;
  stock_quantity: number;
  low_stock_threshold: number;
  price: number;
  image_url: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface InventoryTransaction {
  id: string;
  transaction_type: string;
  quantity_change: number;
  previous_quantity: number;
  new_quantity: number;
  notes: string;
  created_at: string;
  product: {
    name: string;
    sku: string;
  };
}

export const InventoryManagement: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [lowStockProducts, setLowStockProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stockFilter, setStockFilter] = useState('all');
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'subtract'>('add');
  const [adjustmentQuantity, setAdjustmentQuantity] = useState('');
  const [adjustmentNotes, setAdjustmentNotes] = useState('');

  useEffect(() => {
    loadInventoryData();
  }, []);

  const loadInventoryData = async () => {
    try {
      setLoading(true);
      
      // Load products
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('*')
        .order('name');

      if (productsError) throw productsError;

      // Load recent inventory transactions with product details using JOIN
      let transactionsWithProducts = [];
      try {
        const { data: transactionsData, error: transactionsError } = await supabase
          .from('inventory_transactions')
          .select(`
            *,
            products!inner(
              name,
              sku
            )
          `)
          .order('created_at', { ascending: false })
          .limit(50);

        if (transactionsError) {
          console.warn('Transactions error:', transactionsError);
          // Try fallback method without join
          const { data: fallbackData } = await supabase
            .from('inventory_transactions')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(10);

          transactionsWithProducts = (fallbackData || []).map(transaction => ({
            ...transaction,
            products: { name: 'Unknown Product', sku: 'N/A' }
          }));
        } else {
          transactionsWithProducts = transactionsData || [];
        }
      } catch (error) {
        console.warn('Error loading transactions:', error);
        transactionsWithProducts = [];
      }

      // Load low stock products with error handling
      let lowStockData = [];
      try {
        const { data, error: lowStockError } = await supabase.rpc('get_low_stock_products');
        if (lowStockError) {
          console.warn('Low stock RPC error, using manual calculation:', lowStockError.message);
          // Calculate manually as fallback
          lowStockData = (productsData || []).filter(product =>
            product.stock_quantity <= (product.low_stock_threshold || 5)
          );
        } else {
          lowStockData = data || [];
        }
      } catch (error) {
        console.warn('RPC function not available, using manual calculation');
        // Calculate manually as fallback
        lowStockData = (productsData || []).filter(product =>
          product.stock_quantity <= (product.low_stock_threshold || 5)
        );
      }

      setProducts(productsData || []);
      setTransactions(transactionsWithProducts);
      setLowStockProducts(lowStockData || []);
    } catch (error) {
      console.error('Error loading inventory data:', error);
      toast.error('Failed to load inventory data');
    } finally {
      setLoading(false);
    }
  };

  const adjustStock = async () => {
    if (!selectedProduct || !adjustmentQuantity) return;

    try {
      const quantity = parseInt(adjustmentQuantity);
      const quantityChange = adjustmentType === 'add' ? quantity : -quantity;

      const { data, error } = await supabase.rpc('process_inventory_transaction', {
        p_product_id: selectedProduct.id,
        p_transaction_type: 'adjustment',
        p_quantity_change: quantityChange,
        p_reference_type: 'manual',
        p_notes: adjustmentNotes || `Manual ${adjustmentType === 'add' ? 'addition' : 'subtraction'} of ${quantity} units`
      });

      if (error) throw error;

      if (data?.success) {
        toast.success(`Stock ${adjustmentType === 'add' ? 'added' : 'removed'} successfully`);
        setShowAdjustModal(false);
        setSelectedProduct(null);
        setAdjustmentQuantity('');
        setAdjustmentNotes('');
        loadInventoryData();
      } else {
        toast.error(data?.error || 'Failed to adjust stock');
      }
    } catch (error) {
      console.error('Error adjusting stock:', error);
      toast.error('Failed to adjust stock');
    }
  };

  const updateLowStockThreshold = async (productId: string, newThreshold: number) => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ low_stock_threshold: newThreshold })
        .eq('id', productId);

      if (error) throw error;

      toast.success('Low stock threshold updated');
      loadInventoryData();
    } catch (error) {
      console.error('Error updating threshold:', error);
      toast.error('Failed to update threshold');
    }
  };

  const getStockStatus = (product: Product) => {
    if (product.stock_quantity === 0) return 'out_of_stock';
    if (product.stock_quantity <= product.low_stock_threshold) return 'low_stock';
    return 'in_stock';
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'out_of_stock': return 'bg-red-100 text-red-800';
      case 'low_stock': return 'bg-yellow-100 text-yellow-800';
      case 'in_stock': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStockStatusIcon = (status: string) => {
    switch (status) {
      case 'out_of_stock': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'low_stock': return <TrendingDown className="h-4 w-4 text-yellow-500" />;
      case 'in_stock': return <TrendingUp className="h-4 w-4 text-green-500" />;
      default: return <Package className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (!matchesSearch) return false;

    const status = getStockStatus(product);
    if (stockFilter === 'all') return true;
    return status === stockFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading inventory...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-4 sm:p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold mb-2">Inventory Management</h2>
            <p className="opacity-90 text-sm sm:text-base">Monitor stock levels and manage inventory</p>
          </div>
          <Package className="h-8 w-8 sm:h-12 sm:w-12 opacity-80" />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Products</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900">{products.length}</p>
            </div>
            <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Low Stock Items</p>
              <p className="text-xl sm:text-2xl font-bold text-yellow-600">{lowStockProducts.length}</p>
            </div>
            <TrendingDown className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Out of Stock</p>
              <p className="text-xl sm:text-2xl font-bold text-red-600">
                {products.filter(p => p.stock_quantity === 0).length}
              </p>
            </div>
            <AlertTriangle className="h-6 w-6 sm:h-8 sm:w-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Stock Value</p>
              <p className="text-lg sm:text-2xl font-bold text-green-600">
                ₹{products.reduce((sum, p) => sum + (p.stock_quantity * p.price), 0).toFixed(0)}
              </p>
            </div>
            <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search products by name or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <select
              value={stockFilter}
              onChange={(e) => setStockFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Stock Levels</option>
              <option value="in_stock">In Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>
            
            <button
              onClick={loadInventoryData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Products ({filteredProducts.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Threshold
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => {
                const status = getStockStatus(product);
                return (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img
                          src={product.image_url || '/placeholder-product.jpg'}
                          alt={product.name}
                          className="w-10 h-10 object-cover rounded-lg mr-3"
                        />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          <div className="text-sm text-gray-500">₹{product.price}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                      {product.sku}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{product.stock_quantity}</div>
                      <div className="text-sm text-gray-500">units</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getStockStatusIcon(status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStockStatusColor(status)}`}>
                          {status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        value={product.low_stock_threshold}
                        onChange={(e) => updateLowStockThreshold(product.id, parseInt(e.target.value) || 0)}
                        className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                        min="0"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowAdjustModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                      >
                        <Edit className="h-4 w-4" />
                        Adjust
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Stock Adjustment Modal */}
      {showAdjustModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4">Adjust Stock - {selectedProduct.name}</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Stock: {selectedProduct.stock_quantity} units
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Adjustment Type
                </label>
                <div className="flex gap-2">
                  <button
                    onClick={() => setAdjustmentType('add')}
                    className={`flex-1 px-3 py-2 rounded-lg border ${
                      adjustmentType === 'add' 
                        ? 'bg-green-50 border-green-200 text-green-800' 
                        : 'bg-gray-50 border-gray-200 text-gray-600'
                    }`}
                  >
                    <Plus className="h-4 w-4 inline mr-1" />
                    Add Stock
                  </button>
                  <button
                    onClick={() => setAdjustmentType('subtract')}
                    className={`flex-1 px-3 py-2 rounded-lg border ${
                      adjustmentType === 'subtract' 
                        ? 'bg-red-50 border-red-200 text-red-800' 
                        : 'bg-gray-50 border-gray-200 text-gray-600'
                    }`}
                  >
                    <Minus className="h-4 w-4 inline mr-1" />
                    Remove Stock
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <input
                  type="number"
                  value={adjustmentQuantity}
                  onChange={(e) => setAdjustmentQuantity(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter quantity"
                  min="1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes (optional)
                </label>
                <textarea
                  value={adjustmentNotes}
                  onChange={(e) => setAdjustmentNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Reason for adjustment..."
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => {
                  setShowAdjustModal(false);
                  setSelectedProduct(null);
                  setAdjustmentQuantity('');
                  setAdjustmentNotes('');
                }}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={adjustStock}
                disabled={!adjustmentQuantity}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {adjustmentType === 'add' ? 'Add' : 'Remove'} Stock
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryManagement;
