import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';

interface User {
  id: string;
  email: string;
  full_name: string;
  username: string;
  is_premium: boolean;
  premium_lifetime_access: boolean;
  wallet_balance: number;
  wallet_unlocked: boolean;
  created_at: string;
  last_login_at?: string;
  referred_by?: string;
  referral_code?: string;
  premium_referral_count?: number;
  referred_by_id?: string;
}

interface LoginLog {
  id: string;
  user_id: string;
  login_time: string;
  ip_address?: string;
  user_agent?: string;
  full_name: string;
  email: string;
}

export const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loginLogs, setLoginLogs] = useState<LoginLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'users' | 'logs'>('users');
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showPremiumConfirm, setShowPremiumConfirm] = useState(false);
  const [premiumChangeDetails, setPremiumChangeDetails] = useState<{
    user: User;
    newPremiumStatus: boolean;
    newLifetimeAccess: boolean;
  } | null>(null);

  useEffect(() => {
    loadUsers();
    loadLoginLogs();
  }, []);

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    }
  };

  const loadLoginLogs = async () => {
    try {
      // First, let's create the login_logs table if it doesn't exist
      await supabase.rpc('create_login_logs_table');

      const { data, error } = await supabase
        .from('login_logs')
        .select(`
          *,
          user:users(full_name, email)
        `)
        .order('login_time', { ascending: false })
        .limit(100);

      if (error && error.code !== '42P01') { // Ignore table doesn't exist error
        throw error;
      }

      setLoginLogs(data || []);
    } catch (error) {
      console.error('Error loading login logs:', error);
      // Don't show error toast for login logs as table might not exist yet
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser({ ...user });
  };

  const handleSaveUser = async () => {
    if (!editingUser) return;

    try {
      // Get current user data to check for premium status changes
      const { data: currentUser } = await supabase
        .from('users')
        .select('is_premium, premium_lifetime_access')
        .eq('id', editingUser.id)
        .single();

      const premiumStatusChanged =
        currentUser?.is_premium !== editingUser.is_premium ||
        currentUser?.premium_lifetime_access !== editingUser.premium_lifetime_access;

      // If premium status changed, use synchronization function
      if (premiumStatusChanged) {
        const { data: syncResult, error: syncError } = await supabase.rpc('sync_premium_status_change', {
          p_user_id: editingUser.id,
          p_new_premium_status: editingUser.is_premium,
          p_new_lifetime_access: editingUser.premium_lifetime_access,
          p_admin_user_id: null, // Could be set to current admin user ID
          p_change_reason: 'admin_panel_update'
        });

        if (syncError) throw syncError;

        if (syncResult?.success) {
          console.log('Premium status synchronized:', syncResult);

          if (syncResult.referral_updates?.referrer_updated) {
            toast.success(
              `User updated! Referrer ${syncResult.referral_updates.referrer_name} received premium bonus.`
            );
          } else {
            toast.success('User premium status updated and synchronized!');
          }
        } else {
          throw new Error(syncResult?.error || 'Failed to synchronize premium status');
        }
      }

      // Update other fields normally
      const { error } = await supabase
        .from('users')
        .update({
          full_name: editingUser.full_name,
          username: editingUser.username,
          email: editingUser.email,
          wallet_balance: editingUser.wallet_balance,
          wallet_unlocked: editingUser.wallet_unlocked
        })
        .eq('id', editingUser.id);

      if (error) throw error;

      if (!premiumStatusChanged) {
        toast.success('User updated successfully');
      }

      setEditingUser(null);
      loadUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    }
  };

  const handleAddFunds = async (userId: string, amount: number) => {
    try {
      // Get current balance
      const { data: user } = await supabase
        .from('users')
        .select('wallet_balance, full_name')
        .eq('id', userId)
        .single();

      if (!user) throw new Error('User not found');

      const newBalance = (user.wallet_balance || 0) + amount;

      // Update balance
      const { error: updateError } = await supabase
        .from('users')
        .update({ wallet_balance: newBalance })
        .eq('id', userId);

      if (updateError) throw updateError;

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('wallet_transactions')
        .insert({
          user_id: userId,
          type: 'credit',
          amount,
          description: `Admin credit - Funds added by administrator`,
          reference_type: 'admin_credit'
        });

      if (transactionError) throw transactionError;

      toast.success(`₹${amount} added to ${user.full_name}'s wallet`);
      loadUsers();
    } catch (error) {
      console.error('Error adding funds:', error);
      toast.error('Failed to add funds');
    }
  };

  const handleFixReferralRecords = async () => {
    try {
      const { data: result, error } = await supabase.rpc('fix_missing_referral_records');

      if (error) throw error;

      if (result?.success) {
        toast.success(`Fixed ${result.fixed_records} missing referral records`);
        loadUsers();
      } else {
        toast.error('Failed to fix referral records');
      }
    } catch (error) {
      console.error('Error fixing referral records:', error);
      toast.error('Failed to fix referral records');
    }
  };

  const filteredUsers = users.filter(user =>
    user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN');
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">User Management</h2>
        
        {/* Tabs */}
        <div className="flex space-x-4 mb-4">
          <button
            onClick={() => setActiveTab('users')}
            className={`px-4 py-2 rounded-lg font-medium ${
              activeTab === 'users'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Users ({users.length})
          </button>
          <button
            onClick={() => setActiveTab('logs')}
            className={`px-4 py-2 rounded-lg font-medium ${
              activeTab === 'logs'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Login Logs ({loginLogs.length})
          </button>
        </div>

        {/* Search and Actions */}
        {activeTab === 'users' && (
          <div className="mb-4 flex gap-4">
            <input
              type="text"
              placeholder="Search users by name, email, or username..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={handleFixReferralRecords}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 whitespace-nowrap"
              title="Fix missing referral records for manually upgraded premium users"
            >
              🔧 Fix Referrals
            </button>
          </div>
        )}
      </div>

      {/* Users Tab */}
      {activeTab === 'users' && (
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wallet
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Referral
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {user.full_name}
                        </div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="text-xs text-gray-400">@{user.username}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                          user.is_premium
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {user.is_premium ? '✅ Premium' : '⭐ Standard'}
                        </span>
                        {user.wallet_unlocked && (
                          <span className="inline-flex px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">
                            🔓 Wallet Unlocked
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(user.wallet_balance || 0)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {user.referral_code && (
                          <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                            {user.referral_code}
                          </span>
                        )}
                        {user.referred_by && (
                          <div className="text-xs text-gray-500 mt-1">
                            Referred by: {user.referred_by}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user.created_at)}
                      {user.last_login_at && (
                        <div className="text-xs text-gray-400">
                          Last: {formatDate(user.last_login_at)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEditUser(user)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => {
                          const amount = prompt('Enter amount to add to wallet:');
                          if (amount && !isNaN(Number(amount))) {
                            handleAddFunds(user.id, Number(amount));
                          }
                        }}
                        className="text-green-600 hover:text-green-900"
                      >
                        Add Funds
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Login Logs Tab */}
      {activeTab === 'logs' && (
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Login Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    IP Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Device
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loginLogs.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                      No login logs available
                    </td>
                  </tr>
                ) : (
                  loginLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {log.full_name}
                          </div>
                          <div className="text-sm text-gray-500">{log.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(log.login_time)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.ip_address || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.user_agent ? (
                          <span className="truncate max-w-xs block" title={log.user_agent}>
                            {log.user_agent.substring(0, 50)}...
                          </span>
                        ) : (
                          'N/A'
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {editingUser && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 className="text-xl font-bold mb-4">Edit User</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  value={editingUser.full_name}
                  onChange={(e) => setEditingUser({...editingUser, full_name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Username
                </label>
                <input
                  type="text"
                  value={editingUser.username}
                  onChange={(e) => setEditingUser({...editingUser, username: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={editingUser.email}
                  onChange={(e) => setEditingUser({...editingUser, email: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Wallet Balance
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editingUser.wallet_balance}
                  onChange={(e) => setEditingUser({...editingUser, wallet_balance: Number(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editingUser.is_premium}
                    onChange={(e) => setEditingUser({...editingUser, is_premium: e.target.checked})}
                    className="mr-2"
                  />
                  Premium User
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editingUser.premium_lifetime_access}
                    onChange={(e) => setEditingUser({...editingUser, premium_lifetime_access: e.target.checked})}
                    className="mr-2"
                  />
                  Lifetime Access
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editingUser.wallet_unlocked}
                    onChange={(e) => setEditingUser({...editingUser, wallet_unlocked: e.target.checked})}
                    className="mr-2"
                  />
                  Wallet Unlocked
                </label>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setEditingUser(null)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveUser}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
