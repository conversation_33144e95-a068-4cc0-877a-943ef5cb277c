import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuthStore } from '../../stores/authStore';
import toast from 'react-hot-toast';
import { Shield, CheckCircle, Clock, XCircle, AlertCircle } from 'lucide-react';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  data?: any;
}

interface KYCTestUser {
  id: string;
  email: string;
  full_name: string;
  kyc_status: string;
  kyc_submitted_at?: string;
  kyc_reviewed_at?: string;
}

const KYCSystemTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [kycUsers, setKycUsers] = useState<KYCTestUser[]>([]);
  const { user } = useAuthStore();

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const runKYCTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      addResult({ test: 'Starting KYC System Tests', status: 'pass', message: 'Initializing...' });

      // Test 1: Check current user's KYC status
      if (user) {
        const { data: userData } = await supabase
          .from('users')
          .select('kyc_status, kyc_submitted_at, kyc_reviewed_at, kyc_comments')
          .eq('id', user.id)
          .single();

        addResult({
          test: 'Current User KYC Status',
          status: 'pass',
          message: `KYC Status: ${userData?.kyc_status || 'pending'}`,
          data: userData
        });
      }

      // Test 2: Check KYC applications in system
      const { data: allKycUsers, error: kycError } = await supabase
        .from('users')
        .select('id, email, full_name, kyc_status, kyc_submitted_at, kyc_reviewed_at')
        .not('kyc_submitted_at', 'is', null)
        .order('kyc_submitted_at', { ascending: false })
        .limit(10);

      if (kycError) {
        addResult({
          test: 'KYC Applications Query',
          status: 'fail',
          message: `Error: ${kycError.message}`
        });
      } else {
        setKycUsers(allKycUsers || []);
        addResult({
          test: 'KYC Applications Query',
          status: 'pass',
          message: `Found ${allKycUsers?.length || 0} KYC applications`,
          data: allKycUsers
        });
      }

      // Test 3: Check KYC document storage
      try {
        const { data: buckets } = await supabase.storage.listBuckets();
        const kycBucket = buckets?.find(b => b.name === 'kyc-documents');
        
        addResult({
          test: 'KYC Document Storage',
          status: kycBucket ? 'pass' : 'warning',
          message: kycBucket ? 'KYC storage bucket exists' : 'KYC storage bucket not found',
          data: kycBucket
        });
      } catch (error) {
        addResult({
          test: 'KYC Document Storage',
          status: 'fail',
          message: `Storage error: ${error.message}`
        });
      }

      // Test 4: Check KYC bonus system
      const { data: bonusTransactions } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('reference_type', 'kyc_bonus')
        .limit(5);

      addResult({
        test: 'KYC Bonus System',
        status: 'pass',
        message: `Found ${bonusTransactions?.length || 0} KYC bonus transactions`,
        data: bonusTransactions
      });

      // Test 5: Check KYC status distribution
      const { data: statusStats } = await supabase
        .from('users')
        .select('kyc_status')
        .not('kyc_submitted_at', 'is', null);

      if (statusStats) {
        const stats = statusStats.reduce((acc, user) => {
          acc[user.kyc_status] = (acc[user.kyc_status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        addResult({
          test: 'KYC Status Distribution',
          status: 'pass',
          message: 'Status distribution calculated',
          data: stats
        });
      }

    } catch (error) {
      addResult({
        test: 'General Error',
        status: 'fail',
        message: `Unexpected error: ${error.message}`
      });
    } finally {
      setIsRunning(false);
    }
  };

  const simulateKYCApproval = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          kyc_status: 'approved',
          kyc_reviewed_at: new Date().toISOString(),
          kyc_comments: 'Test approval via system test'
        })
        .eq('id', userId);

      if (error) throw error;

      toast.success('KYC approved successfully!');
      runKYCTests(); // Refresh results
    } catch (error) {
      toast.error(`Failed to approve KYC: ${error.message}`);
    }
  };

  const simulateKYCRejection = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          kyc_status: 'rejected',
          kyc_reviewed_at: new Date().toISOString(),
          kyc_comments: 'Test rejection via system test'
        })
        .eq('id', userId);

      if (error) throw error;

      toast.success('KYC rejected successfully!');
      runKYCTests(); // Refresh results
    } catch (error) {
      toast.error(`Failed to reject KYC: ${error.message}`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'under_review': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'rejected': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertCircle className="w-4 h-4 text-orange-600" />;
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-6xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">🛡️ KYC System Testing</h2>
      
      <div className="flex gap-4 mb-6">
        <button
          onClick={runKYCTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Tests...' : 'Run KYC Tests'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Results */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Test Results</h3>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-l-4 ${
                  result.status === 'pass'
                    ? 'bg-green-50 border-green-500'
                    : result.status === 'warning'
                    ? 'bg-yellow-50 border-yellow-500'
                    : 'bg-red-50 border-red-500'
                }`}
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className={`text-sm font-medium ${
                    result.status === 'pass'
                      ? 'text-green-700'
                      : result.status === 'warning'
                      ? 'text-yellow-700'
                      : 'text-red-700'
                  }`}>
                    {result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'} {result.test}
                  </span>
                </div>
                <p className="text-gray-700 text-sm">{result.message}</p>
                {result.data && (
                  <details className="mt-2">
                    <summary className="text-xs text-gray-500 cursor-pointer">View Data</summary>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* KYC Applications */}
        <div>
          <h3 className="text-lg font-semibold mb-4">KYC Applications</h3>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {kycUsers.map((kycUser) => (
              <div key={kycUser.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(kycUser.kyc_status)}
                    <span className="font-medium">{kycUser.full_name}</span>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded ${
                    kycUser.kyc_status === 'approved' ? 'bg-green-100 text-green-700' :
                    kycUser.kyc_status === 'under_review' ? 'bg-yellow-100 text-yellow-700' :
                    kycUser.kyc_status === 'rejected' ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {kycUser.kyc_status}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{kycUser.email}</p>
                {kycUser.kyc_status === 'under_review' && user?.email === '<EMAIL>' && (
                  <div className="flex gap-2">
                    <button
                      onClick={() => simulateKYCApproval(kycUser.id)}
                      className="text-xs px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => simulateKYCRejection(kycUser.id)}
                      className="text-xs px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Reject
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCSystemTest;
