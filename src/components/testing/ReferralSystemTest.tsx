import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { multiLevelReferralService } from '../../services/multiLevelReferralService';
import { referralService } from '../../services/referralService';
import { useAuthStore } from '../../stores/authStore';
import toast from 'react-hot-toast';
import KYCSystemTest from './KYCSystemTest';
import PremiumWalletTest from './PremiumWalletTest';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  data?: any;
}

const ReferralSystemTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { user } = useAuthStore();

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const runReferralTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Check referral code generation
      addResult({ test: 'Starting Referral System Tests', status: 'pass', message: 'Initializing...' });

      // Test 2: Validate current user's referral code
      if (user) {
        const { data: userData } = await supabase
          .from('users')
          .select('referral_code, is_premium, wallet_balance')
          .eq('id', user.id)
          .single();

        if (userData?.referral_code) {
          addResult({
            test: 'User Referral Code',
            status: 'pass',
            message: `User has referral code: ${userData.referral_code}`,
            data: userData
          });
        } else {
          addResult({
            test: 'User Referral Code',
            status: 'fail',
            message: 'User does not have a referral code'
          });
        }
      }

      // Test 3: Check referral chain building
      try {
        const { data: testUser } = await supabase
          .from('users')
          .select('*')
          .eq('email', '<EMAIL>')
          .single();

        if (testUser) {
          const chain = await multiLevelReferralService.buildReferralChain(testUser);
          addResult({
            test: 'Referral Chain Building',
            status: 'pass',
            message: `Built chain with ${chain.length} levels`,
            data: chain
          });
        }
      } catch (error) {
        addResult({
          test: 'Referral Chain Building',
          status: 'fail',
          message: `Error: ${error.message}`
        });
      }

      // Test 4: Check bonus calculation
      try {
        const bonusAmounts = await multiLevelReferralService.getConfigurableBonusAmounts();
        addResult({
          test: 'Bonus Calculation',
          status: 'pass',
          message: 'Bonus amounts configured correctly',
          data: bonusAmounts
        });
      } catch (error) {
        addResult({
          test: 'Bonus Calculation',
          status: 'fail',
          message: `Error: ${error.message}`
        });
      }

      // Test 5: Check referral validation
      try {
        const validation = await referralService.validateReferralCode('ADMIN123');
        addResult({
          test: 'Referral Code Validation',
          status: validation.isValid ? 'pass' : 'warning',
          message: validation.isValid ? 'ADMIN123 is valid' : 'ADMIN123 validation failed',
          data: validation
        });
      } catch (error) {
        addResult({
          test: 'Referral Code Validation',
          status: 'fail',
          message: `Error: ${error.message}`
        });
      }

      // Test 6: Check referral statistics
      if (user) {
        try {
          const stats = await referralService.getReferralStats(user.id);
          addResult({
            test: 'Referral Statistics',
            status: 'pass',
            message: `Stats loaded: ${stats.totalReferrals} referrals, ₹${stats.totalEarnings} earned`,
            data: stats
          });
        } catch (error) {
          addResult({
            test: 'Referral Statistics',
            status: 'fail',
            message: `Error: ${error.message}`
          });
        }
      }

      // Test 7: Check multi-level system status
      try {
        const isEnabled = await multiLevelReferralService.isMultiLevelReferralEnabled();
        addResult({
          test: 'Multi-Level System Status',
          status: isEnabled ? 'pass' : 'warning',
          message: isEnabled ? 'Multi-level referrals enabled' : 'Multi-level referrals disabled'
        });
      } catch (error) {
        addResult({
          test: 'Multi-Level System Status',
          status: 'fail',
          message: `Error: ${error.message}`
        });
      }

    } catch (error) {
      addResult({
        test: 'General Error',
        status: 'fail',
        message: `Unexpected error: ${error.message}`
      });
    } finally {
      setIsRunning(false);
    }
  };

  const simulateReferral = async () => {
    if (!user) {
      toast.error('Please login to test referral simulation');
      return;
    }

    try {
      const result = await multiLevelReferralService.processMultiLevelReferral(
        user.id,
        user.email || '',
        'ADMIN123'
      );

      if (result.success) {
        toast.success(`Simulation successful! ₹${result.totalBonusDistributed} distributed across ${result.levelsProcessed} levels`);
        addResult({
          test: 'Referral Simulation',
          status: 'pass',
          message: 'Simulation completed successfully',
          data: result
        });
      } else {
        toast.error('Simulation failed');
        addResult({
          test: 'Referral Simulation',
          status: 'fail',
          message: 'Simulation failed',
          data: result
        });
      }
    } catch (error) {
      toast.error(`Simulation error: ${error.message}`);
      addResult({
        test: 'Referral Simulation',
        status: 'fail',
        message: `Error: ${error.message}`
      });
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">🔗 Referral System Testing</h2>
      
      <div className="flex gap-4 mb-6">
        <button
          onClick={runReferralTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Tests...' : 'Run Referral Tests'}
        </button>
        
        <button
          onClick={simulateReferral}
          disabled={isRunning}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          Simulate Referral
        </button>
      </div>

      <div className="space-y-4">
        {testResults.map((result, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg border-l-4 ${
              result.status === 'pass'
                ? 'bg-green-50 border-green-500'
                : result.status === 'warning'
                ? 'bg-yellow-50 border-yellow-500'
                : 'bg-red-50 border-red-500'
            }`}
          >
            <div className="flex items-center gap-2 mb-2">
              <span className={`text-sm font-medium ${
                result.status === 'pass'
                  ? 'text-green-700'
                  : result.status === 'warning'
                  ? 'text-yellow-700'
                  : 'text-red-700'
              }`}>
                {result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'} {result.test}
              </span>
            </div>
            <p className="text-gray-700 text-sm">{result.message}</p>
            {result.data && (
              <details className="mt-2">
                <summary className="text-xs text-gray-500 cursor-pointer">View Data</summary>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </details>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReferralSystemTest;
