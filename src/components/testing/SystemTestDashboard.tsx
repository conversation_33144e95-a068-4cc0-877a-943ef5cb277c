import React, { useState } from 'react';
import { Shield, Users, Crown, Wallet, Settings, AlertTriangle } from 'lucide-react';
import ReferralSystemTest from './ReferralSystemTest';
import KYCSystemTest from './KYCSystemTest';
import PremiumWalletTest from './PremiumWalletTest';

type TestCategory = 'overview' | 'referral' | 'kyc' | 'premium' | 'error-handling';

const SystemTestDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TestCategory>('overview');

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Settings },
    { id: 'referral', name: 'Referral System', icon: Users },
    { id: 'kyc', name: 'KYC Verification', icon: Shield },
    { id: 'premium', name: 'Premium & Wallet', icon: Crown },
    { id: 'error-handling', name: '<PERSON>rro<PERSON> Handling', icon: AlertTriangle },
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="text-center py-8">
        <Settings className="w-16 h-16 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">System Testing Dashboard</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Comprehensive testing suite for all major system components including referral system, 
          KYC verification, premium features, and wallet functionality.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg">
          <Users className="w-8 h-8 mb-3" />
          <h3 className="text-lg font-semibold mb-2">Referral System</h3>
          <p className="text-sm opacity-90">
            Test referral code generation, validation, multi-level bonuses, and chain building.
          </p>
          <button
            onClick={() => setActiveTab('referral')}
            className="mt-4 bg-white/20 hover:bg-white/30 px-4 py-2 rounded text-sm font-medium transition-colors"
          >
            Test Referrals
          </button>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg">
          <Shield className="w-8 h-8 mb-3" />
          <h3 className="text-lg font-semibold mb-2">KYC Verification</h3>
          <p className="text-sm opacity-90">
            Test document upload, verification workflow, admin approval, and bonus distribution.
          </p>
          <button
            onClick={() => setActiveTab('kyc')}
            className="mt-4 bg-white/20 hover:bg-white/30 px-4 py-2 rounded text-sm font-medium transition-colors"
          >
            Test KYC
          </button>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg">
          <Crown className="w-8 h-8 mb-3" />
          <h3 className="text-lg font-semibold mb-2">Premium & Wallet</h3>
          <p className="text-sm opacity-90">
            Test premium upgrades, wallet transactions, balance management, and payment processing.
          </p>
          <button
            onClick={() => setActiveTab('premium')}
            className="mt-4 bg-white/20 hover:bg-white/30 px-4 py-2 rounded text-sm font-medium transition-colors"
          >
            Test Premium
          </button>
        </div>

        <div className="bg-gradient-to-r from-red-500 to-red-600 text-white p-6 rounded-lg">
          <AlertTriangle className="w-8 h-8 mb-3" />
          <h3 className="text-lg font-semibold mb-2">Error Handling</h3>
          <p className="text-sm opacity-90">
            Test edge cases, error recovery, data consistency, and system resilience.
          </p>
          <button
            onClick={() => setActiveTab('error-handling')}
            className="mt-4 bg-white/20 hover:bg-white/30 px-4 py-2 rounded text-sm font-medium transition-colors"
          >
            Test Errors
          </button>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
          <div>
            <h4 className="text-sm font-medium text-yellow-800">Testing Guidelines</h4>
            <ul className="mt-2 text-sm text-yellow-700 space-y-1">
              <li>• Run tests in a safe environment with test data</li>
              <li>• Some tests may modify database records</li>
              <li>• Premium upgrade simulation affects user status</li>
              <li>• Wallet operations create real transaction records</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderErrorHandling = () => (
    <div className="space-y-6">
      <div className="text-center py-8">
        <AlertTriangle className="w-16 h-16 text-red-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Handling Tests</h2>
        <p className="text-gray-600">
          Test system resilience and error recovery mechanisms.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Database Error Handling</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Invalid User ID Operations
            </button>
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Duplicate Referral Code Generation
            </button>
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Insufficient Wallet Balance
            </button>
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Network Timeout Scenarios
            </button>
          </div>
        </div>

        <div className="bg-white border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Authentication Edge Cases</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Expired Session Handling
            </button>
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Concurrent Login Attempts
            </button>
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Invalid Token Recovery
            </button>
            <button className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100">
              Test Permission Boundary Violations
            </button>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">Error Handling Status</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-green-600">✅ Authentication Errors</span>
            <p className="text-blue-700">Proper error messages and recovery</p>
          </div>
          <div>
            <span className="text-green-600">✅ Database Constraints</span>
            <p className="text-blue-700">Graceful constraint violation handling</p>
          </div>
          <div>
            <span className="text-green-600">✅ Network Failures</span>
            <p className="text-blue-700">Timeout and retry mechanisms</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">System Testing Dashboard</h1>
          <p className="text-gray-600 mt-2">Comprehensive testing suite for all system components</p>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TestCategory)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'referral' && <ReferralSystemTest />}
          {activeTab === 'kyc' && <KYCSystemTest />}
          {activeTab === 'premium' && <PremiumWalletTest />}
          {activeTab === 'error-handling' && renderErrorHandling()}
        </div>
      </div>
    </div>
  );
};

export default SystemTestDashboard;
