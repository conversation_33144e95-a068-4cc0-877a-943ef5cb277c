import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuthStore } from '../../stores/authStore';
import { walletService } from '../../services/walletService';
import { premiumService } from '../../services/premiumService';
import toast from 'react-hot-toast';
import { Crown, Wallet, CreditCard, TrendingUp } from 'lucide-react';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  data?: any;
}

const PremiumWalletTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
  const [premiumStatus, setPremiumStatus] = useState(false);
  const { user, checkUser } = useAuthStore();

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const runPremiumWalletTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      addResult({ test: 'Starting Premium & Wallet Tests', status: 'pass', message: 'Initializing...' });

      // Test 1: Check current user's premium status
      if (user) {
        const { data: userData } = await supabase
          .from('users')
          .select('is_premium, premium_lifetime_access, wallet_balance, ewallet_unlocked')
          .eq('id', user.id)
          .single();

        setPremiumStatus(userData?.is_premium || false);
        setWalletBalance(userData?.wallet_balance || 0);

        addResult({
          test: 'User Premium Status',
          status: userData?.is_premium ? 'pass' : 'warning',
          message: `Premium: ${userData?.is_premium ? 'Active' : 'Inactive'}, Wallet: ₹${userData?.wallet_balance || 0}`,
          data: userData
        });
      }

      // Test 2: Check premium plans
      try {
        const plans = await premiumService.getPremiumPlans();
        addResult({
          test: 'Premium Plans Loading',
          status: plans.length > 0 ? 'pass' : 'fail',
          message: `Found ${plans.length} premium plans`,
          data: plans
        });
      } catch (error) {
        addResult({
          test: 'Premium Plans Loading',
          status: 'fail',
          message: `Error: ${error.message}`
        });
      }

      // Test 3: Check wallet functionality
      if (user) {
        try {
          const walletStats = await walletService.getWalletStats(user.id);
          addResult({
            test: 'Wallet Statistics',
            status: 'pass',
            message: `Balance: ₹${walletStats.balance}, Transactions: ${walletStats.transactionCount}`,
            data: walletStats
          });
        } catch (error) {
          addResult({
            test: 'Wallet Statistics',
            status: 'fail',
            message: `Error: ${error.message}`
          });
        }
      }

      // Test 4: Check wallet transactions
      const { data: transactions } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(5);

      addResult({
        test: 'Wallet Transactions',
        status: 'pass',
        message: `Found ${transactions?.length || 0} recent transactions`,
        data: transactions
      });

      // Test 5: Check premium users count
      const { data: premiumUsers } = await supabase
        .from('users')
        .select('id, email, is_premium, wallet_balance')
        .eq('is_premium', true)
        .limit(10);

      addResult({
        test: 'Premium Users Query',
        status: 'pass',
        message: `Found ${premiumUsers?.length || 0} premium users`,
        data: premiumUsers
      });

      // Test 6: Check wallet unlock progress
      if (user) {
        try {
          const unlockProgress = await walletService.getUnlockProgress(user.id);
          addResult({
            test: 'Wallet Unlock Progress',
            status: unlockProgress.unlocked ? 'pass' : 'warning',
            message: `Progress: ${unlockProgress.progress}%, Unlocked: ${unlockProgress.unlocked}`,
            data: unlockProgress
          });
        } catch (error) {
          addResult({
            test: 'Wallet Unlock Progress',
            status: 'fail',
            message: `Error: ${error.message}`
          });
        }
      }

    } catch (error) {
      addResult({
        test: 'General Error',
        status: 'fail',
        message: `Unexpected error: ${error.message}`
      });
    } finally {
      setIsRunning(false);
    }
  };

  const simulatePremiumUpgrade = async () => {
    if (!user) {
      toast.error('Please login to test premium upgrade');
      return;
    }

    try {
      const { error } = await supabase
        .from('users')
        .update({
          is_premium: true,
          premium_lifetime_access: true,
          premium_purchase_confirmed: true,
          premium_no_refund_accepted: true,
          premium_purchased_at: new Date().toISOString(),
          ewallet_unlocked: true,
          wallet_unlocked: true
        })
        .eq('id', user.id);

      if (error) throw error;

      await checkUser(); // Refresh user data
      toast.success('Premium upgrade simulated successfully!');
      runPremiumWalletTests(); // Refresh test results
    } catch (error) {
      toast.error(`Premium upgrade failed: ${error.message}`);
    }
  };

  const simulateWalletCredit = async () => {
    if (!user) {
      toast.error('Please login to test wallet credit');
      return;
    }

    try {
      const amount = 100;
      const success = await walletService.addFunds(
        user.id,
        amount,
        'Test credit via system test',
        'test',
        `TEST_${Date.now()}`
      );

      if (success) {
        await checkUser(); // Refresh user data
        toast.success(`₹${amount} credited to wallet successfully!`);
        runPremiumWalletTests(); // Refresh test results
      } else {
        toast.error('Failed to credit wallet');
      }
    } catch (error) {
      toast.error(`Wallet credit failed: ${error.message}`);
    }
  };

  const simulateWalletDebit = async () => {
    if (!user) {
      toast.error('Please login to test wallet debit');
      return;
    }

    try {
      const amount = 50;
      const success = await walletService.deductFunds(
        user.id,
        amount,
        'Test debit via system test',
        'test',
        `TEST_DEBIT_${Date.now()}`
      );

      if (success) {
        await checkUser(); // Refresh user data
        toast.success(`₹${amount} debited from wallet successfully!`);
        runPremiumWalletTests(); // Refresh test results
      } else {
        toast.error('Failed to debit wallet');
      }
    } catch (error) {
      toast.error(`Wallet debit failed: ${error.message}`);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-6xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">💎 Premium & Wallet Testing</h2>
      
      {/* Current Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
          <div className="flex items-center gap-2">
            <Crown className="w-6 h-6" />
            <div>
              <p className="text-sm opacity-90">Premium Status</p>
              <p className="text-lg font-bold">{premiumStatus ? 'Active' : 'Inactive'}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
          <div className="flex items-center gap-2">
            <Wallet className="w-6 h-6" />
            <div>
              <p className="text-sm opacity-90">Wallet Balance</p>
              <p className="text-lg font-bold">₹{walletBalance.toFixed(2)}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-6 h-6" />
            <div>
              <p className="text-sm opacity-90">Test Status</p>
              <p className="text-lg font-bold">{isRunning ? 'Running...' : 'Ready'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={runPremiumWalletTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Tests...' : 'Run Premium & Wallet Tests'}
        </button>
        
        <button
          onClick={simulatePremiumUpgrade}
          disabled={isRunning || premiumStatus}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
        >
          Simulate Premium Upgrade
        </button>
        
        <button
          onClick={simulateWalletCredit}
          disabled={isRunning}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          Add ₹100 to Wallet
        </button>
        
        <button
          onClick={simulateWalletDebit}
          disabled={isRunning || walletBalance < 50}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
        >
          Deduct ₹50 from Wallet
        </button>
      </div>

      {/* Test Results */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {testResults.map((result, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg border-l-4 ${
              result.status === 'pass'
                ? 'bg-green-50 border-green-500'
                : result.status === 'warning'
                ? 'bg-yellow-50 border-yellow-500'
                : 'bg-red-50 border-red-500'
            }`}
          >
            <div className="flex items-center gap-2 mb-2">
              <span className={`text-sm font-medium ${
                result.status === 'pass'
                  ? 'text-green-700'
                  : result.status === 'warning'
                  ? 'text-yellow-700'
                  : 'text-red-700'
              }`}>
                {result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'} {result.test}
              </span>
            </div>
            <p className="text-gray-700 text-sm">{result.message}</p>
            {result.data && (
              <details className="mt-2">
                <summary className="text-xs text-gray-500 cursor-pointer">View Data</summary>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </details>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PremiumWalletTest;
