        import React from 'react';
import { Link } from 'react-router-dom';
import { Star, ShoppingCart, Crown, Eye } from 'lucide-react';
import { Product } from '../../types';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import { getSafeImageUrl, handleImageError, getOptimizedImageUrl } from '../../utils/imageUtils';
import toast from 'react-hot-toast';

interface ProductCardProps {
  product: Product;
  fallbackImageSrc?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, fallbackImageSrc }) => {
  const { addItem } = useCartStore();
  const { user, isAuthenticated } = useAuthStore();

  const imageUrl = getSafeImageUrl(product.image_url, fallbackImageSrc);
  const optimizedImageUrl = getOptimizedImageUrl(imageUrl, 400, 300);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      toast.error('Please login to add items to cart');
      return;
    }

    if (product.is_premium_only && !user?.is_premium) {
      toast.error('This product is only available for Premium members');
      return;
    }

    if (!product.stock_quantity) {
      toast.error('Product is out of stock');
      return;
    }

    addItem(product);
    toast.success('Added to cart!');
  };

  const canPurchase = isAuthenticated && (!product.is_premium_only || user?.is_premium);

  return (
    <div className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
      <div className="relative">
        <img
          src={optimizedImageUrl}
          alt={product.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => handleImageError(e, fallbackImageSrc)}
        />
        {product.is_premium_only && (
          <div className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded-full flex items-center text-xs font-medium">
            <Crown className="h-3 w-3 mr-1" />
            Premium
          </div>
        )}
        {!product.stock_quantity && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <span className="text-white font-semibold">Out of Stock</span>
          </div>
        )}
        {product.original_price && product.original_price > product.price && (
          <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            {Math.round((1 - product.price / product.original_price) * 100)}% OFF
          </div>
        )}
      </div>

      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors">
          {product.name}
        </h3>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {product.description}
        </p>

        <div className="flex items-center mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < Math.floor(product.rating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600 ml-2">
            ({product.reviews_count})
          </span>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-xl font-bold text-green-600">
              ₹{product.price}
            </span>
            {product.original_price && product.original_price > product.price && (
              <span className="text-sm text-gray-500 line-through">
                ₹{product.original_price}
              </span>
            )}
          </div>
        </div>

        <div className="flex space-x-2">
          <Link
            to={`/products/${product.id}`}
            className="flex-1 bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors text-center flex items-center justify-center"
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Link>
          <button
            onClick={handleAddToCart}
            disabled={!canPurchase || !product.stock_quantity}
            className={`flex-1 py-2 rounded-lg transition-colors text-center flex items-center justify-center ${
              canPurchase && product.stock_quantity
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            Add to Cart
          </button>
        </div>

        {product.is_premium_only && !user?.is_premium && (
          <p className="text-xs text-amber-600 mt-2 text-center">
            Premium membership required
          </p>
        )}
      </div>
    </div>
  );
};