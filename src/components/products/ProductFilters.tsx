import React from 'react';
import { Filter, X } from 'lucide-react';
import { useProductStore } from '../../stores/productStore';

interface ProductFiltersProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const ProductFilters: React.FC<ProductFiltersProps> = ({ isOpen, onToggle }) => {
  const { 
    filters, 
    categories, 
    setFilters, 
    sortBy, 
    setSortBy 
  } = useProductStore();

  const clearFilters = () => {
    setFilters({
      category: '',
      subcategory: '',
      priceRange: [0, 1000],
      rating: 0,
      inStock: false,
      isPremiumOnly: false
    });
  };

  return (
    <>
      {/* Mobile Filter Toggle */}
      <button
        onClick={onToggle}
        className="md:hidden flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
      >
        <Filter className="h-4 w-4" />
        <span>Filters</span>
      </button>

      {/* Filter Panel */}
      <div className={`${
        isOpen ? 'block' : 'hidden'
      } md:block bg-white p-6 rounded-lg shadow-sm border border-gray-200`}>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
          <div className="flex space-x-2">
            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear All
            </button>
            <button
              onClick={onToggle}
              className="md:hidden text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="space-y-6">
          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sort By
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="name">Name (A-Z)</option>
              <option value="price-low">Price (Low to High)</option>
              <option value="price-high">Price (High to Low)</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest First</option>
            </select>
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              value={filters.category}
              onChange={(e) => setFilters({ category: e.target.value, subcategory: '' })}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Subcategory */}
          {filters.category && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subcategory
              </label>
              <select
                value={filters.subcategory}
                onChange={(e) => setFilters({ subcategory: e.target.value })}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="">All Subcategories</option>
                {categories
                  .find(cat => cat.name === filters.category)
                  ?.subcategories.map((subcategory) => (
                    <option key={subcategory.id} value={subcategory.name}>
                      {subcategory.name}
                    </option>
                  ))}
              </select>
            </div>
          )}

          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price Range
            </label>
            <div className="space-y-2">
              <input
                type="range"
                min="0"
                max="1000"
                value={filters.priceRange[1]}
                onChange={(e) => setFilters({ 
                  priceRange: [filters.priceRange[0], parseInt(e.target.value)] 
                })}
                className="w-full accent-green-600"
              />
              <div className="flex justify-between text-sm text-gray-600">
                <span>₹{filters.priceRange[0]}</span>
                <span>₹{filters.priceRange[1]}</span>
              </div>
            </div>
          </div>

          {/* Rating */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Minimum Rating
            </label>
            <div className="space-y-2">
              {[4, 3, 2, 1].map((rating) => (
                <label key={rating} className="flex items-center">
                  <input
                    type="radio"
                    name="rating"
                    value={rating}
                    checked={filters.rating === rating}
                    onChange={(e) => setFilters({ rating: parseInt(e.target.value) })}
                    className="mr-2 accent-green-600"
                  />
                  <span className="text-sm">{rating}+ Stars</span>
                </label>
              ))}
              <label className="flex items-center">
                <input
                  type="radio"
                  name="rating"
                  value={0}
                  checked={filters.rating === 0}
                  onChange={(e) => setFilters({ rating: parseInt(e.target.value) })}
                  className="mr-2 accent-green-600"
                />
                <span className="text-sm">All Ratings</span>
              </label>
            </div>
          </div>

          {/* Availability */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={filters.inStock}
                onChange={(e) => setFilters({ inStock: e.target.checked })}
                className="mr-2 accent-green-600"
              />
              <span className="text-sm">In Stock Only</span>
            </label>
          </div>

          {/* Premium Only */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={filters.isPremiumOnly}
                onChange={(e) => setFilters({ isPremiumOnly: e.target.checked })}
                className="mr-2 accent-green-600"
              />
              <span className="text-sm">Premium Products Only</span>
            </label>
          </div>
        </div>
      </div>
    </>
  );
};