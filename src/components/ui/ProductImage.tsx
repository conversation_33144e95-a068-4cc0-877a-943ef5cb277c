import React, { useState } from 'react';
import { ShoppingBag, ImageIcon } from 'lucide-react';

interface ProductImageProps {
  src?: string;
  alt: string;
  className?: string;
  fallbackClassName?: string;
  size?: 'sm' | 'md' | 'lg';
}

const ProductImage: React.FC<ProductImageProps> = ({
  src,
  alt,
  className = '',
  fallbackClassName = '',
  size = 'md'
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  };

  const iconSizes = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  return (
    <div className={`${sizeClasses[size]} bg-gray-100 rounded-md flex items-center justify-center overflow-hidden relative ${className}`}>
      {src && !imageError ? (
        <>
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="animate-pulse">
                <ImageIcon className={`${iconSizes[size]} text-gray-400`} />
              </div>
            </div>
          )}
          <img
            src={src}
            alt={alt}
            className={`w-full h-full object-cover ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${fallbackClassName}`}>
          <ShoppingBag className={`${iconSizes[size]} text-gray-400`} />
        </div>
      )}
    </div>
  );
};

export default ProductImage;
