import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { mlmReferralService, MLMUser, MLM_CONFIG } from '../../services/mlmReferralService';
import { referralService } from '../../services/referralService';
import toast from 'react-hot-toast';

const MLMReferralDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const [referralTree, setReferralTree] = useState<MLMUser | null>(null);
  const [referralCode, setReferralCode] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [generatingCode, setGeneratingCode] = useState(false);
  const [currentBonusAmount, setCurrentBonusAmount] = useState<number>(MLM_CONFIG.BONUS_AMOUNT);

  useEffect(() => {
    if (user) {
      loadReferralData();
    }
  }, [user]);

  const loadReferralData = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Get user's referral tree
      const tree = await mlmReferralService.getUserReferralTree(user.id);
      setReferralTree(tree);

      // Get current bonus amount from admin settings
      const bonusAmount = await mlmReferralService.getMLMBonusAmount();
      setCurrentBonusAmount(bonusAmount);

      // Get user's referral code
      if (user.referral_code) {
        setReferralCode(user.referral_code);
      }
    } catch (error) {
      console.error('Error loading referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      setLoading(false);
    }
  };

  const generateReferralCode = async () => {
    if (!user?.id) return;

    setGeneratingCode(true);
    try {
      const code = await referralService.generateReferralCode(user.id);
      if (code) {
        setReferralCode(code);
        toast.success('Referral code generated successfully!');
      } else {
        toast.error('Failed to generate referral code');
      }
    } catch (error) {
      console.error('Error generating referral code:', error);
      toast.error('Failed to generate referral code');
    } finally {
      setGeneratingCode(false);
    }
  };

  const copyReferralCode = () => {
    if (referralCode) {
      navigator.clipboard.writeText(referralCode);
      toast.success('Referral code copied to clipboard!');
    }
  };

  const getReferralPositionInfo = (position: number) => {
    if (position <= MLM_CONFIG.DIRECT_REFERRAL_LIMIT) {
      return {
        recipient: 'You',
        color: 'text-green-600 bg-green-50',
        icon: '💰'
      };
    } else if (position === MLM_CONFIG.GRANDPARENT_REFERRAL_POSITION) {
      return {
        recipient: referralTree?.referrer_name || 'Your Referrer',
        color: 'text-blue-600 bg-blue-50',
        icon: '⬆️'
      };
    } else {
      return {
        recipient: 'You',
        color: 'text-green-600 bg-green-50',
        icon: '🔄'
      };
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">🎯 MLM Referral System</h2>
        <p className="text-blue-100">
          Earn bonuses for your 1st & 2nd referrals. Your 3rd+ referrals benefit your referrer!
        </p>
      </div>

      {/* Referral Code Section */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Your Referral Code</h3>
        
        {referralCode ? (
          <div className="flex items-center gap-4">
            <div className="flex-1 p-3 bg-gray-50 rounded-lg font-mono text-lg">
              {referralCode}
            </div>
            <button
              onClick={copyReferralCode}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Copy
            </button>
          </div>
        ) : (
          <div className="text-center">
            <p className="text-gray-600 mb-4">You don't have a referral code yet.</p>
            <button
              onClick={generateReferralCode}
              disabled={generatingCode}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {generatingCode ? 'Generating...' : 'Generate Referral Code'}
            </button>
          </div>
        )}
      </div>

      {/* MLM Rules Explanation */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">🎯 How MLM Bonuses Work</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">💰</span>
              <h4 className="font-semibold text-green-800">1st & 2nd Referrals</h4>
            </div>
            <p className="text-green-700 text-sm">
              You receive ₹{currentBonusAmount} for each of your first 2 referrals when they become premium.
            </p>
          </div>

          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">⬆️</span>
              <h4 className="font-semibold text-blue-800">3rd Referral Only</h4>
            </div>
            <p className="text-blue-700 text-sm">
              Your 3rd referral generates ₹{currentBonusAmount} for your referrer (your upline).
            </p>
          </div>

          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">🔄</span>
              <h4 className="font-semibold text-green-800">4th+ Referrals</h4>
            </div>
            <p className="text-green-700 text-sm">
              Your 4th and subsequent referrals generate ₹{currentBonusAmount} for you again.
            </p>
          </div>
        </div>
      </div>

      {/* Current Referrals */}
      {referralTree && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4">Your Direct Referrals</h3>
          
          {referralTree.direct_referrals.length === 0 ? (
            <p className="text-gray-600 text-center py-8">
              No referrals yet. Share your referral code to start earning!
            </p>
          ) : (
            <div className="space-y-3">
              {referralTree.direct_referrals.map((referral, index) => {
                const position = index + 1;
                const positionInfo = getReferralPositionInfo(position);
                
                return (
                  <div key={referral.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold">
                        {position}
                      </div>
                      <div>
                        <p className="font-medium">{referral.name}</p>
                        <p className="text-sm text-gray-600">{referral.email}</p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm ${positionInfo.color}`}>
                        <span>{positionInfo.icon}</span>
                        <span>Bonus to: {positionInfo.recipient}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {referral.is_premium ? '✅ Premium' : '⏳ Not Premium'}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}

      {/* Next Referral Preview */}
      {referralTree && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4">Next Referral Preview</h3>
          
          <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">🔮</span>
              <h4 className="font-semibold text-yellow-800">
                Your {referralTree.referral_count + 1}th Referral
              </h4>
            </div>
            
            {(() => {
              const nextPosition = referralTree.referral_count + 1;
              const nextPositionInfo = getReferralPositionInfo(nextPosition);
              
              return (
                <p className="text-yellow-700">
                  When your next referral becomes premium, the ₹{currentBonusAmount} bonus will go to:
                  <span className="font-semibold"> {nextPositionInfo.recipient}</span>
                  {nextPosition > MLM_CONFIG.DIRECT_REFERRAL_LIMIT && !referralTree.referrer_id && (
                    <span className="text-red-600"> (No referrer - bonus will be lost!)</span>
                  )}
                </p>
              );
            })()}
          </div>
        </div>
      )}

      {/* Statistics */}
      {referralTree && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="text-3xl font-bold text-blue-600">{referralTree.referral_count}</div>
            <div className="text-gray-600">Total Referrals</div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="text-3xl font-bold text-green-600">
              {referralTree.direct_referrals.filter(r => r.is_premium).length}
            </div>
            <div className="text-gray-600">Premium Referrals</div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="text-3xl font-bold text-purple-600">
              ₹{referralTree.wallet_balance.toFixed(2)}
            </div>
            <div className="text-gray-600">Wallet Balance</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MLMReferralDashboard;
