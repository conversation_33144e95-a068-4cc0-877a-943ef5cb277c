import React, { useState, useEffect } from 'react';
import { referralService } from '../../services/referralService';
import { useAuthStore } from '../../stores/authStore';

interface NetworkNode {
  referrer_id: string;
  referred_user_id: string;
  full_name: string;
  email: string;
  is_premium: boolean;
  wallet_balance: number;
  bonus_amount: number;
  created_at: string;
  level: number;
  path: string[];
}

interface ReferralNetworkVisualizationProps {
  userId?: string;
  maxLevels?: number;
}

export const ReferralNetworkVisualization: React.FC<ReferralNetworkVisualizationProps> = ({
  userId,
  maxLevels = 5
}) => {
  const { user } = useAuthStore();
  const [networkData, setNetworkData] = useState<NetworkNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const targetUserId = userId || user?.id;

  useEffect(() => {
    if (targetUserId) {
      loadNetworkData();
    }
  }, [targetUserId, maxLevels]);

  const loadNetworkData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await referralService.getReferralNetwork(targetUserId!, maxLevels);
      setNetworkData(data || []);
    } catch (err) {
      console.error('Error loading referral network:', err);
      setError('Failed to load referral network');
    } finally {
      setLoading(false);
    }
  };

  const groupByLevel = (nodes: NetworkNode[]) => {
    const grouped: { [level: number]: NetworkNode[] } = {};
    nodes.forEach(node => {
      if (!grouped[node.level]) {
        grouped[node.level] = [];
      }
      grouped[node.level].push(node);
    });
    return grouped;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading referral network...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button 
          onClick={loadNetworkData}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  if (networkData.length === 0) {
    return (
      <div className="text-center p-8 bg-gray-50 rounded-lg">
        <p className="text-gray-600">No referral network data available</p>
      </div>
    );
  }

  const groupedData = groupByLevel(networkData);
  const levels = Object.keys(groupedData).map(Number).sort();

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold mb-4">Referral Network Visualization</h3>
        
        {/* Network Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900">Total Referrals</h4>
            <p className="text-2xl font-bold text-blue-600">{networkData.length}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-900">Premium Referrals</h4>
            <p className="text-2xl font-bold text-green-600">
              {networkData.filter(n => n.is_premium).length}
            </p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-medium text-purple-900">Network Levels</h4>
            <p className="text-2xl font-bold text-purple-600">{levels.length}</p>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-medium text-yellow-900">Total Bonuses</h4>
            <p className="text-2xl font-bold text-yellow-600">
              {formatCurrency(networkData.reduce((sum, n) => sum + n.bonus_amount, 0))}
            </p>
          </div>
        </div>

        {/* Network Tree */}
        <div className="space-y-6">
          {levels.map(level => (
            <div key={level} className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold text-gray-900 mb-3">
                Level {level} ({groupedData[level].length} referrals)
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groupedData[level].map((node, index) => (
                  <div 
                    key={`${node.referred_user_id}-${index}`}
                    className={`p-4 rounded-lg border-2 ${
                      node.is_premium 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-gray-900">{node.full_name}</h5>
                      {node.is_premium && (
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          Premium
                        </span>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{node.email}</p>
                    
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Bonus:</span>
                        <span className="font-medium">{formatCurrency(node.bonus_amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Wallet:</span>
                        <span className="font-medium">{formatCurrency(node.wallet_balance)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Joined:</span>
                        <span className="font-medium">{formatDate(node.created_at)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 3rd Referral Rule Explanation */}
        <div className="mt-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
          <h4 className="font-semibold text-orange-900 mb-2">🎯 3rd Referral Special Rule</h4>
          <p className="text-orange-800 text-sm">
            When someone makes their <strong>3rd referral</strong> (ONLY the 3rd, not 6th or 9th),
            the bonus goes to their <strong>parent</strong> (the person who referred them), not to them.
            This creates a fair distribution system where bonuses flow up the referral chain.
          </p>
          <p className="text-orange-700 text-xs mt-2">
            ⚠️ Note: This rule applies ONLY to the 3rd referral. All other referrals (1st, 2nd, 4th, 5th, etc.) follow standard bonus distribution.
          </p>
        </div>

        {/* E-wallet Unlock Status */}
        <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <h4 className="font-semibold text-purple-900 mb-2">E-wallet Unlock Progress</h4>
          <p className="text-purple-800 text-sm">
            Premium referrals: {networkData.filter(n => n.is_premium).length} / 3 
            (E-wallet unlocks after 3 premium referrals)
          </p>
        </div>
      </div>
    </div>
  );
};

export default ReferralNetworkVisualization;
