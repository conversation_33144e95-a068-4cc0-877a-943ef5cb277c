import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import { supabase } from '../../lib/supabase';
import { toast } from 'react-hot-toast';
import { Users, TrendingUp, Gift, <PERSON><PERSON>, <PERSON><PERSON>, Share2, Eye, EyeOff, RefreshCw, BarChart3, Layers, Clock, CreditCard, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { multiLevelReferralService } from '../../services/multiLevelReferralService';

interface ReferralStats {
  totalReferrals: number;
  premiumReferrals: number;
  totalEarned: number;
  walletUnlocked: boolean;
  referralOrder: number;
  bonusAmounts: {
    standard: number;
    premium: number;
    walletUnlock: number;
  };
}

interface ReferralRecord {
  id: string;
  referred_user_name: string;
  referred_user_email: string;
  is_premium: boolean;
  bonus_amount: number;
  created_at: string;
  referral_order: number;
}

interface MultiLevelEarnings {
  total: number;
  byLevel: { [level: number]: number };
  transactions: Array<{
    amount: number;
    description: string;
    metadata?: any;
    created_at: string;
  }>;
}

interface WalletTransaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference_type: string;
  reference_id?: string;
  created_at: string;
  metadata?: any;
}

interface ActivityLog {
  id: string;
  type: 'referral' | 'wallet_credit' | 'wallet_debit' | 'multi_level' | 'bonus';
  title: string;
  description: string;
  amount?: number;
  created_at: string;
  metadata?: any;
}

export const UserReferralDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = useState<ReferralStats>({
    totalReferrals: 0,
    premiumReferrals: 0,
    totalEarned: 0,
    walletUnlocked: false,
    referralOrder: 0,
    bonusAmounts: { standard: 100, premium: 150, walletUnlock: 500 }
  });
  const [referrals, setReferrals] = useState<ReferralRecord[]>([]);
  const [multiLevelEarnings, setMultiLevelEarnings] = useState<MultiLevelEarnings>({
    total: 0,
    byLevel: {},
    transactions: []
  });
  const [activityLog, setActivityLog] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReferralCode, setShowReferralCode] = useState(true);

  useEffect(() => {
    if (user?.is_premium) {
      loadReferralData();
    }
  }, [user]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    if (!user?.is_premium) return;

    const interval = setInterval(() => {
      console.log('Auto-refreshing referral data...');
      loadReferralData();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [user?.is_premium]);

  // Listen for storage events to refresh when data changes in other tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'referral_data_updated') {
        console.log('Referral data updated in another tab, refreshing...');
        loadReferralData();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Add a refresh function that can be called externally
  const refreshData = async () => {
    if (user?.is_premium) {
      // Force refresh user data first
      const { refreshUser } = useAuthStore.getState();
      await refreshUser();
      await loadReferralData();
    }
  };

  const loadReferralData = async () => {
    try {
      setLoading(true);

      // Get bonus amounts from admin settings
      const { data: bonusSettings } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value')
        .in('setting_key', ['referral_bonus_amount', 'premium_referral_bonus_amount']);

      const bonusAmounts = {
        standard: 100,
        premium: 150,
        walletUnlock: 0
      };

      bonusSettings?.forEach(setting => {
        if (setting.setting_key === 'referral_bonus_amount') {
          bonusAmounts.standard = parseFloat(setting.setting_value);
        } else if (setting.setting_key === 'premium_referral_bonus_amount') {
          bonusAmounts.premium = parseFloat(setting.setting_value);
        }
      });

      // Import referral service
      const { referralService } = await import('../../services/referralService');

      // Get referral stats using the fixed service
      const referralStats = await referralService.getReferralStats(user?.id || '');

      // Get detailed multi-level referral statistics
      const detailedStats = await referralService.getReferralStatisticsDetailed(user?.id || '');

      // Get multi-level earnings
      const { data: multiLevelTransactions } = await supabase
        .from('wallet_transactions')
        .select('amount, description, metadata, created_at')
        .eq('user_id', user?.id)
        .eq('reference_type', 'multi_level_referral')
        .order('created_at', { ascending: false });

      // Process multi-level earnings
      const earningsByLevel: { [level: number]: number } = {};
      let totalMultiLevelEarnings = 0;

      multiLevelTransactions?.forEach(transaction => {
        const level = transaction.metadata?.referral_level;
        if (level) {
          earningsByLevel[level] = (earningsByLevel[level] || 0) + transaction.amount;
          totalMultiLevelEarnings += transaction.amount;
        }
      });

      setMultiLevelEarnings({
        total: totalMultiLevelEarnings,
        byLevel: earningsByLevel,
        transactions: multiLevelTransactions || []
      });

      // Get referral records
      const { data: referralData } = await supabase
        .from('referrals')
        .select(`
          id,
          bonus_amount,
          created_at,
          referral_order,
          is_premium_referral,
          referred_user:users!referrals_referred_user_id_fkey(full_name, email, is_premium)
        `)
        .eq('referrer_id', user?.id)
        .order('created_at', { ascending: false });

      // Use the corrected stats from referralService
      const totalReferrals = referralStats.totalReferrals;
      const premiumReferrals = referralStats.premiumReferrals;
      const totalEarned = referralStats.totalEarnings;

      // Format referral records
      const formattedReferrals: ReferralRecord[] = referralData?.map(ref => ({
        id: ref.id,
        referred_user_name: ref.referred_user?.full_name || 'Unknown',
        referred_user_email: ref.referred_user?.email || 'Unknown',
        is_premium: ref.referred_user?.is_premium || false,
        bonus_amount: ref.bonus_amount,
        created_at: ref.created_at,
        referral_order: ref.referral_order
      })) || [];

      setStats({
        totalReferrals,
        premiumReferrals,
        totalEarned,
        walletUnlocked: referralStats.ewalletUnlocked,
        referralOrder: totalReferrals,
        bonusAmounts
      });

      console.log('UserReferralDashboard loaded stats:', {
        totalReferrals,
        premiumReferrals,
        totalEarned,
        walletUnlocked: referralStats.ewalletUnlocked
      });

      setReferrals(formattedReferrals);

      // Load wallet transactions for activity log
      const { data: walletTransactions } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(20);

      // Create combined activity log
      const activities: ActivityLog[] = [];

      // Add referral activities
      formattedReferrals.forEach(referral => {
        activities.push({
          id: `referral-${referral.id}`,
          type: 'referral',
          title: `New Referral: ${referral.referred_user_name}`,
          description: `${referral.referred_user_name} joined using your referral code`,
          amount: referral.bonus_amount,
          created_at: referral.created_at,
          metadata: {
            email: referral.referred_user_email,
            is_premium: referral.is_premium,
            order: referral.referral_order
          }
        });
      });

      // Add wallet transaction activities
      walletTransactions?.forEach(transaction => {
        let activityType: ActivityLog['type'] = 'wallet_credit';
        let title = '';
        let description = transaction.description;

        if (transaction.reference_type === 'referral') {
          activityType = 'referral';
          title = `Referral Bonus`;
        } else if (transaction.reference_type === 'multi_level_referral') {
          activityType = 'multi_level';
          title = `Multi-Level Bonus`;
        } else if (transaction.type === 'credit') {
          activityType = 'wallet_credit';
          title = `Wallet Credit`;
        } else {
          activityType = 'wallet_debit';
          title = `Wallet Debit`;
        }

        activities.push({
          id: `wallet-${transaction.id}`,
          type: activityType,
          title,
          description,
          amount: transaction.type === 'credit' ? transaction.amount : -transaction.amount,
          created_at: transaction.created_at,
          metadata: transaction.metadata
        });
      });

      // Sort activities by date (newest first)
      activities.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      setActivityLog(activities);

    } catch (error) {
      console.error('Error loading referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyReferralCode = () => {
    if (user?.referral_code) {
      navigator.clipboard.writeText(user.referral_code);
      toast.success('Referral code copied to clipboard!');
    }
  };

  const handleShareReferralCode = () => {
    if (user?.referral_code) {
      const shareText = `Join Start Juicce with my referral code ${user.referral_code} and help me earn referral bonuses! 🌿`;
      const shareUrl = `${window.location.origin}/register?ref=${user.referral_code}`;
      
      if (navigator.share) {
        navigator.share({
          title: 'Join Start Juicce',
          text: shareText,
          url: shareUrl
        });
      } else {
        navigator.clipboard.writeText(`${shareText}\n${shareUrl}`);
        toast.success('Referral link copied to clipboard!');
      }
    }
  };

  const formatCurrency = (amount: number) => `₹${amount.toFixed(2)}`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-IN');

  if (!user?.is_premium) {
    return (
      <div className="text-center py-12">
        <div className="relative">
          <div className="filter blur-sm">
            <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
            </div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white p-6 rounded-lg shadow-lg border-2 border-blue-500">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">🔒 Premium Feature</h2>
              <p className="text-gray-600 mb-6">
                Referral system is only available for premium members.
              </p>
              <button
                onClick={() => window.location.href = '/dashboard/premium'}
                className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-lg font-medium hover:from-yellow-500 hover:to-yellow-700 transition-all"
              >
                Upgrade to Premium
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading referral data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Referral Dashboard</h1>
            <p className="opacity-90">Track your referrals and earnings</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => {
                console.log('Current stats:', stats);
                console.log('Current user:', user);
                alert(`Stats: ${stats.totalReferrals} total, ${stats.premiumReferrals} premium`);
              }}
              className="p-3 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
              title="Debug data"
            >
              🐛
            </button>
            <button
              onClick={refreshData}
              className="p-3 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Referrals</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalReferrals}</p>
            </div>
            <Users className="h-12 w-12 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Premium Referrals</p>
              <p className="text-3xl font-bold text-purple-600">{stats.premiumReferrals}</p>
            </div>
            <Gift className="h-12 w-12 text-purple-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Earned</p>
              <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.totalEarned)}</p>
              {multiLevelEarnings.total > 0 && (
                <p className="text-xs text-blue-600 mt-1">
                  ₹{multiLevelEarnings.total.toFixed(2)} from multi-level
                </p>
              )}
            </div>
            <TrendingUp className="h-12 w-12 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">E-wallet Status</p>
              <p className="text-lg font-bold text-green-600">
                Unlocked
              </p>
            </div>
            <Unlock className="h-12 w-12 text-green-500" />
          </div>
        </div>
      </div>

      {/* Recent Activity Summary */}
      {activityLog.length > 0 && (
        <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Recent Activity Summary</h3>
                <p className="text-sm text-gray-600">Last 7 days</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {activityLog.filter(a =>
                  new Date(a.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) &&
                  a.type === 'referral'
                ).length}
              </p>
              <p className="text-sm text-gray-600">New Referrals</p>
            </div>

            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">
                {activityLog.filter(a =>
                  new Date(a.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) &&
                  a.type === 'multi_level'
                ).length}
              </p>
              <p className="text-sm text-gray-600">Multi-Level Bonuses</p>
            </div>

            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                ₹{activityLog.filter(a =>
                  new Date(a.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) &&
                  a.amount && a.amount > 0
                ).reduce((sum, a) => sum + (a.amount || 0), 0).toFixed(2)}
              </p>
              <p className="text-sm text-gray-600">Total Earned</p>
            </div>

            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {activityLog.filter(a =>
                  new Date(a.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
                ).length}
              </p>
              <p className="text-sm text-gray-600">Last 24h Activities</p>
            </div>
          </div>
        </div>
      )}

      {/* Multi-Level Earnings Breakdown */}
      {multiLevelEarnings.total > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-3 mb-6">
            <Layers className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Multi-Level Earnings</h2>
            <span className="text-sm text-gray-500">
              ₹{multiLevelEarnings.total.toFixed(2)} total
            </span>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-3 mb-6">
            {Object.entries(multiLevelEarnings.byLevel)
              .sort(([a], [b]) => parseInt(a) - parseInt(b))
              .map(([level, amount]) => (
                <div key={level} className="text-center p-3 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border">
                  <div className="text-xs font-medium text-gray-600 mb-1">Level {level}</div>
                  <div className="text-sm font-bold text-blue-900">₹{amount.toFixed(2)}</div>
                  <div className="text-xs text-gray-500">
                    {((amount / multiLevelEarnings.total) * 100).toFixed(0)}%
                  </div>
                </div>
              ))}
          </div>

          <div className="border-t pt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Multi-Level Transactions</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {multiLevelEarnings.transactions.slice(0, 5).map((transaction, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{transaction.description}</p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.created_at)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-green-600">+₹{transaction.amount.toFixed(2)}</p>
                    {transaction.metadata?.referral_level && (
                      <p className="text-xs text-blue-600">Level {transaction.metadata.referral_level}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Referral Code Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Your Referral Code</h2>
          <button
            onClick={() => setShowReferralCode(!showReferralCode)}
            className="p-2 text-gray-500 hover:text-gray-700"
          >
            {showReferralCode ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
          </button>
        </div>

        {user?.referral_code ? (
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="flex-1 bg-gray-50 p-4 rounded-lg border-2 border-dashed border-gray-300">
                <p className="text-2xl font-bold text-center text-gray-900 tracking-wider">
                  {showReferralCode ? user.referral_code : '••••••••'}
                </p>
              </div>
              <button
                onClick={handleCopyReferralCode}
                className="p-3 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
                title="Copy code"
              >
                <Copy className="h-5 w-5" />
              </button>
              <button
                onClick={handleShareReferralCode}
                className="p-3 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors"
                title="Share code"
              >
                <Share2 className="h-5 w-5" />
              </button>
            </div>

            {/* Bonus Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-800 mb-1">Standard Referral</h3>
                <p className="text-2xl font-bold text-blue-900">{formatCurrency(stats.bonusAmounts.standard)}</p>
                <p className="text-sm text-blue-600">Per referral</p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-medium text-purple-800 mb-1">Premium Bonus</h3>
                <p className="text-2xl font-bold text-purple-900">{formatCurrency(stats.bonusAmounts.premium)}</p>
                <p className="text-sm text-purple-600">When they upgrade</p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-800 mb-1">E-wallet Status</h3>
                <p className="text-2xl font-bold text-green-900">Unlocked</p>
                <p className="text-sm text-green-600">Automatic for premium users</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">Generate your unique referral code to start earning!</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
            >
              Generate Referral Code
            </button>
          </div>
        )}
      </div>

      {/* E-wallet Status Info */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="font-semibold text-green-900 mb-3">✅ E-wallet Status</h3>
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <p className="text-green-800 mb-2">
              Your e-wallet is automatically unlocked as a premium member!
            </p>
            <p className="text-sm text-green-600">
              You can now access all wallet features including transactions, withdrawals, and referral bonuses.
            </p>
          </div>
          <div className="text-right">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <Unlock className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Referrals */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Recent Referrals</h2>
        </div>

        <div className="divide-y">
          {referrals.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No referrals yet. Start sharing your referral code!
            </div>
          ) : (
            referrals.map((referral) => (
              <div key={referral.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      referral.is_premium ? 'bg-purple-100' : 'bg-blue-100'
                    }`}>
                      <Users className={`h-5 w-5 ${
                        referral.is_premium ? 'text-purple-600' : 'text-blue-600'
                      }`} />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{referral.referred_user_name}</p>
                      <p className="text-sm text-gray-500">{formatDate(referral.created_at)}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          referral.is_premium 
                            ? 'bg-purple-100 text-purple-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {referral.is_premium ? '⭐ Premium' : '👤 Standard'}
                        </span>
                        {referral.referral_order === 3 && (
                          <span className="text-xs px-2 py-1 rounded-full bg-orange-100 text-orange-800">
                            🎯 3rd Referral
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      +{formatCurrency(referral.bonus_amount)}
                    </p>
                    <p className="text-xs text-gray-500">
                      #{referral.referral_order}
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Recent Activity Log */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
            </div>
            <span className="text-sm text-gray-500">
              {activityLog.length} activities
            </span>
          </div>
        </div>

        <div className="divide-y max-h-96 overflow-y-auto">
          {activityLog.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Clock className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p>No recent activity</p>
              <p className="text-sm">Start referring friends to see activity here!</p>
            </div>
          ) : (
            activityLog.map((activity) => (
              <div key={activity.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                      activity.type === 'referral' ? 'bg-blue-100' :
                      activity.type === 'multi_level' ? 'bg-purple-100' :
                      activity.type === 'wallet_credit' ? 'bg-green-100' :
                      activity.type === 'wallet_debit' ? 'bg-red-100' :
                      'bg-gray-100'
                    }`}>
                      {activity.type === 'referral' && <Users className="h-5 w-5 text-blue-600" />}
                      {activity.type === 'multi_level' && <Layers className="h-5 w-5 text-purple-600" />}
                      {activity.type === 'wallet_credit' && <ArrowUpRight className="h-5 w-5 text-green-600" />}
                      {activity.type === 'wallet_debit' && <ArrowDownRight className="h-5 w-5 text-red-600" />}
                      {activity.type === 'bonus' && <Gift className="h-5 w-5 text-yellow-600" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 truncate">{activity.title}</p>
                      <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <p className="text-xs text-gray-500">
                          {new Date(activity.created_at).toLocaleDateString('en-IN', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                        {activity.metadata?.is_premium && (
                          <span className="text-xs px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                            ⭐ Premium
                          </span>
                        )}
                        {activity.metadata?.order === 3 && (
                          <span className="text-xs px-2 py-1 rounded-full bg-orange-100 text-orange-800">
                            🎯 3rd Referral
                          </span>
                        )}
                        {activity.metadata?.referral_level && (
                          <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                            Level {activity.metadata.referral_level}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right flex-shrink-0 ml-4">
                    {activity.amount && (
                      <p className={`font-semibold ${
                        activity.amount > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {activity.amount > 0 ? '+' : ''}{formatCurrency(Math.abs(activity.amount))}
                      </p>
                    )}
                    <div className="flex items-center gap-1 mt-1">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        activity.type === 'referral' ? 'bg-blue-100 text-blue-800' :
                        activity.type === 'multi_level' ? 'bg-purple-100 text-purple-800' :
                        activity.type === 'wallet_credit' ? 'bg-green-100 text-green-800' :
                        activity.type === 'wallet_debit' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {activity.type === 'referral' ? 'Referral' :
                         activity.type === 'multi_level' ? 'Multi-Level' :
                         activity.type === 'wallet_credit' ? 'Credit' :
                         activity.type === 'wallet_debit' ? 'Debit' :
                         'Other'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {activityLog.length > 0 && (
          <div className="p-4 bg-gray-50 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                Showing {Math.min(activityLog.length, 20)} most recent activities
              </span>
              <button
                onClick={refreshData}
                className="text-blue-600 hover:text-blue-800 font-medium flex items-center gap-1"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserReferralDashboard;
