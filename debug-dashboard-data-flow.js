// Debug Dashboard Data Flow - Find Why Wallet Not Updating
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Debug Dashboard Data Flow');
console.log('============================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugDashboardDataFlow() {
  try {
    console.log('1️⃣ CHECKING ALL USERS WITH REFERRAL CODE REFEASFCDB98');
    console.log('─'.repeat(50));
    
    // Find ALL users with this referral code (there might be duplicates)
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('*')
      .eq('referral_code', 'REFEASFCDB98');

    if (allUsersError) {
      console.log('❌ Error finding users:', allUsersError.message);
      return false;
    }

    console.log(`✅ Found ${allUsers?.length || 0} users with referral code REFEASFCDB98:`);
    
    if (allUsers && allUsers.length > 0) {
      allUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
        console.log(`      ID: ${user.id}`);
        console.log(`      Wallet: ₹${user.wallet_balance || 0}`);
        console.log(`      Premium: ${user.is_premium}`);
        console.log(`      Admin: ${user.is_admin}`);
        console.log(`      Created: ${user.created_at}`);
        console.log('');
      });
    }

    console.log('\n2️⃣ CHECKING USERS BY EMAIL');
    console.log('─'.repeat(50));
    
    const targetEmail = '<EMAIL>';
    
    const { data: userByEmail, error: emailError } = await supabase
      .from('users')
      .select('*')
      .eq('email', targetEmail)
      .single();

    if (emailError) {
      console.log('❌ User not found by email:', emailError.message);
    } else {
      console.log(`✅ User found by email ${targetEmail}:`);
      console.log(`   ID: ${userByEmail.id}`);
      console.log(`   Name: ${userByEmail.full_name}`);
      console.log(`   Wallet: ₹${userByEmail.wallet_balance || 0}`);
      console.log(`   Referral Code: ${userByEmail.referral_code}`);
      console.log(`   Premium Count: ${userByEmail.premium_referral_count || 0}`);
      console.log(`   Is Premium: ${userByEmail.is_premium}`);
      console.log(`   Created: ${userByEmail.created_at}`);
      console.log(`   Updated: ${userByEmail.updated_at || 'Not set'}`);
    }

    console.log('\n3️⃣ TESTING AUTHENTICATION FLOW');
    console.log('─'.repeat(50));
    
    // Test if we can authenticate as this user
    console.log('📝 Testing authentication with common passwords...');
    
    const commonPasswords = ['password', '123456', 'admin123', 'test123', userByEmail?.email];
    let authSuccess = false;
    
    for (const password of commonPasswords) {
      if (password === userByEmail?.email) continue; // Skip email as password
      
      console.log(`   Trying password: ${password}`);
      
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: targetEmail,
        password: password
      });

      if (authData?.user && !authError) {
        console.log(`   ✅ Authentication successful with password: ${password}`);
        console.log(`   User ID: ${authData.user.id}`);
        console.log(`   Email: ${authData.user.email}`);
        authSuccess = true;
        
        // Sign out immediately
        await supabase.auth.signOut();
        break;
      } else {
        console.log(`   ❌ Failed: ${authError?.message || 'Invalid credentials'}`);
      }
    }

    if (!authSuccess) {
      console.log('⚠️ Could not authenticate with common passwords');
      console.log('   You may need to reset the password or use the correct one');
    }

    console.log('\n4️⃣ CHECKING WALLET DATA CONSISTENCY');
    console.log('─'.repeat(50));
    
    if (userByEmail) {
      // Check if there are multiple records or data inconsistencies
      const { data: walletCheck, error: walletError } = await supabase
        .from('users')
        .select('id, email, wallet_balance, premium_referral_count')
        .eq('id', userByEmail.id);

      if (walletError) {
        console.log('❌ Wallet check error:', walletError.message);
      } else {
        console.log('✅ Wallet data consistency check:');
        walletCheck?.forEach((record, index) => {
          console.log(`   Record ${index + 1}: ₹${record.wallet_balance || 0}`);
        });
      }

      // Check for any wallet transactions
      const { data: transactions, error: transError } = await supabase
        .from('wallet_transactions')
        .select('*')
        .eq('user_id', userByEmail.id);

      if (transError) {
        console.log('❌ Transaction check error:', transError.message);
      } else {
        console.log(`✅ Wallet transactions: ${transactions?.length || 0}`);
        transactions?.forEach((trans, index) => {
          console.log(`   ${index + 1}. ${trans.type}: ₹${trans.amount} - ${trans.description}`);
        });
      }
    }

    console.log('\n5️⃣ MANUAL WALLET BALANCE FIX');
    console.log('─'.repeat(50));
    
    if (userByEmail && parseFloat(userByEmail.wallet_balance || '0') !== 250) {
      console.log('📝 Wallet balance is not ₹250, forcing update...');
      
      // Force update with raw SQL-like approach
      const { error: forceError } = await supabase
        .from('users')
        .update({ 
          wallet_balance: 250.00,
          premium_referral_count: 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', userByEmail.id);

      if (forceError) {
        console.log('❌ Force update failed:', forceError.message);
      } else {
        console.log('✅ Force update successful');
        
        // Verify immediately
        const { data: verifyUser, error: verifyError } = await supabase
          .from('users')
          .select('wallet_balance, premium_referral_count')
          .eq('id', userByEmail.id)
          .single();

        if (verifyError) {
          console.log('❌ Verification failed:', verifyError.message);
        } else {
          console.log(`✅ Verified wallet: ₹${verifyUser.wallet_balance}`);
          console.log(`✅ Verified referrals: ${verifyUser.premium_referral_count}`);
        }
      }
    } else {
      console.log('✅ Wallet balance is already ₹250');
    }

    console.log('\n6️⃣ FRONTEND DEBUGGING STEPS');
    console.log('─'.repeat(50));
    
    console.log('🔧 Try these frontend debugging steps:');
    console.log('');
    console.log('Step 1: Check browser developer tools');
    console.log('   • Press F12 → Network tab');
    console.log('   • Refresh dashboard page');
    console.log('   • Look for API calls to /users or /wallet');
    console.log('   • Check if the response shows ₹250');
    console.log('');
    console.log('Step 2: Check browser console');
    console.log('   • Press F12 → Console tab');
    console.log('   • Look for JavaScript errors');
    console.log('   • Check for authentication errors');
    console.log('');
    console.log('Step 3: Test with different user');
    console.log('   • Try logging in with a different account');
    console.log('   • See if wallet data loads correctly for other users');
    console.log('');
    console.log('Step 4: Check if you\'re logged in as the right user');
    console.log('   • Make sure you\'re logged <NAME_EMAIL>');
    console.log('   • Not as admin or another user');

    console.log('\n7️⃣ ALTERNATIVE SOLUTION');
    console.log('─'.repeat(50));
    
    console.log('💡 If dashboard still shows ₹0, try this:');
    console.log('');
    console.log('Option 1: Create a new test user');
    console.log('   • I can create a fresh user with ₹250 balance');
    console.log('   • Give you the login credentials');
    console.log('   • Test if the dashboard works with new user');
    console.log('');
    console.log('Option 2: Check admin panel');
    console.log('   • Login to admin panel');
    console.log('   • Go to Users section');
    console.log('   • <NAME_EMAIL>');
    console.log('   • Verify the wallet balance shows ₹250 there');
    console.log('');
    console.log('Option 3: Reset and recreate');
    console.log('   • Reset the user account completely');
    console.log('   • Recreate with proper referral bonus');
    console.log('   • Test the complete flow');

    console.log('\n🎉 DEBUG COMPLETE');
    console.log('═'.repeat(50));
    console.log('📊 Summary:');
    console.log(`   Users with REFEASFCDB98: ${allUsers?.length || 0}`);
    console.log(`   Target user found: ${userByEmail ? '✅' : '❌'}`);
    console.log(`   Current wallet: ₹${userByEmail?.wallet_balance || 0}`);
    console.log(`   Authentication tested: ${authSuccess ? '✅' : '❌'}`);
    console.log('');
    console.log('🔗 Next steps:');
    console.log('   1. Try the frontend debugging steps above');
    console.log('   2. Check admin panel to verify ₹250 is there');
    console.log('   3. Let me know what you find in browser dev tools');

    return true;

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    return false;
  }
}

debugDashboardDataFlow().then(success => {
  if (success) {
    console.log('\n🚀 Debug completed!');
    console.log('💡 Check the frontend debugging steps above!');
  } else {
    console.log('\n⚠️ Debug failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
