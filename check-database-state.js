// Check Database State - Complete Verification
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Complete Database State Check');
console.log('================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkDatabaseState() {
  try {
    console.log('1️⃣ CHECKING USER ACCOUNT');
    console.log('─'.repeat(40));
    
    const userEmail = '<EMAIL>';
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', userEmail)
      .single();

    if (userError) {
      console.log('❌ User not found:', userError.message);
      return false;
    }

    console.log('✅ User Account Details:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Name: ${user.full_name}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Wallet Balance: ₹${user.wallet_balance || 0}`);
    console.log(`   Premium Referral Count: ${user.premium_referral_count || 0}`);
    console.log(`   Is Premium: ${user.is_premium}`);
    console.log(`   Is Admin: ${user.is_admin}`);
    console.log(`   Referral Code: ${user.referral_code}`);
    console.log(`   Referred By: ${user.referred_by || 'None'}`);
    console.log(`   Created: ${user.created_at}`);
    console.log(`   Updated: ${user.updated_at || 'Not set'}`);

    console.log('\n2️⃣ CHECKING WALLET TRANSACTIONS');
    console.log('─'.repeat(40));

    const { data: transactions, error: transError } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (transError) {
      console.log('❌ Error getting transactions:', transError.message);
    } else {
      console.log(`✅ Found ${transactions?.length || 0} wallet transactions:`);
      
      if (transactions && transactions.length > 0) {
        transactions.forEach((trans, index) => {
          console.log(`   ${index + 1}. ${trans.type.toUpperCase()}: ₹${trans.amount}`);
          console.log(`      Description: ${trans.description}`);
          console.log(`      Reference: ${trans.reference_type} - ${trans.reference_id}`);
          console.log(`      Date: ${new Date(trans.created_at).toLocaleString()}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No wallet transactions found!');
        console.log('   This might explain why the balance is not showing correctly.');
      }
    }

    console.log('\n3️⃣ CHECKING REFERRALS TABLE');
    console.log('─'.repeat(40));

    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select(`
        *,
        referred_user:users!referrals_referred_user_id_fkey(
          id,
          email,
          full_name,
          is_premium,
          premium_purchased_at
        )
      `)
      .eq('referrer_id', user.id);

    if (refError) {
      console.log('❌ Error getting referrals:', refError.message);
    } else {
      console.log(`✅ Found ${referrals?.length || 0} referrals:`);
      
      if (referrals && referrals.length > 0) {
        referrals.forEach((ref, index) => {
          const refUser = ref.referred_user;
          console.log(`   ${ref.referral_order}. ${refUser.full_name} (${refUser.email})`);
          console.log(`      Premium: ${refUser.is_premium ? '✅ Yes' : '❌ No'}`);
          console.log(`      Premium Date: ${refUser.premium_purchased_at || 'Not set'}`);
          console.log(`      Bonus Amount: ₹${ref.bonus_amount || 0}`);
          console.log(`      Status: ${ref.status}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No referrals found in referrals table!');
        console.log('   This confirms the referral relationship was not created properly.');
      }
    }

    console.log('\n4️⃣ CHECKING USERS WHO USED THIS REFERRAL CODE');
    console.log('─'.repeat(40));

    const { data: referredUsers, error: referredError } = await supabase
      .from('users')
      .select('*')
      .eq('referred_by', user.referral_code);

    if (referredError) {
      console.log('❌ Error getting referred users:', referredError.message);
    } else {
      console.log(`✅ Found ${referredUsers?.length || 0} users who used referral code ${user.referral_code}:`);
      
      if (referredUsers && referredUsers.length > 0) {
        referredUsers.forEach((refUser, index) => {
          console.log(`   ${index + 1}. ${refUser.full_name} (${refUser.email})`);
          console.log(`      Premium: ${refUser.is_premium ? '✅ Yes' : '❌ No'}`);
          console.log(`      Created: ${refUser.created_at}`);
          console.log(`      Premium Date: ${refUser.premium_purchased_at || 'Not set'}`);
          console.log('');
        });
      } else {
        console.log('   ⚠️ No users found who used this referral code!');
        console.log('   This means the referral signup process did not work correctly.');
      }
    }

    console.log('\n5️⃣ CHECKING MLM SYSTEM SETTINGS');
    console.log('─'.repeat(40));

    const { data: mlmSettings, error: mlmError } = await supabase
      .from('admin_settings')
      .select('*')
      .in('setting_key', ['mlm_system_enabled', 'mlm_bonus_amount']);

    if (mlmError) {
      console.log('❌ Error getting MLM settings:', mlmError.message);
    } else {
      console.log('✅ MLM System Settings:');
      mlmSettings?.forEach(setting => {
        console.log(`   ${setting.setting_key}: ${setting.setting_value}`);
        console.log(`   Description: ${setting.description}`);
      });
    }

    console.log('\n6️⃣ DIAGNOSIS AND RECOMMENDATIONS');
    console.log('─'.repeat(40));

    const walletBalance = parseFloat(user.wallet_balance || '0');
    const hasTransactions = transactions && transactions.length > 0;
    const hasReferrals = referrals && referrals.length > 0;
    const hasReferredUsers = referredUsers && referredUsers.length > 0;

    console.log('📊 DIAGNOSIS:');
    console.log(`   Wallet Balance: ₹${walletBalance} ${walletBalance > 0 ? '✅' : '❌'}`);
    console.log(`   Has Transactions: ${hasTransactions ? '✅ Yes' : '❌ No'}`);
    console.log(`   Has Referrals: ${hasReferrals ? '✅ Yes' : '❌ No'}`);
    console.log(`   Has Referred Users: ${hasReferredUsers ? '✅ Yes' : '❌ No'}`);

    console.log('\n💡 RECOMMENDATIONS:');
    
    if (walletBalance === 0 && !hasTransactions) {
      console.log('   🔧 Issue: Wallet balance is 0 and no transactions exist');
      console.log('   📝 Solution: Need to create wallet transaction for the bonus');
    }
    
    if (!hasReferrals && hasReferredUsers) {
      console.log('   🔧 Issue: Users used referral code but no referral records exist');
      console.log('   📝 Solution: Need to create referral relationships');
    }
    
    if (!hasReferredUsers) {
      console.log('   🔧 Issue: No users found who used the referral code');
      console.log('   📝 Solution: Need to verify the referral signup process');
    }

    console.log('\n🎉 DATABASE CHECK COMPLETE');
    console.log('═'.repeat(50));
    console.log('📋 Summary:');
    console.log(`   User exists: ✅`);
    console.log(`   Wallet balance: ₹${walletBalance}`);
    console.log(`   Referral count: ${user.premium_referral_count || 0}`);
    console.log(`   Transactions: ${transactions?.length || 0}`);
    console.log(`   Referrals: ${referrals?.length || 0}`);
    console.log(`   Referred users: ${referredUsers?.length || 0}`);

    return {
      user,
      walletBalance,
      hasTransactions,
      hasReferrals,
      hasReferredUsers,
      transactions,
      referrals,
      referredUsers
    };

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    return false;
  }
}

checkDatabaseState().then(result => {
  if (result) {
    console.log('\n🚀 Database check completed successfully!');
    console.log('📊 Use the diagnosis above to understand the current state.');
  } else {
    console.log('\n⚠️ Database check failed. Check the errors above.');
  }
  process.exit(result ? 0 : 1);
});
