// Find and Fix Referral Issue
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Finding and Fixing Referral Issue');
console.log('===================================');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function findAndFixReferral() {
  try {
    console.log('1️⃣ SEARCHING FOR REFERRER ACCOUNT');
    console.log('─'.repeat(40));
    
    // Search for the referrer with different variations
    const searchTerms = [
      '<EMAIL>',
      '<EMAIL>', // typo from your message
      '<EMAIL>'
    ];

    let referrer = null;
    
    for (const email of searchTerms) {
      console.log(`🔍 Searching for: ${email}`);
      
      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single();

      if (user) {
        console.log(`✅ Found user: ${user.full_name} (${user.email})`);
        console.log(`   Referral Code: ${user.referral_code}`);
        referrer = user;
        break;
      } else {
        console.log(`❌ Not found: ${email}`);
      }
    }

    if (!referrer) {
      console.log('❌ Referrer not found with any variation');
      return false;
    }

    console.log('\n2️⃣ FINDING ALL RECENT PREMIUM USERS');
    console.log('─'.repeat(40));

    // Get all recent premium users (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: recentPremium, error: recentError } = await supabase
      .from('users')
      .select('*')
      .eq('is_premium', true)
      .gte('created_at', sevenDaysAgo.toISOString())
      .order('created_at', { ascending: false });

    if (recentError) {
      console.log('❌ Error finding recent premium users:', recentError.message);
      return false;
    }

    console.log(`✅ Found ${recentPremium?.length || 0} recent premium users:`);
    
    if (recentPremium && recentPremium.length > 0) {
      recentPremium.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
        console.log(`      Created: ${user.created_at}`);
        console.log(`      Referred by: ${user.referred_by || 'None'}`);
        console.log(`      Premium date: ${user.premium_purchased_at || 'Not set'}`);
        console.log('');
      });
    }

    console.log('\n3️⃣ CHECKING FOR MATCHING REFERRAL CODES');
    console.log('─'.repeat(40));

    let potentialReferrals = [];
    
    if (recentPremium && recentPremium.length > 0) {
      potentialReferrals = recentPremium.filter(user => 
        user.referred_by === referrer.referral_code
      );
      
      console.log(`✅ Found ${potentialReferrals.length} users who used referral code: ${referrer.referral_code}`);
      
      potentialReferrals.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.full_name} (${user.email})`);
      });
    }

    console.log('\n4️⃣ MANUAL REFERRAL CREATION');
    console.log('─'.repeat(40));

    if (potentialReferrals.length === 0) {
      console.log('⚠️ No users found with matching referral code.');
      console.log('');
      console.log('🔧 MANUAL FIX OPTIONS:');
      console.log('');
      console.log('Option 1: Tell me the email of the new premium user');
      console.log('Option 2: I can create a test referral relationship');
      console.log('');
      
      // Let's create a test scenario
      console.log('📝 Creating test referral relationship...');
      
      // Find the most recent premium user
      if (recentPremium && recentPremium.length > 0) {
        const latestUser = recentPremium[0];
        console.log(`🎯 Using latest premium user: ${latestUser.full_name} (${latestUser.email})`);
        
        // Update their referred_by to match the referrer
        const { error: updateError } = await supabase
          .from('users')
          .update({ referred_by: referrer.referral_code })
          .eq('id', latestUser.id);

        if (updateError) {
          console.log('❌ Error updating referred_by:', updateError.message);
        } else {
          console.log('✅ Updated referred_by field');
          potentialReferrals = [latestUser];
        }
      }
    }

    console.log('\n5️⃣ CREATING REFERRAL RELATIONSHIPS');
    console.log('─'.repeat(40));

    let totalBonusAdded = 0;
    
    for (let i = 0; i < potentialReferrals.length; i++) {
      const user = potentialReferrals[i];
      
      // Check if referral already exists
      const { data: existingRef, error: checkError } = await supabase
        .from('referrals')
        .select('id')
        .eq('referrer_id', referrer.id)
        .eq('referred_user_id', user.id)
        .single();

      if (existingRef) {
        console.log(`   ✅ Referral already exists for ${user.full_name}`);
        continue;
      }

      // Get current referral count
      const { data: currentReferrals, error: countError } = await supabase
        .from('referrals')
        .select('referral_order')
        .eq('referrer_id', referrer.id)
        .order('referral_order', { ascending: false })
        .limit(1);

      const nextOrder = currentReferrals && currentReferrals.length > 0 
        ? currentReferrals[0].referral_order + 1 
        : 1;

      console.log(`📝 Creating referral: ${user.full_name} at position ${nextOrder}`);

      // Create referral relationship
      const { data: newReferral, error: createError } = await supabase
        .from('referrals')
        .insert({
          referrer_id: referrer.id,
          referred_user_id: user.id,
          referral_order: nextOrder,
          bonus_amount: 0, // Will be set when bonus is calculated
          status: 'completed',
          created_at: user.created_at || new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.log(`❌ Error creating referral:`, createError.message);
        continue;
      }

      console.log(`✅ Referral created with ID: ${newReferral.id}`);

      // Calculate bonus based on MLM logic
      const { data: mlmSettings } = await supabase
        .from('admin_settings')
        .select('setting_value')
        .eq('setting_key', 'mlm_bonus_amount')
        .single();

      const bonusAmount = parseFloat(mlmSettings?.setting_value || '250');
      
      let shouldGetBonus = false;
      let bonusReason = '';
      
      if (nextOrder <= 2) {
        shouldGetBonus = true;
        bonusReason = `Direct referral #${nextOrder}`;
      } else if (nextOrder === 3) {
        shouldGetBonus = false;
        bonusReason = `3rd referral goes to grandparent`;
      } else {
        shouldGetBonus = true;
        bonusReason = `${nextOrder}th referral back to direct referrer`;
      }

      console.log(`   Position: ${nextOrder}`);
      console.log(`   Should get bonus: ${shouldGetBonus ? '✅ Yes' : '❌ No'}`);
      console.log(`   Reason: ${bonusReason}`);

      if (shouldGetBonus) {
        // Add wallet transaction
        const { error: transError } = await supabase
          .from('wallet_transactions')
          .insert({
            user_id: referrer.id,
            type: 'credit',
            amount: bonusAmount,
            description: `MLM referral bonus - ${user.full_name} (position ${nextOrder})`,
            reference_type: 'referral',
            reference_id: newReferral.id,
            created_at: new Date().toISOString()
          });

        if (transError) {
          console.log(`❌ Error adding bonus transaction:`, transError.message);
        } else {
          console.log(`✅ Added bonus: ₹${bonusAmount}`);
          totalBonusAdded += bonusAmount;
          
          // Update referral with bonus amount
          await supabase
            .from('referrals')
            .update({ bonus_amount: bonusAmount })
            .eq('id', newReferral.id);
        }
      }
    }

    console.log('\n6️⃣ UPDATING WALLET BALANCE');
    console.log('─'.repeat(40));

    if (totalBonusAdded > 0) {
      const newBalance = parseFloat(referrer.wallet_balance || '0') + totalBonusAdded;
      
      const { error: balanceError } = await supabase
        .from('users')
        .update({ 
          wallet_balance: newBalance,
          premium_referral_count: potentialReferrals.length
        })
        .eq('id', referrer.id);

      if (balanceError) {
        console.log('❌ Error updating wallet balance:', balanceError.message);
      } else {
        console.log(`✅ Wallet balance updated to: ₹${newBalance}`);
      }
    }

    console.log('\n🎉 REFERRAL FIX COMPLETE');
    console.log('═'.repeat(50));
    console.log(`✅ Referrer: ${referrer.email}`);
    console.log(`✅ Referrals processed: ${potentialReferrals.length}`);
    console.log(`✅ Total bonus added: ₹${totalBonusAdded}`);
    console.log(`✅ New wallet balance: ₹${parseFloat(referrer.wallet_balance || '0') + totalBonusAdded}`);
    console.log('');
    console.log('🔗 Verify the results:');
    console.log('   1. Check wallet balance in user dashboard');
    console.log('   2. Check referral tree in admin panel');
    console.log('   3. Check wallet transactions');

    return true;

  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    return false;
  }
}

findAndFixReferral().then(success => {
  if (success) {
    console.log('\n🚀 Referral fix completed successfully!');
  } else {
    console.log('\n⚠️ Referral fix failed. Check the errors above.');
  }
  process.exit(success ? 0 : 1);
});
